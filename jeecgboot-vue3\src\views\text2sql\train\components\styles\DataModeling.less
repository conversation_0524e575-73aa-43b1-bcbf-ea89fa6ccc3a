/* 独立使用时的CSS变量和基础样式 */
.data-modeling-standalone {
  /* CSS 变量定义 */
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --font-size-base: 14px;
  --heading-color: rgba(0, 0, 0, 0.85);
  --text-color: rgba(0, 0, 0, 0.65);
  --text-color-secondary: rgba(0, 0, 0, 0.45);
  --disabled-color: rgba(0, 0, 0, 0.25);
  --border-radius-base: 4px;
  --border-color-base: #d9d9d9;
  --box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
  --background-color-light: #f5f5f5;
  --background-color-base: #f0f2f5;

  /* 基础样式 */
  width: 100%;
  height: calc(100vh - 110px); // 减去顶部导航栏高度
  min-height: 500px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  font-size: var(--font-size-base);
  color: var(--text-color);
  background: var(--background-color-base);
  box-sizing: border-box;
  overflow: hidden;
  overscroll-behavior: none;
  touch-action: none;

  /* 确保全屏功能正常工作 */
  position: relative;
  z-index: 1;

  /* 全屏状态样式 */
  &.fullscreen-root {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    background: var(--background-color-base) !important;
  }

  /* 重置一些可能的全局样式影响 */
  * {
    box-sizing: border-box;
  }

  /* 确保滚动条样式一致 */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

/* 全局拖拽状态优化 */
body.canvas-panning {
  /* 拖拽时禁用大部分过渡效果以提高性能 */
  .table-node,
  .floating-toolbar,
  .canvas-content {
    transition: none !important;
    animation: none !important;
  }
  
  /* 优化拖拽时的用户体验 */
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  
  /* 禁用文本选择指针事件 */
  .ant-typography {
    pointer-events: none;
  }
  
  /* 确保拖拽流畅性 */
  .data-modeling-standalone {
    will-change: auto;
  }
  
  /* 关系线保持可见性但禁用交互 */
  .relationships-layer {
    .relationship-click-area {
      pointer-events: none !important;
    }
  }
}

/* 禁用但可点击的按钮样式 */
.disabled-but-clickable {
  opacity: 0.6 !important;
  cursor: pointer !important;

  &:hover {
    opacity: 0.8 !important;
  }

  &:active {
    opacity: 0.5 !important;
  }
}

/* 暗黑模式适配 */
[data-theme='dark'] {
  .data-modeling-container {
    background: #141414;
  }

  .connection-card,
  .tables-panel {
    background: #1f1f1f;
    border-color: #303030;
  }

  .connection-section h3 {
    color: #1890ff;
  }

  .connection-section p {
    color: #a6a6a6;
  }

  .table-name {
    color: #ffffff;
  }

  .table-description {
    color: #8c8c8c;
  }

  .columns-count {
    color: #737373;
  }

  .modeling-canvas {
    background: #1f1f1f;
    border-color: #303030;
  }

  .canvas-content {
    background-image: radial-gradient(circle, #404040 1px, transparent 1px);
  }

  .floating-toolbar {
    background: rgba(31, 31, 31, 0.95);
    border-color: rgba(48, 48, 48, 0.2);
  }

  .table-node {
    background: #262626;
    border-color: #404040;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .table-header {
    background: linear-gradient(135deg, #1f1f1f 0%, #262626 100%);
    border-bottom-color: #404040;
  }

  .table-title {
    color: #ffffff;
  }

  .table-actions .ant-btn {
    border-color: #404040;
    color: #a6a6a6;
  }

  .table-actions .ant-btn:hover {
    border-color: #1890ff;
    color: #1890ff;
  }

  .column-item {
    border-bottom-color: #303030;
  }

  .column-name {
    color: #ffffff;
  }

  .column-type {
    color: #a6a6a6;
  }

  .column-constraints {
    color: #8c8c8c;
  }

  .canvas-tips {
    background: rgba(31, 31, 31, 0.95);
    color: #a6a6a6;
    border-color: rgba(64, 64, 64, 0.3);
  }

  .tip-text {
    color: #a6a6a6;
  }

  .floating-edit-card {
    background: rgba(31, 31, 31, 0.98) !important;
    border-color: rgba(48, 48, 48, 0.4) !important;
  }

  .floating-edit-card:hover {
    background: rgba(31, 31, 31, 1) !important;
    box-shadow: 0 20px 80px rgba(0, 0, 0, 0.5) !important;
  }

  .floating-edit-card .card-header {
    background: linear-gradient(135deg, #262626 0%, #1f1f1f 100%) !important;
    border-bottom-color: #404040 !important;
  }

  .floating-edit-card .card-header h4 {
    color: #ffffff !important;
  }

  .floating-edit-card .card-content {
    background: #1f1f1f !important;
  }

  .floating-edit-card .ant-form-item-label > label {
    color: #ffffff !important;
  }

  .floating-edit-card .ant-table-thead > tr > th {
    background: #262626 !important;
    color: #ffffff !important;
    border-bottom-color: #404040 !important;
  }

  .floating-edit-card .ant-table-tbody > tr > td {
    background: #1f1f1f !important;
    color: #a6a6a6 !important;
    border-bottom-color: #303030 !important;
  }

  .floating-edit-card .ant-table-tbody > tr:hover > td {
    background: #262626 !important;
  }

  .floating-edit-card .compact-table {
    background: #1f1f1f !important;
  }

  .floating-edit-card .compact-table .ant-table {
    background: #1f1f1f !important;
  }

  .column-name-cell .column-name {
    color: #ffffff;
  }

  .data-type-tag {
    background: #262626;
    color: #a6a6a6;
    border-color: #404040;
  }

  .empty-canvas-content h3 {
    color: #ffffff;
  }

  .empty-canvas-content p {
    color: #a6a6a6;
  }

  .guide-item {
    background: #262626;
    border-color: #404040;
  }

  .guide-item:hover {
    background: #1f3a8a;
    border-color: #3b82f6;
  }

  .guide-text {
    color: #ffffff;
  }

  .table-item {
    background: #262626;
    border-color: #404040;
  }

  .table-item:hover {
    background: #303030;
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  }

  .no-connection-tip {
    color: #a6a6a6;
  }

  .tip-content p {
    color: #a6a6a6 !important;
  }

  .empty-content h3,
  .empty-content-centered h3 {
    color: #ffffff !important;
  }

  .empty-content p,
  .empty-content-centered p {
    color: #a6a6a6 !important;
  }

  /* 全屏模式下的悬浮卡片暗黑模式 */
  .modeling-canvas.fullscreen .floating-edit-card {
    background: rgba(31, 31, 31, 0.98) !important;
    border-color: rgba(48, 48, 48, 0.4) !important;
  }

  .modeling-canvas.fullscreen .floating-edit-card:hover:not(.dragging) {
    background: rgba(31, 31, 31, 1) !important;
    box-shadow: 0 24px 96px rgba(0, 0, 0, 0.6) !important;
  }

  /* 拖拽时的头部样式暗黑模式 */
  .floating-edit-card.dragging .card-header {
    background: linear-gradient(135deg, #1f3a8a 0%, #1e40af 100%) !important;
    border-bottom-color: #3b82f6 !important;
  }

  /* 关系图例暗黑模式 */
  .relationship-legend {
    background: rgba(31, 31, 31, 0.95) !important;
    border-color: rgba(64, 64, 64, 0.3) !important;
  }

  .legend-title {
    color: #1890ff !important;
  }

  .legend-text {
    color: #a6a6a6 !important;
  }

  /* 全屏模式下的关系图例暗黑模式 */
  .modeling-canvas.fullscreen .relationship-legend {
    background: rgba(31, 31, 31, 0.9) !important;
  }
}

.data-modeling-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f0f2f5;
  padding: 16px;
  box-sizing: border-box;
  overflow: hidden;
  overscroll-behavior: none;
  touch-action: none;
}

.modeling-workspace {
  flex: 1;
  display: flex;
  gap: 16px;
  height: 100%;
  min-height: 500px;
}

.sidebar {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex-shrink: 0;
}

.connection-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.connection-section {
  padding: 0;
}

.connection-section h3 {
  margin: 0 0 8px 0;
  color: #1890ff;
  font-size: 16px;
}

.connection-section p {
  margin: 0 0 16px 0;
  color: #666;
  font-size: 14px;
}

.tables-panel {
  flex: 1;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.no-connection-tip {
  height: calc(100vh - 400px);
  min-height: 300px;
  max-height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tip-content {
  text-align: center;
}

.tip-content p {
  margin: 12px 0 0 0;
  color: #999;
  font-size: 14px;
}

.tables-list {
  height: calc(100vh - 400px);
  min-height: 300px;
  max-height: 500px;
  overflow-y: auto;
  padding: 8px;
}

.table-item {
  display: flex;
  align-items: center;
  padding: 12px;
  margin-bottom: 8px;
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.table-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
  transform: translateY(-1px);
}

.table-icon {
  font-size: 18px;
  color: #1890ff;
  margin-right: 12px;
}

.table-info {
  flex: 1;
}

.table-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.table-description {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.columns-count {
  font-size: 12px;
  color: #999;
}

.canvas-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  position: relative;
  overflow: hidden;
  overscroll-behavior: none;
  touch-action: none;
}

/* 悬浮工具栏样式 */
.floating-toolbar {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1001;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  pointer-events: auto;
}

.floating-toolbar:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.floating-toolbar .ant-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-radius: 6px;
  min-width: 32px;
}

.floating-toolbar .ant-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.floating-toolbar .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
}

.floating-toolbar .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
}

.floating-toolbar .ant-btn:disabled {
  background: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: #bfbfbf !important;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.floating-toolbar .ant-btn:disabled:hover {
  background: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: #bfbfbf !important;
  transform: none !important;
  box-shadow: none !important;
}

.modeling-canvas {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  min-height: calc(100vh - 120px);
  width: 100%;
  border: 1px solid #e8e8e8;
  overscroll-behavior: none;
  touch-action: none;
  
  /* 优化拖拽时的平滑度 */
  &.dragging-relationship {
    cursor: crosshair;
    user-select: none;
    
    .canvas-content {
      pointer-events: none;
    }
    
    .table-node {
      pointer-events: auto;
    }
  }
  
  /* 平移状态优化 */
  &.panning {
    cursor: grabbing;
    
    .canvas-content {
      will-change: transform;
      transition: none !important;
    }
    
    .table-node {
      pointer-events: none;
    }
    
    .floating-toolbar {
      pointer-events: none;
    }
    
    /* 关系线在画布拖拽时的优化 */
    .relationships-layer {
      opacity: 0.8;
      
      .relationship-click-area {
        pointer-events: none;
      }
    }
  }
  
  /* 缩放状态优化 */
  &.zooming {
    .canvas-content {
      will-change: transform;
    }
  }
}

.canvas-content {
  width: 100%;
  height: 100%;
  position: relative;
  min-height: 6000px;
  min-width: 6000px;
  background-image:
    radial-gradient(circle, #e8e8e8 1px, transparent 1px);
  background-size: 20px 20px;
  background-position: 0 0, 10px 10px;
  
  /* 硬件加速优化 */
  will-change: auto;
  transform-style: preserve-3d;
  backface-visibility: hidden;
  -webkit-font-smoothing: subpixel-antialiased;
  
  /* 拖拽状态优化 */
  .data-modeling-standalone.panning & {
    will-change: transform;
    transition: none !important;
  }
}

.empty-canvas {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 6000px;
  min-width: 6000px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.empty-content {
  text-align: center;
}

.empty-content h3 {
  margin: 16px 0 8px 0;
  color: #666;
}

.empty-content p {
  margin: 0;
  color: #999;
}

.table-node {
  min-width: 220px;
  max-width: 320px;
  background: #fff;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  user-select: none;
  z-index: 10;
  transition: all 0.3s ease;
  position: relative;
  will-change: auto;
  
  /* 硬件加速优化 */
  transform: translateZ(0);
  backface-visibility: hidden;
  
  /* 拖拽状态优化 */
  &.dragging {
    will-change: transform;
    transition: none !important;
    pointer-events: none;
    z-index: 1000;
    
    /* 拖拽时减少阴影计算 */
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
    
    .table-header,
    .column-item {
      transition: none !important;
    }
  }
  
  /* 非拖拽状态下的hover效果 */
  &:not(.dragging):hover {
    box-shadow: 0 6px 20px rgba(24, 144, 255, 0.3);
    border-color: #1890ff;
    transform: translateY(-2px) translateZ(0);
    
    .drag-handle {
      opacity: 1;
    }
  }
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e8e8e8;
  border-radius: 8px 8px 0 0;
  cursor: grab;
  transition: all 0.3s ease;
  position: relative;
}

.table-header::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #d9d9d9 0%, #bfbfbf 100%);
  border-radius: 2px;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.table-header:hover {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border-bottom-color: #1890ff;
}

.table-header:hover::before {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  opacity: 1;
}

.table-header:active {
  cursor: grabbing;
  background: linear-gradient(135deg, #bae7ff 0%, #e6f7ff 100%);
}

.table-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #1890ff;
  flex: 1;
}

/* 拖拽手柄样式 */
.drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: #999;
  border-radius: 4px;
  transition: all 0.3s ease;
  padding: 4px;
  opacity: 0.8;
  pointer-events: none; /* 禁用手柄的直接交互，让表头处理拖动 */
}

.table-header:hover .drag-handle {
  color: #1890ff;
  opacity: 1;
}

/*
.drag-handle:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
  transform: scale(1.1);
  opacity: 1;
}

.drag-handle:active {
  cursor: grabbing;
  background: rgba(24, 144, 255, 0.2);
}
*/

.drag-handle:active {
  cursor: grabbing;
  background: rgba(24, 144, 255, 0.2);
}

.table-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s;
}

.table-node:hover .table-actions {
  opacity: 1;
}

/* 拖拽状态样式 */
.table-node.dragging {
  transform: rotate(2deg) translateY(-2px);
  box-shadow: 0 8px 32px rgba(24, 144, 255, 0.3) !important;
  border-color: #1890ff !important;
  z-index: 1000;  /* 拖拽时确保在关系线之上 */
  transition: none;
}

.table-node.dragging .table-header {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
}

.table-node.dragging .drag-handle {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.2);
}

.table-columns {
  max-height: 300px;
  overflow-y: auto;
}

.column-item {
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
  position: relative;
}

.column-item:hover {
  background-color: #f8f9fa;
}

.column-item:last-child {
  border-bottom: none;
}

.column-item.primary-key {
  background: linear-gradient(135deg, #fff7e6 0%, #fef3d0 100%);
}

.column-item.foreign-key {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
}

.column-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.column-name {
  display: flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  flex: 1;
  min-width: 0;
}

.key-icon {
  font-size: 12px;
  flex-shrink: 0;
}

.column-type {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  margin-left: 8px;
}

/* 连接点样式 */
.connection-points {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.connection-point {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid #d9d9d9;
  background: #fff;
  position: absolute;
  transition: all 0.3s ease;
}

.connection-point.right {
  right: -4px;
}

.connection-point.left {
  left: -4px;
}

.connection-point.active {
  border-color: #1890ff;
  background: #1890ff;
  box-shadow: 0 0 8px rgba(24, 144, 255, 0.4);
}

.column-item:hover .connection-point {
  border-color: #1890ff;
  transform: scale(1.2);
}

.column-item:hover .connection-point.active {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 关系线层样式优化 */
.relationships-layer {
  pointer-events: none;
  
  /* 硬件加速优化 */
  will-change: auto;
  transform: translateZ(0);
  
  /* 高DPI屏幕优化 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
  
  /* 拖拽时的性能优化 - 只在画布拖拽时生效 */
  body.canvas-panning & {
    opacity: 0.7;
    will-change: transform;
  }
}

.relationships-group {
  /* SVG优化渲染设置 - 平衡性能和质量 */
  shape-rendering: geometricPrecision;
  
  /* 抗锯齿优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  .relationship-line {
    /* 优化线条渲染 */
    stroke-linecap: round;
    stroke-linejoin: round;
    vector-effect: non-scaling-stroke;
    transition: stroke-width 0.2s ease, filter 0.2s ease;
    
    /* 抗锯齿设置 */
    shape-rendering: geometricPrecision;
    
    /* 只在画布拖拽时禁用过渡 */
    body.canvas-panning & {
      transition: none !important;
    }
  }
  
  .relationship-click-area {
    cursor: pointer;
    pointer-events: all; /* 使用all确保在所有情况下都能点击 */
    opacity: 1; /* 保持opacity为1，依赖stroke的透明度 */
    
    &:hover {
      stroke: rgba(24, 144, 255, 0.15) !important;
    }
    
    &:hover + .relationship-line {
      stroke-width: 3;
      filter: drop-shadow(0 0 4px currentColor);
    }
    
    /* 只在画布拖拽时禁用点击 */
    body.canvas-panning & {
      pointer-events: none;
      
      &:hover + .relationship-line {
        stroke-width: inherit;
        filter: none;
      }
    }
    
    /* 开发调试：取消注释下面的规则来可视化点击区域 */
    /* 
    &:hover {
      stroke: rgba(255, 0, 0, 0.3) !important;
      stroke-width: 16 !important;
    }
    */
  }
}

.relationship-preview {
  /* 预览线条优化 */
  shape-rendering: geometricPrecision;
  stroke-linecap: round;
  stroke-linejoin: round;
  
  /* 抗锯齿优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  /* 预览线条特效 */
  filter: drop-shadow(0 2px 4px rgba(24, 144, 255, 0.3));
}

/* 不同关系类型的样式 */
.relationship-1-to-1 {
  stroke-dasharray: none;
}

.relationship-1-to-N,
.relationship-N-to-1 {
  stroke-dasharray: none;
}

.relationship-N-to-M {
  stroke-dasharray: 8 4;
}

/* 移除动画以提高性能 */
.relationship-line:hover {
  stroke-width: 4 !important;
}

/* 关系类型图例样式 */
.relationship-legend {
  position: absolute;
  bottom: 20px;
  left: 20px;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 8px 10px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.legend-header {
  margin-bottom: 8px;
}

.legend-title {
  font-weight: 500;
  color: #1890ff;
  font-size: 14px;
}

.legend-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-line {
  width: 24px;
  height: 12px;
}

.legend-text {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .modeling-workspace {
    flex-direction: column;
    height: auto;
  }

  .sidebar {
    width: 100%;
    flex-direction: row;
    height: auto;
  }

  .connection-card {
    flex: 1;
  }

  .tables-panel {
    flex: 2;
  }

  .tables-list {
    height: 200px;
  }

  .canvas-area {
    height: 500px;
  }

  .modeling-canvas {
    min-height: 500px;
  }

  .canvas-content {
    min-height: 500px;
  }

  .floating-toolbar {
    top: 10px;
    right: 10px;
    padding: 6px;
  }

  .floating-toolbar .ant-btn {
    width: 30px;
    height: 30px;
    font-size: 13px;
    min-width: 30px;
  }

  .relationship-legend {
    bottom: 15px;
    left: 15px;
    padding: 10px;
  }

  .legend-content {
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .data-modeling-container {
    padding: 8px;
  }

  .sidebar {
    flex-direction: column;
  }

  .connection-section {
    flex-direction: column;
    align-items: stretch;
  }

  .tables-list {
    height: 150px;
  }

  .floating-toolbar {
    position: fixed;
    bottom: 20px;
    right: 20px;
    top: auto;
    padding: 6px;
  }

  .floating-toolbar .ant-btn {
    width: 28px;
    height: 28px;
    font-size: 12px;
    min-width: 28px;
  }

  .canvas-tips {
    bottom: 20px;
    right: 80px;
    max-width: 160px;
    font-size: 11px;
    padding: 8px 12px;
  }

  .canvas-tips .tip-text {
    font-size: 11px;
  }

  .relationship-legend {
    bottom: 10px;
    left: 10px;
    padding: 8px;
  }

  .legend-content {
    flex-direction: column;
    gap: 8px;
  }

  .legend-title {
    font-size: 14px;
  }

  .legend-text {
    font-size: 12px;
  }
}

/* 工具栏分隔线 */
.toolbar-divider {
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #e8e8e8 20%, #e8e8e8 80%, transparent 100%);
  margin: 6px 0;
  opacity: 0.6;
}

/* 缩放信息显示 */
.zoom-info {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 50;
  background: rgba(0, 0, 0, 0.75);
  color: white;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
  pointer-events: none;
}

.zoom-info span {
  display: block;
  text-align: center;
  min-width: 40px;
}

/* 全屏画布样式 */
.modeling-canvas.fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999 !important;
  background: #fff !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  border: none !important;
}

.modeling-canvas.fullscreen .floating-toolbar {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 10001;
}

.modeling-canvas.fullscreen .zoom-info {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(15px);
}

.modeling-canvas.fullscreen .relationship-legend {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
}

.modeling-canvas.fullscreen .canvas-tips {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
}

/* 画布变换容器 */
.canvas-content {
  transition: transform 0.2s ease-out;
  will-change: transform;
  cursor: grab;
}

.canvas-content:active {
  cursor: grabbing;
}

/* 平移状态下的光标 */
.modeling-canvas.panning {
  cursor: grabbing !important;
}

.modeling-canvas.panning * {
  cursor: grabbing !important;
}

/* 缩放时的性能优化 */
.canvas-content.zooming {
  transition: none;
}

.canvas-content.zooming .table-node {
  will-change: transform;
}

.canvas-content.zooming .relationships-layer {
  will-change: transform;
}

.canvas-content.zooming .relationships-layer {
  will-change: transform;
}

.canvas-tips {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 50;
  background: rgba(255, 255, 255, 0.95);
  color: #666;
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  pointer-events: none;
  opacity: 0.8;
  max-width: 200px;
}

.tips-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.tip-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tip-icon {
  font-size: 14px;
  flex-shrink: 0;
}

.tip-text {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

/* 表编辑模态框样式 */
.column-name-cell {
  display: flex;
  align-items: center;
  gap: 6px;
}

.column-name-cell .key-icon {
  font-size: 12px;
  flex-shrink: 0;
}

.column-name-cell .key-icon.pk {
  color: #faad14;
}

.column-name-cell .key-icon.fk {
  color: #1890ff;
}

.column-name-cell .column-name {
  font-weight: 500;
  color: #333;
}

/* 表格样式优化 */
.ant-table-small .ant-table-tbody > tr > td {
  padding: 8px 12px;
}

.ant-table-small .ant-table-thead > tr > th {
  padding: 8px 12px;
  background: #fafafa;
  font-weight: 600;
}

/* 关系状态指示器样式 */
.relationship-status {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: auto;
  margin-right: 8px;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  opacity: 0.8;
}

.status-dot.outgoing {
  background-color: #52c41a;
}

.status-dot.incoming {
  background-color: #13c2c2;
}

/* 字段悬停效果 */
.column-item {
  cursor: pointer;
  transition: all 0.2s ease;
}

.column-item:hover {
  background-color: #f0f9ff;
  border-left: 2px solid #1890ff;
}

.column-item.primary-key:hover {
  background-color: #fff7e6;
  border-left: 2px solid #faad14;
}

.column-item.foreign-key:hover {
  background-color: #f0f9ff;
  border-left: 2px solid #1890ff;
}

/* 拖拽目标高亮 */
.column-item.drag-target {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%) !important;
  border-left: 3px solid #52c41a;
  animation: highlight 0.5s ease-in-out;
}

@keyframes highlight {
  0% {
    background: linear-gradient(135deg, #fff 0%, #fff 100%);
  }
  100% {
    background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  }
}

/* 拖拽状态下的画布样式 */
.modeling-canvas.dragging-relationship {
  cursor: crosshair !important;
}

.modeling-canvas.dragging-relationship * {
  cursor: crosshair !important;
}

/* 拖拽提示样式 */
.drag-hint {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 100;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  padding: 8px 10px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  pointer-events: none;
  opacity: 0.8;
  max-width: 200px;
}

.drag-hint span {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

.hidden-relationships-info {
  font-size: 12px;
  color: #999;
  margin-left: 10px;
}

.hidden-count {
  font-weight: 500;
  color: #1890ff;
}

.empty-state-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 50;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  pointer-events: none;
}

.empty-content-centered {
  text-align: center;
  pointer-events: auto;
}

.empty-content-centered h3 {
  margin: 16px 0 8px 0;
  color: #666;
}

.empty-content-centered p {
  margin: 0;
  color: #999;
}

.floating-edit-card {
  position: absolute;
  top: 80px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10000;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 12px;
  box-shadow: 0 16px 64px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  min-width: 600px;
  max-width: 800px;
  user-select: none;
  will-change: transform;
}

.floating-edit-card:hover {
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 20px 80px rgba(0, 0, 0, 0.2);
  transform: translateX(-50%) translateY(-2px);
}

/* 关系编辑卡片特定样式 */
.relationship-edit-card {
  min-width: 500px;
  max-width: 650px;
}

/* 表编辑卡片特定样式 */
.table-edit-card {
  min-width: 700px;
  max-width: 900px;
}

/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #e8e8e8;
  border-radius: 12px 12px 0 0;
  cursor: grab;
  transition: all 0.3s ease;
  position: relative;
}

.card-header::before {
  content: '';
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: linear-gradient(135deg, #d9d9d9 0%, #bfbfbf 100%);
  border-radius: 2px;
  opacity: 0.6;
  transition: all 0.3s ease;
}

.card-header:hover {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border-bottom-color: #1890ff;
}

.card-header:hover::before {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  opacity: 1;
}

.card-header:active {
  cursor: grabbing;
  background: linear-gradient(135deg, #bae7ff 0%, #e6f7ff 100%);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: #1890ff;
  font-size: 16px;
  flex: 1;
}

.card-icon {
  font-size: 16px;
  color: #1890ff;
  flex-shrink: 0;
}

.drag-indicator {
  font-size: 14px;
  color: #999;
  opacity: 0.6;
  transition: all 0.3s ease;
  font-weight: bold;
  letter-spacing: 1px;
  transform: rotate(90deg);
  margin: 0 4px;
}

.card-header:hover .drag-indicator {
  color: #1890ff;
  opacity: 1;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-radius: 6px;
  min-width: 32px;
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #e8e8e8;
}

.close-btn:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 1);
  border-color: #ff4d4f;
  color: #ff4d4f;
}

/* 卡片内容样式 */
.card-content {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

/* 卡片底部样式 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #fafafa;
  border-top: 1px solid #e8e8e8;
  border-radius: 0 0 12px 12px;
}

.footer-left, .footer-right {
  display: flex;
  gap: 10px;
}

/* 紧凑表单项样式 */
.compact-form-item {
  margin-bottom: 16px;
}

.compact-form-item .ant-form-item-label {
  padding-bottom: 4px;
}

.compact-form-item .ant-form-item-label > label {
  font-weight: 500;
  color: #333;
}

/* 列表容器样式 */
.columns-table-container {
  max-height: 250px;
  overflow-y: auto;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fff;
}

.compact-table {
  width: 100%;
}

.compact-table .ant-table-tbody > tr:hover > td {
  background: #f0f9ff;
}

/* 全屏模式下的悬浮卡片样式 */
.modeling-canvas.fullscreen .floating-edit-card {
  z-index: 10001 !important;
  background: rgba(255, 255, 255, 0.98) !important;
  backdrop-filter: blur(25px) !important;
  box-shadow: 0 20px 80px rgba(0, 0, 0, 0.25) !important;
  border: 1px solid rgba(255, 255, 255, 0.4) !important;
  max-height: 80vh;
}

/* 只有未被拖拽的卡片才应用初始居中样式 */
.modeling-canvas.fullscreen .floating-edit-card:not([style*="position: fixed"]) {
  position: fixed !important;
  top: 10vh !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
}

.modeling-canvas.fullscreen .floating-edit-card:hover:not(.dragging) {
  background: rgba(255, 255, 255, 1) !important;
  box-shadow: 0 24px 96px rgba(0, 0, 0, 0.3) !important;
}

.modeling-canvas.fullscreen .floating-edit-card:hover:not(.dragging):not([style*="position: fixed"]) {
  transform: translateX(-50%) translateY(-2px) !important;
}

.modeling-canvas.fullscreen .floating-edit-card .card-content {
  max-height: 60vh;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .floating-edit-card {
    min-width: 90vw;
    max-width: 95vw;
    left: 50%;
    top: 60px;
  }

  .relationship-edit-card,
  .table-edit-card {
    min-width: 90vw;
    max-width: 95vw;
  }

  .card-content {
    padding: 16px;
    max-height: 60vh;
  }

  .card-header {
    padding: 12px 16px;
  }

  .card-footer {
    padding: 12px 16px;
  }
}

@media (max-width: 768px) {
  .floating-edit-card {
    min-width: 95vw;
    max-width: 98vw;
    top: 40px;
  }

  .card-title {
    font-size: 14px;
  }

  .card-content {
    padding: 12px;
    max-height: 50vh;
  }

  .card-header {
    padding: 10px 12px;
  }

  .card-footer {
    padding: 10px 12px;
  }

  .columns-table-container {
    max-height: 150px;
  }

  .footer-left,
  .footer-right {
    flex-direction: column;
    gap: 6px;
  }
}

/* 卡片拖拽状态 */
.floating-edit-card.dragging {
  cursor: grabbing !important;
  transform: none !important; /* 清除默认的transform */
  box-shadow: 0 24px 96px rgba(0, 0, 0, 0.3) !important;
  z-index: 10002 !important;
  transition: none !important;
  opacity: 0.95;
}

/* 全屏模式下的拖拽状态 - 确保最高优先级 */
.modeling-canvas.fullscreen .floating-edit-card.dragging {
  position: fixed !important;
  z-index: 10003 !important;
  transform: none !important;
}

.floating-edit-card.dragging * {
  cursor: grabbing !important;
  pointer-events: none; /* 拖拽时禁用所有内部交互 */
}

/* 拖拽时的头部样式 */
.floating-edit-card.dragging .card-header {
  background: linear-gradient(135deg, #bae7ff 0%, #e6f7ff 100%) !important;
  border-bottom-color: #1890ff !important;
}

/* 动画效果 */
.floating-edit-card {
  animation: fadeInScale 0.3s ease-out;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: translateX(-50%) scale(0.95) translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) scale(1) translateY(0);
  }
}

/* 当卡片被拖拽后，移除居中transform */
.floating-edit-card[style*="position: fixed"] {
  transform: none !important;
}

/* 表单项间距优化 */
.compact-form-item .ant-row {
  margin-bottom: 0;
}

.compact-form-item .ant-col {
  margin-bottom: 0;
}

/* 选择框样式优化 */
.compact-form-item .ant-select {
  width: 100%;
}

.compact-form-item .ant-input,
.compact-form-item .ant-input-affix-wrapper {
  border-radius: 4px;
  transition: all 0.3s ease;
}

.compact-form-item .ant-input:focus,
.compact-form-item .ant-input-affix-wrapper:focus,
.compact-form-item .ant-input-affix-wrapper-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式优化 */
.card-footer .ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.card-footer .ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-footer .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
}

.card-footer .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
}

.card-footer .ant-btn-primary:not(:disabled):not(.ant-btn-disabled):hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  border-color: transparent;
}

/* 滚动条样式 */
.card-content::-webkit-scrollbar,
.columns-table-container::-webkit-scrollbar {
  width: 6px;
}

.card-content::-webkit-scrollbar-track,
.columns-table-container::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 3px;
}

.card-content::-webkit-scrollbar-thumb,
.columns-table-container::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
  transition: background 0.3s ease;
}

.card-content::-webkit-scrollbar-thumb:hover,
.columns-table-container::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

/* 悬浮卡片内的弹出层样式 */
.floating-edit-card .ant-select-dropdown,
.floating-edit-card .ant-popover,
.floating-edit-card .ant-tooltip {
  z-index: 10010 !important;
}

/* 确保弹出层在全屏模式下也有正确的层级 */
.modeling-canvas.fullscreen .floating-edit-card .ant-select-dropdown,
.modeling-canvas.fullscreen .floating-edit-card .ant-popover,
.modeling-canvas.fullscreen .floating-edit-card .ant-tooltip {
  z-index: 10010 !important;
}

/* 全局确保Ant Design弹出层在悬浮卡片中有足够高的z-index */
:deep(.ant-select-dropdown),
:deep(.ant-popover),
:deep(.ant-tooltip),
:deep(.ant-popconfirm) {
  z-index: 10010 !important;
}

/* 特别针对悬浮卡片的弹出层 */
:deep(.floating-edit-card .ant-select-dropdown),
:deep(.floating-edit-card .ant-popover),
:deep(.floating-edit-card .ant-tooltip),
:deep(.floating-edit-card .ant-popconfirm) {
  z-index: 10010 !important;
}

:deep(.floating-edit-card .ant-popconfirm) {
  z-index: 10010 !important;
}

/* 确保所有类型的弹出层在任何情况下都有足够高的z-index */
:deep(.ant-select-dropdown),
:deep(.ant-dropdown),
:deep(.ant-popover),
:deep(.ant-tooltip),
:deep(.ant-popconfirm),
:deep(.ant-picker-dropdown),
:deep(.ant-cascader-dropdown),
:deep(.ant-tree-select-dropdown),
:deep(.ant-mention-dropdown),
:deep(.ant-modal),
:deep(.ant-drawer) {
  z-index: 10010 !important;
}

/* 全屏模式下的所有弹出层 */
.modeling-canvas.fullscreen :deep(.ant-select-dropdown),
.modeling-canvas.fullscreen :deep(.ant-dropdown),
.modeling-canvas.fullscreen :deep(.ant-popover),
.modeling-canvas.fullscreen :deep(.ant-tooltip),
.modeling-canvas.fullscreen :deep(.ant-popconfirm),
.modeling-canvas.fullscreen :deep(.ant-picker-dropdown),
.modeling-canvas.fullscreen :deep(.ant-cascader-dropdown),
.modeling-canvas.fullscreen :deep(.ant-tree-select-dropdown),
.modeling-canvas.fullscreen :deep(.ant-mention-dropdown),
.modeling-canvas.fullscreen :deep(.ant-modal),
.modeling-canvas.fullscreen :deep(.ant-drawer) {
  z-index: 10020 !important;
}

/* 针对具体的弹出确认框组件类名 */
:deep(.ant-popover-placement-top),
:deep(.ant-popover-placement-bottom),
:deep(.ant-popover-placement-left),
:deep(.ant-popover-placement-right),
:deep(.ant-popover-placement-topLeft),
:deep(.ant-popover-placement-topRight),
:deep(.ant-popover-placement-bottomLeft),
:deep(.ant-popover-placement-bottomRight),
:deep(.ant-popover-placement-leftTop),
:deep(.ant-popover-placement-leftBottom),
:deep(.ant-popover-placement-rightTop),
:deep(.ant-popover-placement-rightBottom) {
  z-index: 10010 !important;
}

/* 暗黑模式样式 */
[data-theme='dark'] .data-modeling-standalone {
  /* 暗黑模式CSS变量 */
  --heading-color: rgba(255, 255, 255, 0.85);
  --text-color: rgba(255, 255, 255, 0.65);
  --text-color-secondary: rgba(255, 255, 255, 0.45);
  --disabled-color: rgba(255, 255, 255, 0.25);
  --border-color-base: #404040;
  --box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.3);
  --background-color-light: #262626;
  --background-color-base: #1f1f1f;

  background: var(--background-color-base);
  color: var(--text-color);

  &.fullscreen-root {
    background: var(--background-color-base) !important;
  }

  /* 工具栏暗黑模式 */
  .toolbar {
    background: #262626;
    border-bottom-color: #404040;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

    .toolbar-section {
      .toolbar-title {
        color: rgba(255, 255, 255, 0.85);
      }

      .toolbar-button {
        background: #1a1a1a;
        border-color: #404040;
        color: rgba(255, 255, 255, 0.65);

        &:hover {
          background: #303030;
          border-color: #1890ff;
          color: #1890ff;
        }

        &.active {
          background: #1890ff;
          border-color: #1890ff;
          color: white;
        }
      }
    }
  }

  /* 画布暗黑模式 */
  .canvas-container {
    background: #1a1a1a;

    .canvas-background {
      background: #1a1a1a;
    }

    .grid-pattern {
      stroke: #404040;
      opacity: 0.3;
    }
  }

  /* 表格节点暗黑模式 */
  .table-node {
    background: #262626;
    border-color: #404040;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

    &:hover {
      border-color: #1890ff;
      box-shadow: 0 6px 16px rgba(24, 144, 255, 0.2);
    }

    &.selected {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.3);
    }

    .table-header {
      background: #1a1a1a;
      border-bottom-color: #404040;

      .table-name {
        color: rgba(255, 255, 255, 0.85);
      }

      .table-actions {
        .action-button {
          color: rgba(255, 255, 255, 0.65);

          &:hover {
            color: #1890ff;
            background: rgba(24, 144, 255, 0.1);
          }
        }
      }
    }

    .table-body {
      background: #262626;

      .field-row {
        border-bottom-color: #404040;

        &:hover {
          background: #303030;
        }

        .field-name {
          color: rgba(255, 255, 255, 0.85);
        }

        .field-type {
          color: rgba(255, 255, 255, 0.65);
        }

        .field-constraints {
          .constraint-tag {
            background: #1a1a1a;
            border-color: #404040;
            color: rgba(255, 255, 255, 0.65);

            &.primary-key {
              background: rgba(245, 34, 45, 0.15);
              border-color: #ff7875;
              color: #ff7875;
            }

            &.foreign-key {
              background: rgba(24, 144, 255, 0.15);
              border-color: #40a9ff;
              color: #40a9ff;
            }

            &.unique {
              background: rgba(250, 173, 20, 0.15);
              border-color: #ffc53d;
              color: #ffc53d;
            }

            &.not-null {
              background: rgba(82, 196, 26, 0.15);
              border-color: #73d13d;
              color: #73d13d;
            }
          }
        }
      }
    }
  }

  /* 关系线暗黑模式 */
  .relationship-line {
    stroke: #1890ff;

    &.selected {
      stroke: #40a9ff;
      stroke-width: 3;
    }

    &:hover {
      stroke: #69c0ff;
      stroke-width: 2.5;
    }
  }

  /* 侧边栏暗黑模式 */
  .sidebar {
    background: #262626;
    border-color: #404040;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

    .sidebar-header {
      background: #1a1a1a;
      border-bottom-color: #404040;

      .sidebar-title {
        color: rgba(255, 255, 255, 0.85);
      }

      .close-button {
        color: rgba(255, 255, 255, 0.65);

        &:hover {
          color: #ff4d4f;
          background: rgba(255, 77, 79, 0.1);
        }
      }
    }

    .sidebar-content {
      background: #262626;

      .form-group {
        .form-label {
          color: rgba(255, 255, 255, 0.85);
        }

        .form-input,
        .form-select,
        .form-textarea {
          background: #1a1a1a;
          border-color: #404040;
          color: rgba(255, 255, 255, 0.85);

          &:hover {
            border-color: #1890ff;
          }

          &:focus {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }

        .form-input::placeholder,
        .form-textarea::placeholder {
          color: rgba(255, 255, 255, 0.45);
        }
      }

      .field-list {
        .field-item {
          background: #1a1a1a;
          border-color: #404040;

          &:hover {
            background: #303030;
            border-color: #1890ff;
          }

          .field-info {
            .field-name {
              color: rgba(255, 255, 255, 0.85);
            }

            .field-type {
              color: rgba(255, 255, 255, 0.65);
            }
          }

          .field-actions {
            .action-button {
              color: rgba(255, 255, 255, 0.65);

              &:hover {
                color: #1890ff;
                background: rgba(24, 144, 255, 0.1);
              }

              &.delete-button:hover {
                color: #ff4d4f;
                background: rgba(255, 77, 79, 0.1);
              }
            }
          }
        }
      }
    }
  }

  /* 按钮暗黑模式 */
  .ant-btn {
    &:not(.ant-btn-primary):not(.ant-btn-danger) {
      background: #262626;
      border-color: #404040;
      color: rgba(255, 255, 255, 0.65);

      &:hover {
        background: #303030;
        border-color: #1890ff;
        color: #1890ff;
      }
    }
  }

  /* 模态框暗黑模式 */
  .ant-modal {
    .ant-modal-content {
      background: #262626;

      .ant-modal-header {
        background: #262626;
        border-bottom-color: #404040;

        .ant-modal-title {
          color: rgba(255, 255, 255, 0.85);
        }
      }

      .ant-modal-body {
        background: #262626;
        color: rgba(255, 255, 255, 0.85);
      }

      .ant-modal-footer {
        background: #262626;
        border-top-color: #404040;
      }
    }
  }

  /* 弹出框暗黑模式 */
  .ant-popover {
    .ant-popover-content {
      background: #262626;

      .ant-popover-inner {
        background: #262626;
        border-color: #404040;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

        .ant-popover-title {
          background: #262626;
          border-bottom-color: #404040;
          color: rgba(255, 255, 255, 0.85);
        }

        .ant-popover-inner-content {
          background: #262626;
          color: rgba(255, 255, 255, 0.85);
        }
      }
    }

    .ant-popover-arrow {
      &::before {
        background: #262626;
        border-color: #404040;
      }
    }
  }
}
