// 侧边栏样式
.chat-sidebar {
  width: 280px;
  max-height: calc(100vh - 130px); // 减去顶部导航栏高度并留出更多空间
  height: 0; // 初始高度为0，实现卷帘门效果
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 12px; // 四周圆角
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04); // 多层阴影
  position: fixed;
  top: 184px; // 从导航栏下方开始，增加间距
  left: 8px; // 增加左侧间距
  z-index: 1000;
  overflow: hidden; // 隐藏超出部分
  transition: height 0.4s cubic-bezier(0.4, 0, 0.2, 1), box-shadow 0.3s ease; // 同时动画高度和阴影
  
  &.sidebar-visible {
    height: calc(100vh - 194px); // 展开到完整高度，与max-height保持一致
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12), 0 4px 16px rgba(0, 0, 0, 0.08); // 展开时增强多层阴影
  }
  
  // 搜索区域
  .search-section {
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;
    background: white;
    border-radius: 12px 12px 0 0; // 顶部圆角，因为现在是顶部区域
    flex-shrink: 0; // 防止压缩
    
    .search-input-container {
      position: relative;
      display: flex;
      align-items: center;
      
      .search-input {
        flex: 1;
        height: 36px;
        padding: 0 36px 0 12px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        font-size: 14px;
        outline: none;
        background: white;
        transition: all 0.2s;
        
        &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
        }
        
        &::placeholder {
          color: #bfbfbf;
        }
      }
      
      .search-btn {
        position: absolute;
        right: 36px;
        top: 50%;
        transform: translateY(-50%);
        width: 24px;
        height: 24px;
        border: none;
        background: transparent;
        cursor: pointer;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        transition: all 0.2s;
        
        &:hover:not(:disabled) {
          background: #f0f0f0;
          color: #1890ff;
        }
        
        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }
      }
      
      .clear-search-btn {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        width: 24px;
        height: 24px;
        border: none;
        background: transparent;
        cursor: pointer;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        transition: all 0.2s;
        
        &:hover {
          background: #f0f0f0;
          color: #666;
        }
      }
    }
    
    .search-loading {
      margin-top: 8px;
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: #999;
    }
    
    .search-stats {
      margin-top: 8px;
      font-size: 12px;
      color: #666;
    }
  }
  
  .sidebar-content {
    flex: 1;
    overflow: hidden;
    background: white;
    border-radius: 0 0 12px 12px; // 底部圆角
    display: flex;
    flex-direction: column;
    min-height: 0; // 允许flex子项缩小
    
          // 搜索结果
      .search-results {
        flex: 1;
        overflow-y: auto;
        padding: 16px 20px;
        
        &::-webkit-scrollbar {
          width: 4px;
        }
        
        &::-webkit-scrollbar-track {
          background: transparent;
        }
        
        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 2px;
          
          &:hover {
            background: rgba(0, 0, 0, 0.2);
          }
        }
        
        .search-results-header {
          margin-bottom: 16px;
          
          .search-results-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
          }
        }
        
        .search-results-list {
          .search-result-item {
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.2s;
            
            &:hover {
              background: #f8f9fa;
              margin: 0 -12px;
              padding: 12px;
              border-radius: 6px;
            }
            
            .search-result-content {
              .search-result-text {
                font-size: 14px;
                color: #333;
                line-height: 1.6;
                margin-bottom: 8px;
                
                // 高亮样式
                :global(mark) {
                  background: #fff2e6;
                  color: #fa8c16;
                  padding: 2px 4px;
                  border-radius: 3px;
                  font-weight: 500;
                }
              }
              
              .search-result-meta {
                display: flex;
                align-items: center;
                gap: 8px;
                font-size: 12px;
                color: #999;
                
                .search-result-role {
                  padding: 2px 6px;
                  border-radius: 3px;
                  font-size: 11px;
                  font-weight: 500;
                  
                  &:contains('您') {
                    background: #e6f7ff;
                    color: #1890ff;
                  }
                  
                  &:contains('AI') {
                    background: #f6ffed;
                    color: #52c41a;
                  }
                }
                
                .search-result-time {
                  color: #999;
                }
              }
            }
          }
        }
        
        .load-more-search {
          text-align: center;
          padding: 16px 0;
          
          .load-more-btn {
            padding: 8px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            gap: 6px;
            
            &:hover:not(:disabled) {
              border-color: #1890ff;
              color: #1890ff;
            }
            
            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }
          }
        }
      }
    
          // 无搜索结果
      .no-search-results {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 40px 20px;
        color: #999;
        
        .no-results-icon {
          font-size: 48px;
          margin-bottom: 16px;
          color: #d9d9d9;
        }
        
        .no-results-text {
          font-size: 16px;
          margin-bottom: 8px;
          color: #666;
        }
        
        .no-results-desc {
          font-size: 14px;
          color: #999;
        }
      }

      .sessions-list {
        flex: 1;
        overflow-y: auto;
        padding: 20px 20px 20px;
        
        &::-webkit-scrollbar {
          width: 4px;
        }
        
        &::-webkit-scrollbar-track {
          background: transparent;
        }
        
        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.1);
          border-radius: 2px;
          
          &:hover {
            background: rgba(0, 0, 0, 0.2);
          }
        }
        
        .session-group {
          margin-bottom: 20px;
          
          .session-group-title {
            font-size: 12px;
            font-weight: 500;
            color: #999;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            padding-left: 4px;
          }
          
          .session-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 12px 8px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            margin-bottom: 4px;
            
            &:hover {
              background: #f8f9fa;
            }
            
            &.active {
              background: #f0f8ff;
              border-left: 3px solid #1890ff;
              
              .session-name {
                color: #1890ff;
                font-weight: 500;
              }
            }
            
            .session-info {
              flex: 1;
              min-width: 0;
              
              .session-name {
                font-size: 14px;
                color: #333;
                margin-bottom: 4px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                line-height: 1.2;
              }
              
              .session-meta {
                font-size: 12px;
                color: #999;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                line-height: 1.2;
              }
            }
            
            .session-actions {
              flex-shrink: 0;
              margin-left: 8px;
              opacity: 0;
              transition: opacity 0.2s;
              
              .session-action-btn {
                width: 24px;
                height: 24px;
                border: none;
                background: transparent;
                cursor: pointer;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #999;
                
                &:hover {
                  background: #f0f0f0;
                  color: #333;
                }
              }
            }
            
            &:hover .session-actions {
              opacity: 1;
            }
          }
        }
        
        .no-sessions {
          text-align: center;
          padding: 40px 20px;
          color: #999;
          
          .no-sessions-text {
            font-size: 16px;
            margin-bottom: 8px;
          }
          
          .no-sessions-desc {
            font-size: 14px;
            color: #ccc;
          }
        }
      }

  }
}

// 移动端适配
@media (max-width: 768px) {
  .chat-sidebar {
    width: 100vw;
    top: 0;
    left: 0;
    height: 0;
    max-height: 100vh;
    border-radius: 0;
    
    &.sidebar-visible {
      height: 100vh;
    }
    
    .search-section {
      border-radius: 0;
      padding: 20px 16px 16px;
    }
    
    .sidebar-content {
      border-radius: 0;
    }
  }
}

@media (max-width: 480px) {
  .chat-sidebar {
    .search-section {
      padding: 16px 12px 12px;
    }
    
    .sidebar-content {
      .sessions-list {
        padding: 16px 12px 16px;
      }
    }
  }
}

// 暗黑模式支持
[data-theme='dark'] .chat-sidebar {
  background: #1f1f1f;
  border-color: #303030;
  color: rgba(255, 255, 255, 0.85);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3), 0 1px 4px rgba(0, 0, 0, 0.2);

  &.sidebar-visible {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 4px 16px rgba(0, 0, 0, 0.3);
  }

  .search-section {
    background: #1f1f1f;
    border-bottom-color: #303030;

    .search-input {
      background: #262626;
      border-color: #404040;
      color: rgba(255, 255, 255, 0.85);

      &::placeholder {
        color: rgba(255, 255, 255, 0.45);
      }

      &:focus {
        border-color: #1890ff;
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }

    .search-btn,
    .clear-search-btn {
      color: rgba(255, 255, 255, 0.65);

      &:hover:not(:disabled) {
        background: #303030;
        color: #1890ff;
      }
    }
  }

  .sidebar-content {
    background: #1f1f1f;

    .search-results {
      .search-result-item {
        border-bottom-color: #303030;

        &:hover {
          background: #262626;
        }

        .search-result-content {
          .search-result-text {
            color: rgba(255, 255, 255, 0.85);
          }

          .search-result-meta {
            color: rgba(255, 255, 255, 0.45);

            .search-result-role {
              &:contains('您') {
                background: rgba(24, 144, 255, 0.15);
                color: #40a9ff;
              }

              &:contains('AI') {
                background: rgba(82, 196, 26, 0.15);
                color: #73d13d;
              }
            }
          }
        }
      }
    }

    .sessions-list {
      .session-group {
        .session-group-title {
          color: rgba(255, 255, 255, 0.45);
        }

        .session-item {
          &:hover {
            background: #262626;
          }

          &.active {
            background: rgba(24, 144, 255, 0.15);
            border-left-color: #40a9ff;

            .session-name {
              color: #40a9ff;
            }
          }

          .session-info {
            .session-name {
              color: rgba(255, 255, 255, 0.85);
            }

            .session-meta {
              color: rgba(255, 255, 255, 0.45);
            }
          }

          .session-actions {
            .session-action-btn {
              color: rgba(255, 255, 255, 0.65);

              &:hover {
                background: rgba(255, 255, 255, 0.1);
                color: rgba(255, 255, 255, 0.85);
              }
            }
          }
        }
      }

      .no-sessions {
        color: rgba(255, 255, 255, 0.45);

        .no-sessions-text {
          color: rgba(255, 255, 255, 0.45);
        }

        .no-sessions-desc {
          color: rgba(255, 255, 255, 0.25);
        }
      }
    }
  }
}