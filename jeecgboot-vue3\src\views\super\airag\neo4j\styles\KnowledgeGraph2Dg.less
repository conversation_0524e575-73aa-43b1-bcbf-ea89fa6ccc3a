// 2D知识图谱 - 实体详情面板样式
@import './common.less';

// 实体详情面板共享样式（可在父组件中复用）
.entity-detail-style {
  .entity-detail-base();
  .node-relations-style();
}

// ========== 暗黑模式适配 ==========
[data-theme='dark'] {
  .entity-detail-style {
    .entity-detail-base();
    .node-relations-style();

    // 确保在暗黑模式下正确显示
    background: @dark-bg-translucent !important;
    border-color: @dark-border-color !important;
    color: @dark-text-color !important;

    .entity-detail-header {
      background: @dark-bg-secondary !important;
      border-bottom-color: @dark-border-color !important;

      .node-title h3 {
        color: @dark-text-color !important;
      }

      .ant-btn {
        color: @dark-text-color-light !important;

        &:hover {
          color: @dark-text-color !important;
          background: rgba(255, 255, 255, 0.1) !important;
        }
      }
    }

    .entity-detail-content {
      background: @dark-bg-primary !important;

      .ant-descriptions {
        .ant-descriptions-item-label {
          color: @dark-text-color-secondary !important;
        }

        .ant-descriptions-item-content {
          color: @dark-text-color !important;
        }
      }
    }
  }
}