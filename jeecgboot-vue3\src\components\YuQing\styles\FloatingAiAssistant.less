.floating-ai-assistant {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

.ai-button {
  width: 50px;
  height: 120px;
  background: white;
  border-radius: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: visible;
  padding: 12px 8px;

  &::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 43px;
    z-index: -1;
    transition: all 0.3s ease;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    
    &::before {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
  }

  .ai-head {
    width: 42px;
    height: 42px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    position: relative;
    z-index: 1;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
    
    &::before {
      content: '';
      position: absolute;
      top: -3px;
      left: -3px;
      right: -3px;
      bottom: -3px;
      background: conic-gradient(from 0deg, #667eea, #764ba2, #4facfe, #00f2fe, #667eea);
      border-radius: 50%;
      z-index: -1;
      animation: borderFlow 3s linear infinite;
    }
    
    &::after {
      content: '';
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      background: white;
      border-radius: 50%;
      z-index: -1;
    }
  }

  .ai-eyes {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .ai-eye {
    width: 6px;
    height: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    transition: transform 0.1s ease;
  }

  .left-eye, .right-eye {
    display: block;
  }

  .ai-text {
    font-size: 12px;
    font-weight: 600;
    line-height: 1.2;
    color: #667eea;
    margin-top: 6px;
    text-align: center;
    white-space: nowrap;
      }
  }

@keyframes borderFlow {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.ai-chat-panel {
  width: 380px;
  height: 600px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: headerShimmer 4s infinite;
  }
}

.header-info {
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 1;
}

.header-avatar {
  display: flex;
  align-items: center;
}

.header-head {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 1;
  
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: conic-gradient(from 0deg, rgba(102, 126, 234, 0.7), rgba(118, 75, 162, 0.7), rgba(79, 172, 254, 0.7), rgba(0, 242, 254, 0.7), rgba(102, 126, 234, 0.7));
    border-radius: 50%;
    z-index: -1;
    animation: borderFlow 4s linear infinite reverse;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 50%;
    z-index: -1;
  }
}

.header-eyes {
  display: flex;
  align-items: center;
  gap: 5px;
}

.header-eye {
  width: 4px;
  height: 8px;
  background: rgba(102, 126, 234, 0.8);
  border-radius: 50%;
}

.ai-title {
  font-size: 16px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

@keyframes headerShimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.2s ease;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 36px;
  min-height: 36px;
  position: relative;

  &:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.15);
    transform: scale(1.05);
  }

  &:active:not(:disabled) {
    transform: scale(0.95);
  }

  &:disabled {
    opacity: 0.4;
    cursor: not-allowed;
  }
}

// 图片预览模态框样式
.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
}

.image-preview-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: zoomIn 0.3s ease-out;
}

.image-preview-close {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  font-size: 20px;
  font-weight: bold;
  cursor: pointer;
  z-index: 10;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background: rgba(0, 0, 0, 0.9);
    transform: scale(1.1);
  }
}

.image-preview-img {
  width: 100%;
  height: auto;
  max-width: 90vw;
  max-height: 80vh;
  object-fit: contain;
  display: block;
}

.image-preview-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 20px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.minimize-btn {
  font-size: 20px;
  font-weight: bold;
}

.clear-btn {
  font-size: 16px;
  
  &:disabled {
    opacity: 0.3;
  }
  
  &:hover:not(:disabled) {
    animation: shake 0.5s ease-in-out;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0) scale(1.05);
  }
  25% {
    transform: translateX(-2px) scale(1.05);
  }
  75% {
    transform: translateX(2px) scale(1.05);
  }
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.welcome-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.welcome-card {
  text-align: center;
  padding: 32px 24px;
  max-width: 300px;

  h3 {
    color: #333;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  p {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 24px;
  }
}

.message {
  display: flex;
  margin-bottom: 16px;

  &.user {
    justify-content: flex-end;
  }

  &.ai {
    justify-content: flex-start;
  }
}

.message-content {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 18px;
  
  .message-text {
    font-size: 14px;
    line-height: 1.4;
    word-wrap: break-word;
  }
  
  .message-images {
    margin-top: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  
  .message-image-container {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    background: rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;  /* 整个容器都显示指针光标 */
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .message-image {
      width: 100%;
      height: auto;
      max-height: 200px;
      object-fit: cover;
      display: block;
      border-radius: 8px;
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        transform: scale(1.02);
        filter: brightness(1.1);
      }
      
      &::after {
        content: '🔍';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 14px;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
      }
      
      &:hover::after {
        opacity: 1;
      }
    }
    
    .image-caption {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
      color: white;
      padding: 8px 12px;
      font-size: 12px;
      font-weight: 500;
      border-radius: 0 0 8px 8px;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
      opacity: 0;
      transition: opacity 0.3s ease;
      pointer-events: none;  /* 防止阻挡图片点击事件 */
    }
    
    &:hover .image-caption {
      opacity: 1;
    }
  }
  
  .message-time {
    font-size: 11px;
    margin-top: 4px;
    opacity: 0.7;
  }
}

.message.user .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 18px 18px 4px 18px;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.message.ai .message-content {
  background: #f8f9fa;
  color: #333;
  border-radius: 18px 18px 18px 4px;
}

// 思考泡泡框样式
.thinking-bubble-container {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 16px;
  animation: fadeInUp 0.4s ease-out;
}

.thinking-bubble {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20px 20px 20px 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  position: relative;
  max-width: 85%;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(102, 126, 234, 0.1);
  
  &::before {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 20px 20px 20px 6px;
    z-index: -1;
    animation: borderGlow 2s ease-in-out infinite alternate;
  }
}

.thinking-avatar {
  flex-shrink: 0;
}

.thinking-head {
  width: 28px;
  height: 28px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
  animation: headPulse 2s ease-in-out infinite;
  
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: conic-gradient(from 0deg, #667eea, #764ba2, #4facfe, #00f2fe, #667eea);
    border-radius: 50%;
    z-index: -1;
    animation: borderFlow 3s linear infinite;
  }
  
  &::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    z-index: -1;
  }
}

.thinking-eyes {
  display: flex;
  align-items: center;
  gap: 4px;
}

.thinking-eye {
  width: 3px;
  height: 6px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  animation: eyeBlink 3s ease-in-out infinite;
}

.thinking-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.thinking-text {
  font-size: 14px;
  font-weight: 500;
  color: #555;
  line-height: 1.4;
}

.thinking-dots {
  display: flex;
  align-items: center;
  gap: 4px;
}

.thinking-dots .dot {
  width: 6px;
  height: 6px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  animation: dotPulse 1.4s ease-in-out infinite;
  
  &:nth-child(2) {
    animation-delay: 0.2s;
  }
  
  &:nth-child(3) {
    animation-delay: 0.4s;
  }
}

// 动画定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes borderGlow {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.8;
  }
}

@keyframes headPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes eyeBlink {
  0%, 90%, 100% {
    height: 6px;
  }
  95% {
    height: 1px;
  }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(1);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.3);
    opacity: 1;
  }
}

.chat-input {
  border-top: 1px solid #e9ecef;
  padding: 16px;
  background: white;
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 8px;
}

.message-input {
  flex: 1;
  border: none;
  background: none;
  resize: none;
  outline: none;
  font-size: 14px;
  line-height: 1.4;
  padding: 8px 12px;
  min-height: 20px;
  max-height: 120px;
  font-family: inherit;

  &::placeholder {
    color: #999;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.send-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  }

  &:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 4px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Markdown内容样式 */
.markdown-content {
  :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
    margin: 16px 0 8px 0;
    font-weight: 600;
    line-height: 1.3;
    color: #333;
  }

  :deep(h1) { font-size: 18px; }
  :deep(h2) { font-size: 16px; }
  :deep(h3) { font-size: 15px; }
  :deep(h4) { font-size: 14px; }
  :deep(h5), :deep(h6) { font-size: 13px; }

  :deep(p) {
    margin: 8px 0;
    line-height: 1.6;
  }

  :deep(strong) {
    font-weight: 600;
  }

  :deep(em) {
    font-style: italic;
  }

  :deep(ul), :deep(ol) {
    margin: 8px 0;
    padding-left: 20px;
  }

  :deep(li) {
    margin: 4px 0;
    line-height: 1.5;
  }

  :deep(code) {
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 4px;
    padding: 2px 4px;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 13px;
    color: #667eea;
  }
  
  // 特殊处理：当code标签只包含路径链接时，隐藏code的背景和边框
  :deep(code.route-code) {
    background: transparent !important;
    border: none !important;
    padding: 0 !important;
  }

  :deep(pre) {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 12px;
    margin: 12px 0;
    overflow-x: auto;
    line-height: 1.4;

    code {
      background: none;
      border: none;
      padding: 0;
      color: #333;
    }
  }

  :deep(blockquote) {
    border-left: 4px solid #667eea;
    background: rgba(102, 126, 234, 0.05);
    margin: 12px 0;
    padding: 8px 12px;
    font-style: italic;
    color: #555;
  }

  :deep(table) {
    width: 100%;
    border-collapse: collapse;
    margin: 12px 0;
    font-size: 13px;
  }

  :deep(th), :deep(td) {
    border: 1px solid #e9ecef;
    padding: 8px;
    text-align: left;
  }

  :deep(th) {
    background: #f8f9fa;
    font-weight: 600;
  }

  :deep(hr) {
    border: none;
    border-top: 1px solid #e9ecef;
    margin: 16px 0;
  }

  :deep(a) {
    color: #667eea;
    text-decoration: none;
    
    &:hover {
      text-decoration: underline;
    }
  }

  // 路径链接样式
  :deep(.route-link) {
    // 重置所有可能的继承样式
    all: unset;
    
    // 重新定义所有样式
    color: #1890ff !important;
    background: rgba(24, 144, 255, 0.08) !important;
    border: 1px solid rgba(24, 144, 255, 0.25) !important;
    border-radius: 4px !important;
    padding: 2px 6px !important;
    font-family: 'Monaco', 'Consolas', 'SF Mono', monospace !important;
    font-size: 13px !important;
    font-weight: 500 !important;
    cursor: pointer !important;
    transition: all 0.2s ease !important;
    text-decoration: none !important;
    display: inline-block !important;
    margin: 0 2px !important;
    pointer-events: auto !important;
    user-select: none !important;
    vertical-align: baseline !important;
    box-sizing: border-box !important;
    position: relative !important;
    z-index: 1 !important;
    
    // 支持长路径换行
    word-break: break-all !important;
    white-space: normal !important;
    max-width: 100% !important;
    line-height: 1.4 !important;
    
    // 确保没有额外的背景或边框
    outline: none !important;
    box-shadow: none !important;
    
    &:hover {
      background: rgba(24, 144, 255, 0.12) !important;
      border-color: rgba(24, 144, 255, 0.4) !important;
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15) !important;
      text-decoration: none !important;
    }
    
    &:active {
      transform: translateY(0) !important;
      box-shadow: 0 1px 4px rgba(24, 144, 255, 0.1) !important;
    }
    
    // 确保没有伪元素干扰
    &::before,
    &::after {
      display: none !important;
    }
  }
  
  // 确保在code标签内的路径链接也能正确显示
  :deep(code .route-link) {
    background: rgba(24, 144, 255, 0.08) !important;
    border: 1px solid rgba(24, 144, 255, 0.25) !important;
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .floating-ai-assistant {
    bottom: 16px;
    right: 16px;
  }

  .ai-chat-panel {
    width: calc(100vw - 32px);
    height: calc(100vh - 100px);
    max-width: 380px;
  }
}

/* 暗黑模式适配 */
[data-theme='dark'] {
  .floating-ai-assistant {
    /* 悬浮按钮暗黑适配 */
    .ai-button {
      background: #262626 !important;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;

      &::before {
        background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%) !important;
      }

      &:hover {
        box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4) !important;

        &::before {
          background: linear-gradient(135deg, #40a9ff 0%, #b37feb 100%) !important;
        }
      }

      .ai-head {
        background: #262626 !important;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3) !important;

        &::after {
          background: #262626 !important;
        }
      }

      .ai-text {
        color: #1890ff !important;
      }
    }

    /* 聊天面板暗黑适配 */
    .ai-chat-panel {
      background: #1f1f1f !important;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4) !important;
    }

    /* 聊天头部暗黑适配 */
    .chat-header {
      background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%) !important;

      &::before {
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.05), transparent) !important;
      }

      .header-head {
        background: rgba(31, 31, 31, 0.95) !important;

        &::after {
          background: rgba(31, 31, 31, 0.95) !important;
        }
      }

      .header-eye {
        background: rgba(24, 144, 255, 0.8) !important;
      }
    }

    /* 聊天消息区域暗黑适配 */
    .chat-messages {
      background: #1f1f1f !important;
    }

    /* 欢迎消息暗黑适配 */
    .welcome-card {
      h3 {
        color: rgba(255, 255, 255, 0.85) !important;
      }

      p {
        color: rgba(255, 255, 255, 0.65) !important;
      }
    }

    /* AI消息气泡暗黑适配 */
    .message.ai .message-content {
      background: #262626 !important;
      color: rgba(255, 255, 255, 0.85) !important;
    }

    /* 思考泡泡暗黑适配 */
    .thinking-bubble {
      background: linear-gradient(135deg, #262626 0%, #303030 100%) !important;
      border-color: rgba(24, 144, 255, 0.2) !important;

      &::before {
        background: linear-gradient(135deg, rgba(24, 144, 255, 0.1), rgba(114, 46, 209, 0.1)) !important;
      }
    }

    .thinking-text {
      color: rgba(255, 255, 255, 0.75) !important;
    }

    /* 输入区域暗黑适配 */
    .chat-input {
      background: #1f1f1f !important;
      border-top-color: #404040 !important;
    }

    .input-container {
      background: #262626 !important;
    }

    .message-input {
      color: rgba(255, 255, 255, 0.85) !important;

      &::placeholder {
        color: rgba(255, 255, 255, 0.45) !important;
      }
    }

    /* 图片预览暗黑适配 */
    .image-preview-modal {
      background: rgba(0, 0, 0, 0.9) !important;
    }

    .image-preview-content {
      background: #262626 !important;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6) !important;
    }

    /* 滚动条暗黑适配 */
    .chat-messages::-webkit-scrollbar-track {
      background: #262626 !important;
    }

    .chat-messages::-webkit-scrollbar-thumb {
      background: #404040 !important;
    }

    .chat-messages::-webkit-scrollbar-thumb:hover {
      background: #525252 !important;
    }

    /* Markdown 内容暗黑适配 */
    .markdown-content {
      :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
        color: rgba(255, 255, 255, 0.85) !important;
      }

      :deep(p) {
        color: rgba(255, 255, 255, 0.75) !important;
      }

      :deep(code) {
        background: rgba(24, 144, 255, 0.15) !important;
        border-color: rgba(24, 144, 255, 0.3) !important;
        color: #40a9ff !important;
      }

      :deep(pre) {
        background: #262626 !important;
        border-color: #404040 !important;

        code {
          color: rgba(255, 255, 255, 0.85) !important;
        }
      }

      :deep(blockquote) {
        border-left-color: #1890ff !important;
        background: rgba(24, 144, 255, 0.08) !important;
        color: rgba(255, 255, 255, 0.75) !important;
      }

      :deep(table) {
        border-color: #404040 !important;
      }

      :deep(th), :deep(td) {
        border-color: #404040 !important;
        color: rgba(255, 255, 255, 0.85) !important;
      }

      :deep(th) {
        background: #262626 !important;
      }

      :deep(hr) {
        border-top-color: #404040 !important;
      }

      :deep(a) {
        color: #40a9ff !important;
      }

      :deep(.route-link) {
        color: #40a9ff !important;
        background: rgba(24, 144, 255, 0.12) !important;
        border-color: rgba(24, 144, 255, 0.3) !important;

        &:hover {
          background: rgba(24, 144, 255, 0.18) !important;
          border-color: rgba(24, 144, 255, 0.5) !important;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2) !important;
        }
      }

      :deep(code .route-link) {
        background: rgba(24, 144, 255, 0.12) !important;
        border-color: rgba(24, 144, 255, 0.3) !important;
      }
    }

    /* 图片容器暗黑适配 */
    .message-image-container {
      background: rgba(255, 255, 255, 0.05) !important;
      border-color: rgba(255, 255, 255, 0.1) !important;

      &:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3) !important;
      }
    }

    /* 消息时间戳暗黑适配 */
    .message-time {
      color: rgba(255, 255, 255, 0.45) !important;
    }
  }
}