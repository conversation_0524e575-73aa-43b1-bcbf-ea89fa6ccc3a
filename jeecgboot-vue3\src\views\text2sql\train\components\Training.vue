<template>
  <div class="training-container">
    <a-row :gutter="[24, 24]">
      <a-col :span="24">
        <div class="page-header">
          <h1 class="page-title">
            <experiment-outlined /> {{ t('text2sql.train.training.title') }}
          </h1>
          <p class="page-description">{{ t('text2sql.train.training.description') }}</p>
        </div>
      </a-col>

      <!-- 一键训练区域 -->
      <a-col :span="24">
        <a-card :bordered="false" class="auto-train-card">
          <div class="auto-train-section">
            <div class="auto-train-content">
                             <div class="auto-train-info">
                 <h3>
                   <experiment-outlined /> {{ t('text2sql.train.training.autoTrain') }}
                 </h3>
                 <p>{{ t('text2sql.train.training.autoTrainDescription') }}</p>
                 <p class="auto-train-warning">{{ t('text2sql.train.training.autoTrainWarning') }}</p>
               </div>
              <a-button
                type="primary"
                size="large"
                @click="handleAutoTrain"
                :loading="autoTraining"
                danger
                class="auto-train-button"
              >
                <template #icon><experiment-outlined /></template>
                {{ t('text2sql.train.training.autoTrain') }}
              </a-button>
            </div>
          </div>
        </a-card>
      </a-col>

      <a-col :span="24">
        <a-card :bordered="false" class="training-card">
          <a-tabs v-model:activeKey="activeTab" class="custom-tabs">
            <a-tab-pane key="add" :tab="t('text2sql.train.training.add')">
              <div class="form-container">
                <a-form
                  :model="formState"
                  layout="vertical"
                  @finish="handleSubmit"
                  class="training-form"
                >
                  <a-form-item :label="t('text2sql.train.training.trainingType')">
                    <a-radio-group v-model:value="formState.type" button-style="solid">
                      <a-radio-button value="sql">
                        <database-outlined /> {{ t('text2sql.train.training.sqlQA') }}
                      </a-radio-button>
                      <a-radio-button value="ddl">
                        <table-outlined /> {{ t('text2sql.train.training.databaseStructure') }}
                      </a-radio-button>
                      <a-radio-button value="doc">
                        <file-text-outlined /> {{ t('text2sql.train.training.documentation') }}
                      </a-radio-button>
                    </a-radio-group>
                  </a-form-item>

                  <a-form-item
                    v-if="formState.type === 'sql'"
                    :label="t('text2sql.train.training.question')"
                    name="question"
                    :rules="[{ required: true, message: t('text2sql.train.training.validation.questionRequired') }]"
                  >
                    <a-input
                      v-model:value="formState.question"
                      :placeholder="t('text2sql.train.training.questionPlaceholder')"
                      :maxLength="100"
                      show-count
                      allow-clear
                    />
                  </a-form-item>

                  <a-form-item
                    v-if="formState.type === 'sql'"
                    :label="t('text2sql.train.training.sql')"
                    name="sql"
                    :rules="[{ required: true, message: t('text2sql.train.training.validation.sqlRequired') }]"
                  >
                    <a-textarea
                      v-model:value="formState.sql"
                      :placeholder="t('text2sql.train.training.sqlPlaceholder')"
                      :rows="8"
                      :maxLength="2000"
                      show-count
                      allow-clear
                      class="code-textarea"
                    />
                  </a-form-item>

                  <a-form-item
                    v-if="formState.type === 'ddl'"
                    :label="t('text2sql.train.training.ddl')"
                    name="ddl"
                    :rules="[{ required: true, message: t('text2sql.train.training.validation.ddlRequired') }]"
                  >
                    <a-textarea
                      v-model:value="formState.ddl"
                      :placeholder="t('text2sql.train.training.ddlPlaceholder')"
                      :rows="8"
                      :maxLength="2000"
                      show-count
                      allow-clear
                      class="code-textarea"
                    />
                  </a-form-item>

                  <a-form-item
                    v-if="formState.type === 'doc'"
                    :label="t('text2sql.train.training.docLabel')"
                    name="documentation"
                    :rules="[{ required: true, message: t('text2sql.train.training.validation.docRequired') }]"
                  >
                    <a-textarea
                      v-model:value="formState.documentation"
                      :placeholder="t('text2sql.train.training.docPlaceholder')"
                      :rows="8"
                      :maxLength="2000"
                      show-count
                      allow-clear
                    />
                  </a-form-item>

                  <a-form-item>
                    <a-space>
                      <a-button type="primary" html-type="submit" :loading="submitting">
                        <save-outlined /> {{ t('text2sql.train.training.submit') }}
                      </a-button>
                      <a-button @click="resetForm">
                        <clear-outlined /> {{ t('text2sql.train.training.reset') }}
                      </a-button>
                    </a-space>
                  </a-form-item>
                </a-form>

                <div class="form-tips">
                  <a-alert
                    :message="getFormTips"
                    type="info"
                    show-icon
                  />
                </div>
              </div>
            </a-tab-pane>

            <a-tab-pane key="view" :tab="t('text2sql.train.training.view')">
              <a-spin :spinning="loading">
                <div class="table-toolbar">
                  <a-space>
                    <a-button
                      type="primary"
                      @click="loadTrainingData"
                    >
                      <template #icon><reload-outlined /></template>
                      {{ t('text2sql.train.training.refreshData') }}
                    </a-button>

                    <a-input-search
                      v-model:value="searchText"
                      :placeholder="t('text2sql.train.training.searchTrainingData')"
                      style="width: 250px"
                      @search="onSearch"
                    />

                    <a-button
                      @click="handleReset"
                      :disabled="!searchText && filterType === 'all'"
                    >
                      <template #icon><clear-outlined /></template>
                      {{ t('text2sql.train.training.reset') }}
                    </a-button>
                  </a-space>

                  <a-radio-group v-model:value="filterType" button-style="solid" @change="onFilterChange">
                    <a-radio-button value="all">{{ t('text2sql.train.training.all') }}</a-radio-button>
                    <a-radio-button value="sql">SQL</a-radio-button>
                    <a-radio-button value="ddl">DDL</a-radio-button>
                    <a-radio-button value="doc">{{ t('text2sql.train.training.typeLabels.doc') }}</a-radio-button>
                  </a-radio-group>
                </div>

                <a-table
                  :dataSource="filteredTrainingData"
                  :columns="columns"
                  :scroll="{ x: 'max-content' }"
                  :pagination="pagination"
                  size="middle"
                  bordered
                  class="data-table"
                  :rowClassName="getRowClassName"
                  @change="handleTableChange"
                >
                  <template #bodyCell="{ column, record }">
                    <template v-if="column.key === 'question'">
                      <span v-if="record.question">
                        {{ record.question }}
                      </span>
                      <a-tag v-else color="orange">{{ t('text2sql.train.training.noQuestion') }}</a-tag>
                    </template>

                    <template v-if="column.key === 'content'">
                      <Paragraph
                        :ellipsis="{ rows: 2, expandable: true, symbol: t('text2sql.train.training.expand') }"
                        :content="record.content"
                      />
                    </template>

                    <template v-if="column.key === 'type'">
                      <a-tag
                        :color="getTypeColor(record.id)"
                        class="type-tag"
                      >
                        {{ getTypeLabel(record.id) }}
                      </a-tag>
                    </template>

                    <template v-if="column.key === 'action'">
                      <a-space>
                        <a-tooltip :title="t('text2sql.train.training.copyId')">
                          <a-button type="text" @click="copyId(record.id)">
                            <copy-outlined />
                          </a-button>
                        </a-tooltip>

                        <a-popconfirm
                          :title="t('text2sql.train.training.deleteConfirm')"
                          :ok-text="t('text2sql.train.training.confirm')"
                          :cancel-text="t('text2sql.train.training.cancel')"
                          @confirm="handleDelete(record.id)"
                        >
                          <a-button type="text" danger>
                            <delete-outlined />
                          </a-button>
                        </a-popconfirm>
                      </a-space>
                    </template>
                  </template>

                  <template #emptyText>
                    <a-empty :description="t('text2sql.train.training.empty')" />
                  </template>
                </a-table>
              </a-spin>
            </a-tab-pane>

            
          </a-tabs>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, h } from 'vue';
import { message, Typography, Modal } from 'ant-design-vue';
import { getTrainingData, removeTrainingData, train, autoTrainBySchema } from '@/api/text2sql';
import type { TrainingData } from '/@/views/text2sql/train/text2sqlTyping';
import { useI18n } from 'vue-i18n';
import {
  ExperimentOutlined,
  ReloadOutlined,
  DatabaseOutlined,
  TableOutlined,
  FileTextOutlined,
  CopyOutlined,
  DeleteOutlined,
  ClearOutlined,
  QuestionCircleOutlined,
  SaveOutlined
} from '@ant-design/icons-vue';

const { Paragraph } = Typography;
const { t } = useI18n();

// 状态
const activeTab = ref('add');
const loading = ref(false);
const submitting = ref(false);
const autoTraining = ref(false);
const trainingData = ref<TrainingData[]>([]);
const searchText = ref('');
const filterType = ref('all');

// 分页状态
const pagination = reactive({
  current: 1,
  pageSize: 10,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => 
    `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
  pageSizeOptions: ['10', '20', '50', '100'],
});

// 表单状态
const formState = reactive({
  type: 'sql',
  question: '',
  sql: '',
  ddl: '',
  documentation: '',
});

// 过滤后的训练数据
const filteredTrainingData = computed(() => {
  if (!trainingData.value || !Array.isArray(trainingData.value)) {
    return [];
  }

  let result = [...trainingData.value];

  // 按类型过滤
  if (filterType.value !== 'all') {
    result = result.filter(item => {
      const type = getTypeFromId(item.id);
      return type === filterType.value;
    });
  }

  // 按搜索文本过滤
  if (searchText.value) {
    const searchLower = searchText.value.toLowerCase();
    result = result.filter(item => {
      if (!item) return false;

      return (
        (item.question && item.question.toLowerCase().includes(searchLower)) ||
        (item.content && item.content.toLowerCase().includes(searchLower)) ||
        (item.id && item.id.toLowerCase().includes(searchLower))
      );
    });
  }

  return result;
});

// 表格列定义
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
    width: 220,
    ellipsis: true,
  },
  {
    title: t('text2sql.train.training.type'),
    key: 'type',
    width: 100,
  },
  {
    title: t('text2sql.train.training.question'),
    dataIndex: 'question',
    key: 'question',
    ellipsis: true,
  },
  {
    title: t('text2sql.train.training.content'),
    dataIndex: 'content',
    key: 'content',
    ellipsis: true,
  },
  {
    title: t('text2sql.train.training.actions'),
    key: 'action',
    width: 100,
    fixed: 'right',
  },
];

// 从 ID 中获取类型
const getTypeFromId = (id: string): string => {
  if (!id) return 'unknown';

  if (id.endsWith('-sql')) return 'sql';
  if (id.endsWith('-ddl')) return 'ddl';
  if (id.endsWith('-doc')) return 'doc';
  return 'unknown';
};

// 获取类型标签
const getTypeLabel = (id: string): string => {
  const type = getTypeFromId(id);
  return t(`text2sql.train.training.typeLabels.${type}`);
};

// 获取类型颜色
const getTypeColor = (id: string): string => {
  const type = getTypeFromId(id);
  switch (type) {
    case 'sql': return 'blue';
    case 'ddl': return 'purple';
    case 'doc': return 'green';
    default: return 'default';
  }
};

// 获取行类名
const getRowClassName = (record: TrainingData): string => {
  const type = getTypeFromId(record.id);
  return `row-${type}`;
};

// 搜索处理
const onSearch = (value: string) => {
  searchText.value = value;
  // 重置分页到第一页
  pagination.current = 1;
};

// 类型过滤处理
const onFilterChange = () => {
  // 重置分页到第一页
  pagination.current = 1;
};

// 搜索变化处理
const onSearchChange = () => {
  // 重置分页到第一页
  pagination.current = 1;
};

// 分页变化处理
const handleTableChange = (paginationInfo: any) => {
  pagination.current = paginationInfo.current;
  pagination.pageSize = paginationInfo.pageSize;
};

// 重置搜索和筛选
const handleReset = () => {
  searchText.value = '';
  filterType.value = 'all';
  pagination.current = 1;
  message.success('已重置搜索条件');
};

// 复制 ID
const copyId = async (id: string) => {
  try {
    await navigator.clipboard.writeText(id);
    message.success(t('text2sql.train.training.messages.idCopied'));
  } catch (err) {
    console.error('复制失败:', err);
    message.error(t('text2sql.train.training.messages.copyFailed'));
  }
};

// 重置表单
const resetForm = () => {
  formState.question = '';
  formState.sql = '';
  formState.ddl = '';
  formState.documentation = '';
  // message.info('表单已重置');
};

// 获取表单提示
const getFormTips = computed(() => {
  return t(`text2sql.train.training.tips.${formState.type}`, {}, t('text2sql.train.training.tips.default'));
});

// 加载训练数据
const loadTrainingData = async () => {
  loading.value = true;
  try {
    const response = await getTrainingData();
    trainingData.value = response.df || [];
    // 重置分页到第一页
    pagination.current = 1;
    message.success(t('text2sql.train.training.messages.dataLoadSuccess', { count: trainingData.value.length }));
  } catch (error) {
    console.error('加载训练数据失败:', error);
    message.error(t('text2sql.train.training.messages.loadDataFailed'));
  } finally {
    loading.value = false;
  }
};

// 删除训练数据
const handleDelete = async (id: string) => {
  loading.value = true;
  try {
    await removeTrainingData({ id });
    message.success(t('text2sql.train.training.messages.deleteSuccess'));
    await loadTrainingData();
  } catch (error) {
    console.error('删除训练数据失败:', error);
    message.error(t('text2sql.train.training.messages.deleteFailed'));
  } finally {
    loading.value = false;
  }
};

// 提交表单
const handleSubmit = async () => {
  submitting.value = true;
  try {
    const data = {
      question: formState.type === 'sql' ? formState.question : undefined,
      sql: formState.type === 'sql' ? formState.sql : undefined,
      ddl: formState.type === 'ddl' ? formState.ddl : undefined,
      documentation: formState.type === 'doc' ? formState.documentation : undefined,
    };

    await train(data);
    message.success(t('text2sql.train.training.messages.addSuccess'));

    // 重置表单
    resetForm();

    // 切换到查看标签页并刷新数据
    activeTab.value = 'add';
    await loadTrainingData();
  } catch (error) {
    console.error('添加训练数据失败:', error);
    message.error(t('text2sql.train.training.messages.addFailed'));
  } finally {
    submitting.value = false;
  }
};

// 一键训练确认对话框
const showAutoTrainConfirm = () => {
  Modal.confirm({
    title: t('text2sql.train.training.autoTrainConfirmTitle'),
    content: t('text2sql.train.training.autoTrainConfirmContent'),
    okText: t('text2sql.train.training.confirm'),
    cancelText: t('text2sql.train.training.cancel'),
    okType: 'danger',
    icon: () => h('span', { style: 'color: #ff4d4f; font-size: 18px;' }, '⚠️'),
    onOk: () => {
      return executeAutoTrain();
    },
    okButtonProps: {
      loading: autoTraining.value,
    },
  });
};

// 执行一键训练
const executeAutoTrain = async () => {
  autoTraining.value = true;
  try {
    message.info(t('text2sql.train.training.messages.autoTrainStart'));
    const response = await autoTrainBySchema();
    if (response.result === 'success') {
      message.success(t('text2sql.train.training.messages.autoTrainSuccess'));
      // 刷新训练数据
      await loadTrainingData();
    } else {
      message.error(t('text2sql.train.training.messages.autoTrainFailed'));
    }
  } catch (error) {
    console.error('一键训练失败:', error);
    message.error(t('text2sql.train.training.messages.autoTrainFailed'));
  } finally {
    autoTraining.value = false;
  }
};

// 一键训练（显示确认对话框）
const handleAutoTrain = () => {
  showAutoTrainConfirm();
};

// 组件挂载时加载训练数据
onMounted(async () => {
  await loadTrainingData();
});
</script>

<style scoped>
.training-container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--heading-color);
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-description {
  color: var(--text-color-secondary);
  font-size: 16px;
  margin-bottom: 0;
}

.training-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.custom-tabs :deep(.ant-tabs-nav) {
  margin-bottom: 24px;
}

.custom-tabs :deep(.ant-tabs-tab) {
  font-size: 16px;
  padding: 12px 16px;
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  flex-wrap: wrap;
  gap: 16px;
}

.data-table {
  margin-top: 16px;
}

.type-tag {
  min-width: 60px;
  text-align: center;
}

/* 行样式 */
.row-sql {
  background-color: rgba(24, 144, 255, 0.05);
}

.row-ddl {
  background-color: rgba(114, 46, 209, 0.05);
}

.row-doc {
  background-color: rgba(82, 196, 26, 0.05);
}

.form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.training-form {
  max-width: 800px;
  margin: 0 auto;
}

.code-textarea {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

.form-tips {
  max-width: 800px;
  margin: 0 auto;
}

/* 一键训练区域样式 */
.auto-train-card {
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.15);
  background: linear-gradient(135deg, #ffffff 0%, #f0f7ff 100%);
  border: 1px solid rgba(24, 144, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.auto-train-card::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(24, 144, 255, 0.03) 0%, transparent 60%);
  animation: pulse 15s infinite linear;
  z-index: 0;
}

.auto-train-card :deep(.ant-card-body) {
  padding: 24px;
  position: relative;
  z-index: 1;
}

.auto-train-section {
  color: #333;
}

.auto-train-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
}

.auto-train-info h3 {
  color: #1890ff;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(to right, #1890ff, #36cfc9);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.auto-train-info p {
  color: rgba(51, 51, 51, 0.8);
  margin-bottom: 0;
  font-size: 14px;
  line-height: 1.5;
}

.auto-train-warning {
  color: #ff7a00 !important;
  font-weight: 500;
  margin-top: 8px !important;
  font-size: 13px !important;
  display: flex;
  align-items: center;
  gap: 4px;
}

.auto-train-button {
  flex-shrink: 0;
  height: 48px;
  padding: 0 24px;
  font-size: 16px;
  font-weight: 500;
  border-radius: 24px;
  border: 1px solid rgba(24, 144, 255, 0.3);
  background: linear-gradient(90deg, #1890ff, #36cfc9);
  color: white;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.auto-train-button:hover {
  background: linear-gradient(90deg, #40a9ff, #5cdbd3);
  border-color: rgba(24, 144, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
  color: white;
}

.auto-train-button.ant-btn-loading {
  background: linear-gradient(90deg, #69c0ff, #87e8de);
  color: white;
}

@keyframes pulse {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-toolbar {
    flex-direction: column;
    align-items: flex-start;
  }

  .custom-tabs :deep(.ant-tabs-tab) {
    padding: 8px 12px;
    font-size: 14px;
  }

  .auto-train-content {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .auto-train-button {
    width: 100%;
  }
}

/* 暗黑模式样式 */
[data-theme='dark'] .training-container {
  .page-title {
    color: rgba(255, 255, 255, 0.85);
  }

  .page-description {
    color: rgba(255, 255, 255, 0.45);
  }

  .training-card {
    background: #262626;
    border-color: #404040;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  /* 行样式暗黑模式 */
  .row-sql {
    background-color: rgba(24, 144, 255, 0.1);
  }

  .row-ddl {
    background-color: rgba(114, 46, 209, 0.1);
  }

  .row-doc {
    background-color: rgba(82, 196, 26, 0.1);
  }

  /* 表格样式 */
  .data-table {
    .ant-table {
      background: #262626;

      .ant-table-thead > tr > th {
        background: #1a1a1a;
        border-bottom-color: #404040;
        color: rgba(255, 255, 255, 0.85);
      }

      .ant-table-tbody > tr > td {
        border-bottom-color: #303030;
        color: rgba(255, 255, 255, 0.65);
        background: #262626;
      }

      .ant-table-tbody > tr:hover > td {
        background: #303030;
      }

      .ant-table-tbody > tr.row-sql > td {
        background: rgba(24, 144, 255, 0.1);
      }

      .ant-table-tbody > tr.row-ddl > td {
        background: rgba(114, 46, 209, 0.1);
      }

      .ant-table-tbody > tr.row-doc > td {
        background: rgba(82, 196, 26, 0.1);
      }

      .ant-table-tbody > tr.row-sql:hover > td {
        background: rgba(24, 144, 255, 0.15);
      }

      .ant-table-tbody > tr.row-ddl:hover > td {
        background: rgba(114, 46, 209, 0.15);
      }

      .ant-table-tbody > tr.row-doc:hover > td {
        background: rgba(82, 196, 26, 0.15);
      }
    }
  }

  /* 标签样式 */
  .type-tag {
    background: #1a1a1a;
    border-color: #404040;
    color: rgba(255, 255, 255, 0.65);
  }

  /* 统计卡片 */
  .stats-card {
    background: #262626;
    border-color: #404040;

    .stats-title {
      color: rgba(255, 255, 255, 0.85);
    }

    .stats-number {
      color: #1890ff;
    }

    .stats-description {
      color: rgba(255, 255, 255, 0.45);
    }
  }

  /* 自动训练区域 */
  .auto-train-card {
    background: #262626;
    border-color: #404040;

    .auto-train-title {
      color: rgba(255, 255, 255, 0.85);
    }

    .auto-train-description {
      color: rgba(255, 255, 255, 0.65);
    }

    .auto-train-status {
      color: rgba(255, 255, 255, 0.45);
    }
  }

  /* 进度条 */
  .ant-progress {
    .ant-progress-bg {
      background: #1890ff;
    }

    .ant-progress-text {
      color: rgba(255, 255, 255, 0.85);
    }
  }

  /* 输入框和选择器 */
  .ant-input,
  .ant-select-selector,
  .ant-textarea {
    background: #1a1a1a !important;
    border-color: #404040 !important;
    color: rgba(255, 255, 255, 0.85) !important;

    &:hover {
      border-color: #1890ff !important;
    }

    &:focus,
    &.ant-select-focused .ant-select-selector {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
    }
  }

  .ant-input::placeholder,
  .ant-textarea::placeholder {
    color: rgba(255, 255, 255, 0.45) !important;
  }

  .ant-select-selection-placeholder {
    color: rgba(255, 255, 255, 0.45) !important;
  }

  .ant-select-selection-item {
    color: rgba(255, 255, 255, 0.85) !important;
  }

  .ant-select-arrow {
    color: rgba(255, 255, 255, 0.45) !important;
  }

  /* 按钮样式 */
  .ant-btn {
    &:not(.ant-btn-primary):not(.ant-btn-danger) {
      background: #262626;
      border-color: #404040;
      color: rgba(255, 255, 255, 0.65);

      &:hover {
        background: #303030;
        border-color: #1890ff;
        color: #1890ff;
      }
    }
  }
}
</style>
