/* 暗黑模式适配 */
[data-theme='dark'] .database-knowledge-graph-container {
  background: #1f1f1f;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

  .graph-header {
    background: linear-gradient(90deg, #262626 0%, #1f1f1f 100%);
    border-bottom-color: #404040;

    .graph-title {
      color: #1890ff;
    }

    .connection-name {
      color: #a6a6a6;
    }
  }

  .graph-controls {
    background: rgba(31, 31, 31, 0.95);
    border-color: rgba(64, 64, 64, 0.2);
  }

  .table-detail-popup {
    background: rgba(31, 31, 31, 0.98);
    border-color: rgba(64, 64, 64, 0.4);
    color: #ffffff;

    .popup-header h4 {
      color: #ffffff;
    }

    .popup-content {
      color: #a6a6a6;
    }

    .columns-section h5,
    .relationships-section h5 {
      color: #ffffff;
    }

    .column-item {
      background: #262626;
      border-color: #404040;
    }

    .column-name {
      color: #ffffff;
    }

    .column-type {
      color: #a6a6a6;
    }

    .column-constraints {
      color: #8c8c8c;
    }

    .relation-item {
      background: #262626;
      border-color: #404040;
    }

    .relation-table {
      color: #1890ff;
    }

    .relation-current {
      color: #ffffff;
    }

    .relation-detail {
      color: #8c8c8c;
    }
  }

  .graph-footer {
    background: #262626;
    border-top-color: #404040;
  }

  .stat-label {
    color: #a6a6a6;
  }

  .stat-value {
    color: #ffffff;
  }
}

.database-knowledge-graph-container {
                     height: 100%;
                     min-height: 500px;
                     display: flex;
                     flex-direction: column;
                     background: #fff;
                     border-radius: 16px;
                     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

                     .graph-header {
                       display: flex;
                       justify-content: space-between;
                       align-items: center;
                       padding: 10px 14px;
                       border-bottom: 1px solid #f0f0f0;
                       background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
                       flex-shrink: 0;

                       .header-left {
                         display: flex;
                         align-items: center;
                         gap: 12px;

                         .graph-title {
                           margin: 0;
                           display: flex;
                           align-items: center;
                           gap: 6px;
                           color: #1890ff;
                           font-size: 13px;
                           font-weight: 600;

                           .title-icon {
                             font-size: 14px;
                           }
                         }

                         .connection-info {
                           display: flex;
                           align-items: center;
                           gap: 6px;

                           .connection-name {
                             color: #666;
                             font-size: 12px;
                           }
                         }
                       }

                       .header-right {
                         .ant-btn-primary {
                           // 与KnowledgeGraph3D保持一致的主色调渐变效果
                           background: linear-gradient(135deg, #1890ff, #40a9ff) !important;
                           border: none !important;
                           transition: all 0.3s ease;
                           
                           &:hover {
                             background: linear-gradient(135deg, #096dd9, #1890ff) !important;
                             transform: translateY(-1px);
                             box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
                           }
                           
                           &:focus {
                             background: linear-gradient(135deg, #1890ff, #40a9ff) !important;
                             box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                           }
                           
                           &:active {
                             transform: translateY(0);
                           }
                         }
                       }
                     }

                     .graph-container {
                       flex: 1;
                       position: relative;
                       overflow: hidden;
                       min-height: 400px;

                       .graph-view {
                         width: 100%;
                         height: 100%;
                         min-height: 400px;
                       }

                       .loading-container {
                         position: absolute;
                         top: 50%;
                         left: 50%;
                         transform: translate(-50%, -50%);
                         z-index: 10;
                       }

                       .empty-container {
                         position: absolute;
                         top: 50%;
                         left: 50%;
                         transform: translate(-50%, -50%);
                         z-index: 10;
                       }

                       .graph-controls {
                         position: absolute;
                         bottom: 20px;
                         right: 60px;
                         z-index: 100;
                         background: rgba(255, 255, 255, 0.95);
                         border-radius: 20px;
                         box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                         padding: 6px 12px;
                         backdrop-filter: blur(8px);
                         border: 1px solid rgba(255, 255, 255, 0.2);

                         .control-buttons {
                           display: flex;
                           gap: 8px;
                           align-items: center;

                           .ant-btn {
                             width: 32px;
                             height: 32px;
                             display: flex;
                             align-items: center;
                             justify-content: center;
                             border: none;
                             border-radius: 6px;
                             transition: all 0.3s ease;

                             .anticon {
                               font-size: 14px;
                             }

                             // 与KnowledgeGraph3D保持一致的主色调渐变效果
                             &.ant-btn-primary {
                               background: linear-gradient(135deg, #1890ff, #40a9ff);
                               
                               &:hover {
                                 background: linear-gradient(135deg, #096dd9, #1890ff);
                                 transform: translateY(-1px);
                                 box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
                               }
                               
                               &:focus {
                                 box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                               }
                               
                               &:active {
                                 transform: translateY(0);
                               }
                             }
                           }
                         }
                       }

                       .table-detail-panel {
                         position: absolute;
                         width: 350px;
                         max-height: 500px;
                         background: white;
                         border-radius: 8px;
                         box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
                         z-index: 1000;
                         overflow: hidden;
                         border: 1px solid #e8e8e8;

                         .table-detail-header {
                           display: flex;
                           justify-content: space-between;
                           align-items: center;
                           padding: 12px 16px;
                           background: #f8f9fa;
                           border-bottom: 1px solid #e8e8e8;

                           .table-title {
                             display: flex;
                             align-items: center;
                             gap: 8px;

                             .table-type-indicator {
                               width: 12px;
                               height: 12px;
                               background: #4CAF50;
                               border-radius: 2px;
                             }

                             h4 {
                               margin: 0;
                               color: #333;
                               font-size: 14px;
                               font-weight: 600;
                             }
                           }
                         }

                         .table-detail-content {
                           padding: 16px;
                           max-height: 400px;
                           overflow-y: auto;

                           .columns-section {
                             margin-top: 16px;

                             h5 {
                               margin: 0 0 12px 0;
                               color: #333;
                               font-size: 13px;
                               font-weight: 600;
                               border-bottom: 1px solid #f0f0f0;
                               padding-bottom: 4px;
                             }

                             .columns-list {
                               .column-item {
                                 margin-bottom: 8px;
                                 padding: 8px;
                                 background: #f8f9fa;
                                 border-radius: 4px;
                                 border-left: 3px solid #4CAF50;

                                 .column-header {
                                   display: flex;
                                   align-items: center;
                                   gap: 8px;
                                   margin-bottom: 4px;

                                   .column-name {
                                     font-weight: 600;
                                     color: #333;
                                   }
                                 }

                                 .column-details {
                                   display: flex;
                                   gap: 12px;
                                   font-size: 12px;
                                   color: #666;

                                   .column-type {
                                     font-family: 'Courier New', monospace;
                                     background: #e8e8e8;
                                     padding: 2px 6px;
                                     border-radius: 3px;
                                   }
                                 }
                               }
                             }
                           }

                           .relationships-section {
                             margin-top: 16px;

                             h5 {
                               margin: 0 0 12px 0;
                               color: #333;
                               font-size: 13px;
                               font-weight: 600;
                               border-bottom: 1px solid #f0f0f0;
                               padding-bottom: 4px;
                             }

                             .relation-group {
                               margin-bottom: 12px;

                               h6 {
                                 margin: 0 0 8px 0;
                                 color: #666;
                                 font-size: 12px;
                                 font-weight: 500;
                               }

                               .relation-item {
                                 display: flex;
                                 align-items: center;
                                 gap: 8px;
                                 margin-bottom: 6px;
                                 font-size: 12px;

                                 .relation-table {
                                   color: #1890ff;
                                   cursor: pointer;
                                   text-decoration: underline;

                                   &:hover {
                                     color: #40a9ff;
                                   }
                                 }

                                 .relation-current {
                                   color: #52c41a;
                                   font-weight: 600;
                                 }

                                 .relation-arrow {
                                   color: #999;
                                 }

                                 .relation-detail {
                                   font-size: 11px;
                                   color: #999;
                                   font-family: 'Courier New', monospace;
                                   background: #f0f0f0;
                                   padding: 2px 4px;
                                   border-radius: 2px;
                                   margin-left: auto;
                                 }
                               }
                             }
                           }
                         }
                       }
                     }

                     .graph-footer {
                       padding: 8px 16px;
                       border-top: 1px solid #f0f0f0;
                       background: #fafafa;

                       .statistics {
                         display: flex;
                         gap: 20px;

                         .stat-item {
                           display: flex;
                           align-items: center;
                           gap: 4px;

                           .stat-label {
                             color: #666;
                             font-size: 12px;
                           }

                           .stat-value {
                             color: #1890ff;
                             font-weight: 600;
                             font-size: 13px;
                           }
                         }
                       }
                     }
                   }

// 全屏模式样式
:global(.database-knowledge-graph-container:fullscreen) {
  .graph-container {
    .table-detail-panel {
      position: fixed;
    }
  }
}

/* 暗黑模式样式 */
[data-theme='dark'] .database-knowledge-graph-container {
  background: #1f1f1f;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

  .graph-header {
    background: linear-gradient(90deg, #262626 0%, #303030 100%);
    border-bottom-color: #404040;

    .header-left {
      .graph-title {
        color: #40a9ff;
      }

      .connection-info {
        .connection-name {
          color: rgba(255, 255, 255, 0.65);
        }
      }
    }

    .header-right {
      .control-button {
        background: #1a1a1a;
        border-color: #404040;
        color: rgba(255, 255, 255, 0.65);

        &:hover {
          background: #303030;
          border-color: #1890ff;
          color: #1890ff;
        }

        &.active {
          background: #1890ff;
          border-color: #1890ff;
          color: white;
        }
      }
    }
  }

  .graph-container {
    background: #1a1a1a;

    .graph-canvas {
      background: #1a1a1a;
    }

    .loading-overlay {
      background: rgba(26, 26, 26, 0.8);

      .loading-content {
        background: #262626;
        border-color: #404040;
        color: rgba(255, 255, 255, 0.85);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }
    }

    .empty-state {
      color: rgba(255, 255, 255, 0.45);

      .empty-icon {
        color: #404040;
      }

      .empty-title {
        color: rgba(255, 255, 255, 0.65);
      }

      .empty-description {
        color: rgba(255, 255, 255, 0.45);
      }
    }

    .table-detail-panel {
      background: #262626;
      border-color: #404040;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

      .panel-header {
        background: #1a1a1a;
        border-bottom-color: #404040;

        .panel-title {
          color: rgba(255, 255, 255, 0.85);
        }

        .close-button {
          color: rgba(255, 255, 255, 0.65);

          &:hover {
            color: #ff4d4f;
            background: rgba(255, 77, 79, 0.1);
          }
        }
      }

      .panel-content {
        background: #262626;

        .table-info {
          .info-item {
            border-bottom-color: #404040;

            .info-label {
              color: rgba(255, 255, 255, 0.65);
            }

            .info-value {
              color: rgba(255, 255, 255, 0.85);
            }
          }
        }

        .fields-list {
          .field-item {
            background: #1a1a1a;
            border-color: #404040;

            &:hover {
              background: #303030;
              border-color: #1890ff;
            }

            .field-info {
              .field-name {
                color: rgba(255, 255, 255, 0.85);
              }

              .field-type {
                color: rgba(255, 255, 255, 0.65);
              }

              .field-constraints {
                .constraint-tag {
                  background: #1a1a1a;
                  border-color: #404040;
                  color: rgba(255, 255, 255, 0.65);

                  &.primary-key {
                    background: rgba(245, 34, 45, 0.15);
                    border-color: #ff7875;
                    color: #ff7875;
                  }

                  &.foreign-key {
                    background: rgba(24, 144, 255, 0.15);
                    border-color: #40a9ff;
                    color: #40a9ff;
                  }

                  &.unique {
                    background: rgba(250, 173, 20, 0.15);
                    border-color: #ffc53d;
                    color: #ffc53d;
                  }

                  &.not-null {
                    background: rgba(82, 196, 26, 0.15);
                    border-color: #73d13d;
                    color: #73d13d;
                  }
                }
              }
            }
          }
        }

        .relationships-list {
          .relationship-item {
            background: #1a1a1a;
            border-color: #404040;

            &:hover {
              background: #303030;
              border-color: #1890ff;
            }

            .relationship-info {
              .relationship-type {
                color: rgba(255, 255, 255, 0.85);
              }

              .relationship-target {
                color: rgba(255, 255, 255, 0.65);
              }
            }
          }
        }

        .statistics-grid {
          .stat-item {
            background: #1a1a1a;
            border-color: #404040;

            .stat-label {
              color: rgba(255, 255, 255, 0.65);
            }

            .stat-value {
              color: #40a9ff;
            }
          }
        }
      }
    }
  }
}
