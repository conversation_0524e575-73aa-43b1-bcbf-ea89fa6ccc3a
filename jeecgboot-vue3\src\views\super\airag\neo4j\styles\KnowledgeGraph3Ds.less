@import './common.less';

.knowledge-graph-container {
  height: 100%;

  .card-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .search-area {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 16px;
    margin-bottom: 16px;
    padding: 16px;
    background-color: #fafafa;
    border-radius: @radius-normal;
    border: 1px solid @border-color-light;

    .search-field {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      .search-label {
        white-space: nowrap;
        margin-right: 8px;
        min-width: 70px;
        text-align: right;
        color: @text-color-secondary;
        font-weight: 500;
      }

      .search-input {
        width: 180px;
        transition: all @animation-duration ease;
        
        &:focus {
          .card-shadow();
        }
        
        @media (max-width: 768px) {
          width: 140px;
        }
      }
    }

    .search-buttons {
      margin-left: auto;
      display: flex;
      gap: 8px;
      
      .ant-btn {
        .hover-lift();
        
        &.ant-btn-primary {
          background: linear-gradient(135deg, @primary-color, lighten(@primary-color, 10%));
          border: none;
          
          &:hover {
            background: linear-gradient(135deg, darken(@primary-color, 5%), @primary-color);
          }
        }
      }
    }

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: stretch;
      
      .search-buttons {
        margin-left: 0;
        justify-content: center;
      }
    }
  }

  .graph-main-container {
    height: calc(100vh - 250px);
    min-height: 500px;
    flex: 1;
    overflow: hidden;
    position: relative;
  }

  .graph-container {
    .graph-container-base();
    .graph-controls-style();
    border: 1px solid @border-color-light;
    border-radius: @radius-small;
  }

  .graph-footer {
    margin-top: 16px;
    padding: 12px 16px;
    background-color: #fafafa;
    border-radius: @radius-normal;
    border: 1px solid @border-color-light;

    .statistics-container {
      display: flex;
      gap: 24px;
      justify-content: center;

      .statistic-item {
        text-align: center;
        
        .statistic-label {
          font-weight: 500;
          color: @text-color-secondary;
          margin-bottom: 4px;
          display: block;
          font-size: 12px;
        }

        .statistic-value {
          color: @primary-color;
          font-size: 18px;
          font-weight: bold;
          display: block;
        }
        
        &:not(:last-child)::after {
          content: '';
          position: absolute;
          right: -12px;
          top: 50%;
          transform: translateY(-50%);
          width: 1px;
          height: 20px;
          background-color: @border-color;
        }
        
        position: relative;
      }
      
      @media (max-width: 768px) {
        flex-direction: column;
        gap: 12px;
        
        .statistic-item::after {
          display: none;
        }
      }
    }
  }
}

// 统计信息悬浮窗样式
.statistics-floating-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid @border-color-light;
  border-radius: @radius-normal;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  padding: 12px;
  min-width: 140px;
  font-size: 12px;
  transition: all @animation-duration ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
  
  .statistics-header {
    font-weight: 600;
    color: @text-color;
    margin-bottom: 8px;
    font-size: 13px;
    text-align: center;
    border-bottom: 1px solid @border-color-light;
    padding-bottom: 6px;
  }
  
  .statistics-content {
    .statistic-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 6px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .statistic-icon {
        font-size: 14px;
        margin-right: 6px;
      }
      
      .statistic-label {
        color: @text-color-secondary;
        font-weight: 500;
        flex: 1;
      }
      
      .statistic-value {
        color: @primary-color;
        font-weight: bold;
        font-size: 13px;
      }
    }
  }
  
  // 在较小的屏幕上调整位置
  @media (max-width: 768px) {
    top: 10px;
    left: 10px;
    min-width: 120px;
    font-size: 11px;
    
    .statistics-header {
      font-size: 12px;
    }
    
    .statistics-content .statistic-item {
      .statistic-icon {
        font-size: 12px;
      }
      
      .statistic-value {
        font-size: 12px;
      }
    }
  }
}

:global(.node-label) {
  .label-styles();
}

:global(.link-label) {
  .label-styles();
}

// ========== 暗黑模式适配 ==========
[data-theme='dark'] {
  .knowledge-graph-container {
    background: @dark-bg-primary;
    color: @dark-text-color;

    .card-container {
      background: @dark-bg-primary !important;
      border-color: @dark-border-color !important;

      :deep(.ant-card-body) {
        background: @dark-bg-primary !important;
        color: @dark-text-color !important;
      }
    }

    .search-area {
      background-color: @dark-bg-secondary;
      border: 1px solid @dark-border-color;

      .search-field {
        .search-label {
          color: @dark-text-color-secondary;
        }

        .search-input {
          &.ant-input {
            background: @dark-bg-tertiary;
            border-color: @dark-border-color;
            color: @dark-text-color;

            &:hover {
              border-color: @primary-color;
            }

            &:focus {
              border-color: @primary-color;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            }

            &::placeholder {
              color: @dark-text-color-light;
            }
          }

          &.ant-select {
            .ant-select-selector {
              background: @dark-bg-tertiary !important;
              border-color: @dark-border-color !important;
              color: @dark-text-color !important;

              &:hover {
                border-color: @primary-color !important;
              }
            }

            &.ant-select-focused .ant-select-selector {
              border-color: @primary-color !important;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
            }

            .ant-select-selection-placeholder {
              color: @dark-text-color-light !important;
            }

            .ant-select-selection-item {
              color: @dark-text-color !important;
            }

            .ant-select-arrow {
              color: @dark-text-color-light !important;
            }
          }

          &.ant-slider {
            .ant-slider-rail {
              background: @dark-bg-tertiary;
            }

            .ant-slider-track {
              background: @primary-color;
            }

            .ant-slider-handle {
              border-color: @primary-color;
              background: @primary-color;

              &:hover {
                border-color: lighten(@primary-color, 10%);
              }
            }
          }
        }
      }

      .search-buttons {
        .ant-btn {
          &:not(.ant-btn-primary) {
            background: @dark-bg-tertiary;
            border-color: @dark-border-color;
            color: @dark-text-color;

            &:hover {
              background: @dark-bg-primary;
              border-color: @primary-color;
              color: @primary-color;
            }
          }
        }
      }
    }

    .graph-main-container {
      .graph-container {
        .graph-container-base();
        .graph-controls-style();
        border: 1px solid @dark-border-color;
        border-radius: @radius-normal;
        background: @dark-bg-primary;
      }
    }

    .node-detail-panel {
      .entity-detail-base();

      .ant-descriptions {
        .ant-descriptions-item-label {
          color: @dark-text-color-secondary !important;
        }

        .ant-descriptions-item-content {
          color: @dark-text-color !important;
        }
      }

      .node-relations {
        .relation-item {
          background: @dark-bg-secondary;
          border: 1px solid @dark-border-color;
          color: @dark-text-color;

          &:hover {
            background: @dark-bg-tertiary;
            border-color: @primary-color;
          }
        }
      }
    }

    .statistics-floating-panel {
      background: @dark-bg-translucent;
      backdrop-filter: blur(10px);
      border: 1px solid @dark-border-color;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
      color: @dark-text-color;

      &:hover {
        background: rgba(38, 38, 38, 0.98);
        box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
      }

      .statistics-header {
        color: @dark-text-color;
        border-bottom: 1px solid @dark-border-color;
      }

      .statistics-content {
        .statistic-item {
          color: @dark-text-color-secondary;

          .statistic-icon {
            color: @primary-color;
          }

          .statistic-value {
            color: @dark-text-color;
          }

          .statistic-label {
            color: @dark-text-color-secondary;
          }
        }
      }
    }
  }

  // 全局标签样式暗黑模式
  :global(.node-label) {
    .label-styles();
  }

  :global(.link-label) {
    .label-styles();
  }
}