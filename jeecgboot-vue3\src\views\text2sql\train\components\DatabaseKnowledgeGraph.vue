<!-- 数据库知识图谱组件 -->
<template>
  <div class="database-knowledge-graph-container">
    <div class="graph-header">
      <div class="header-left">
        <h3 class="graph-title">
          <PartitionOutlined class="title-icon" />
          {{ t('text2sql.train.knowledgeGraph.title') }}
        </h3>
        <div class="connection-info" v-if="connectionInfo">
          <a-tag :color="getDbTypeColor(getDbTypeName(connectionInfo.db_type))">{{ getDbTypeName(connectionInfo.db_type) }}</a-tag>
          <span class="connection-name">{{ connectionInfo.name }}</span>
        </div>
      </div>
      <div class="header-right">
        <a-space>
          <a-button type="primary" size="small" @click="refreshGraph" :loading="loading">
            <ReloadOutlined />
            {{ t('text2sql.train.knowledgeGraph.reload') }}
          </a-button>
        </a-space>
      </div>
    </div>

    <div class="graph-container" ref="graphContainerRef">
      <div ref="graphRef" class="graph-view"></div>
      
      <div v-if="loading" class="loading-container">
        <a-spin :tip="t('text2sql.train.knowledgeGraph.loading')" size="large" />
      </div>
      
              <div v-if="!loading && (!graphData.nodes || graphData.nodes.length === 0)" class="empty-container">
        <a-empty :description="t('text2sql.train.knowledgeGraph.noData')">
          <template #image>
            <PartitionOutlined style="font-size: 48px; color: #d9d9d9;" />
          </template>
        </a-empty>
      </div>

      <!-- 图谱控制按钮 -->
      <div class="graph-controls" v-if="graphData.nodes && graphData.nodes.length > 0">
        <div class="control-buttons">
          <a-tooltip :title="t('text2sql.train.knowledgeGraph.controls.zoomIn')">
            <a-button type="primary" size="small" shape="circle" @click="zoomIn">
              <PlusOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('text2sql.train.knowledgeGraph.controls.zoomOut')">
            <a-button type="primary" size="small" shape="circle" @click="zoomOut">
              <MinusOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip :title="t('text2sql.train.knowledgeGraph.controls.reset')">
            <a-button type="primary" size="small" shape="circle" @click="resetView">
              <UndoOutlined />
            </a-button>
          </a-tooltip>
          <a-tooltip :title="isFullscreen ? t('text2sql.train.knowledgeGraph.controls.exitFullscreen') : t('text2sql.train.knowledgeGraph.controls.fullscreen')">
            <a-button type="primary" size="small" shape="circle" @click="toggleFullscreen">
              <FullscreenOutlined v-if="!isFullscreen" />
              <FullscreenExitOutlined v-else />
            </a-button>
          </a-tooltip>
        </div>
      </div>

      <!-- 表详情面板 -->
      <div v-if="selectedTable" class="table-detail-panel" :style="detailPanelStyle">
        <div class="table-detail-header">
          <div class="table-title">
            <div class="table-type-indicator"></div>
            <h4>{{ selectedTable.name }}</h4>
          </div>
          <a-button type="text" size="small" @click="closeTableDetail">
            <CloseOutlined />
          </a-button>
        </div>
        <div class="table-detail-content">
                      <a-descriptions :column="1" size="small" bordered>
            <a-descriptions-item :label="t('text2sql.train.knowledgeGraph.tableDetail.tableName')">{{ selectedTable.name }}</a-descriptions-item>
            <a-descriptions-item :label="t('text2sql.train.knowledgeGraph.tableDetail.description')">{{ selectedTable.description || t('text2sql.train.knowledgeGraph.tableDetail.noDescription') }}</a-descriptions-item>
            <a-descriptions-item :label="t('text2sql.train.knowledgeGraph.tableDetail.tableType')">{{ selectedTable.table_type || 'table' }}</a-descriptions-item>
            <a-descriptions-item :label="t('text2sql.train.knowledgeGraph.tableDetail.columnsCount')">{{ selectedTable.columns_count || 0 }}</a-descriptions-item>
          </a-descriptions>
          
          <!-- 列信息 -->
          <div class="columns-section" v-if="selectedTable.columns && selectedTable.columns.length > 0">
            <h5>{{ t('text2sql.train.knowledgeGraph.tableDetail.columnInfo') }}</h5>
            <div class="columns-list">
              <div v-for="column in selectedTable.columns" :key="column.name" class="column-item">
                <div class="column-header">
                  <span class="column-name">{{ column.name }}</span>
                  <a-tag v-if="column.is_primary_key" color="red" size="small">{{ t('text2sql.train.knowledgeGraph.tableDetail.primaryKey') }}</a-tag>
                  <a-tag v-if="column.is_foreign_key" color="orange" size="small">{{ t('text2sql.train.knowledgeGraph.tableDetail.foreignKey') }}</a-tag>
                </div>
                <div class="column-details">
                  <span class="column-type">{{ column.data_type }}</span>
                  <span v-if="column.description" class="column-desc">{{ column.description }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 关系信息 -->
          <div class="relationships-section" v-if="tableRelationships.incoming.length > 0 || tableRelationships.outgoing.length > 0">
            <h5>{{ t('text2sql.train.knowledgeGraph.tableDetail.tableRelationships') }}</h5>
            
            <div v-if="tableRelationships.incoming.length > 0" class="relation-group">
              <h6>{{ t('text2sql.train.knowledgeGraph.tableDetail.referencedBy') }}</h6>
              <div v-for="rel in tableRelationships.incoming" :key="rel.id" class="relation-item">
                <span class="relation-table" @click="selectTableById(rel.source)">{{ getTableName(rel.source) }}</span>
                <span class="relation-arrow">→</span>
                <span class="relation-current">{{ selectedTable.name }}</span>
                <div class="relation-detail">
                  {{ rel.source_column }} → {{ rel.target_column }}
                </div>
              </div>
            </div>
            
            <div v-if="tableRelationships.outgoing.length > 0" class="relation-group">
              <h6>{{ t('text2sql.train.knowledgeGraph.tableDetail.references') }}</h6>
              <div v-for="rel in tableRelationships.outgoing" :key="rel.id" class="relation-item">
                <span class="relation-current">{{ selectedTable.name }}</span>
                <span class="relation-arrow">→</span>
                <span class="relation-table" @click="selectTableById(rel.target)">{{ getTableName(rel.target) }}</span>
                <div class="relation-detail">
                  {{ rel.source_column }} → {{ rel.target_column }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="graph-footer" v-if="graphData.nodes && graphData.nodes.length > 0">
      <div class="statistics">
        <div class="stat-item">
          <span class="stat-label">{{ t('text2sql.train.knowledgeGraph.statistics.totalTables') }}</span>
          <span class="stat-value">{{ statistics.total_tables }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">{{ t('text2sql.train.knowledgeGraph.statistics.totalRelationships') }}</span>
          <span class="stat-value">{{ statistics.total_relationships }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">{{ t('text2sql.train.knowledgeGraph.statistics.totalColumns') }}</span>
          <span class="stat-value">{{ statistics.total_columns }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, onMounted, onBeforeUnmount, watch } from 'vue';
import { message } from 'ant-design-vue';
import { graphVisualizationApi } from '@/api/text2sql/sys';
import * as d3 from 'd3';
import {
  PartitionOutlined,
  ReloadOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
  PlusOutlined,
  MinusOutlined,
  UndoOutlined,
  CloseOutlined
} from '@ant-design/icons-vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { useRootSetting } from '/@/hooks/setting/useRootSetting';
import { ThemeEnum } from '/@/enums/appEnum';

interface Props {
  connectionId?: string;
}

const props = withDefaults(defineProps<Props>(), {
  connectionId: undefined
});

const emit = defineEmits<{
  'table-selected': [tableId: string];
}>();

// 响应式数据
const { t } = useI18n();
const { getDarkMode } = useRootSetting();
const isDarkMode = computed(() => getDarkMode.value === ThemeEnum.DARK);
const graphRef = ref<HTMLElement | null>(null);
const graphContainerRef = ref<HTMLElement | null>(null);
const loading = ref<boolean>(false);
const graphData = ref<any>({ nodes: [], edges: [] });
const connectionInfo = ref<any>(null);
const selectedTable = ref<any>(null);
const clickPosition = ref({ x: 0, y: 0 });
const isFullscreen = ref<boolean>(false);

// D3相关变量
let svg: d3.Selection<SVGSVGElement, unknown, null, undefined> | null = null;
let simulation: d3.Simulation<any, undefined> | null = null;
let zoom: d3.ZoomBehavior<SVGSVGElement, unknown> | null = null;

// 计算属性
const detailPanelStyle = computed(() => {
  if (!clickPosition.value) return {};
  
  const containerWidth = graphRef.value?.offsetWidth || window.innerWidth;
  const containerHeight = graphRef.value?.offsetHeight || window.innerHeight;
  
  const panelWidth = 380;
  const panelHeight = 520;
  const offset = 20; // 浮窗与节点的距离
  
  let left = clickPosition.value.x + offset;
  let top = clickPosition.value.y - panelHeight / 2; // 垂直居中对齐节点
  
  // 检查右边界，如果超出则显示在节点左侧
  if (left + panelWidth > containerWidth - 20) {
    left = clickPosition.value.x - panelWidth - offset;
  }
  
  // 检查下边界
  if (top + panelHeight > containerHeight - 20) {
    top = containerHeight - panelHeight - 20;
  }
  
  // 检查上边界
  if (top < 20) {
    top = 20;
  }
  
  // 确保左边界不超出
  if (left < 20) {
    left = 20;
  }
  
  return {
    left: `${left}px`,
    top: `${top}px`
  };
});

const statistics = computed(() => {
  return graphData.value.statistics || {
    total_tables: 0,
    total_relationships: 0,
    total_columns: 0
  };
});

const tableRelationships = computed(() => {
  if (!selectedTable.value || !graphData.value.edges) {
    return { incoming: [], outgoing: [] };
  }
  
  const tableId = selectedTable.value.id;
  const incoming = graphData.value.edges.filter((edge: any) => edge.target === tableId);
  const outgoing = graphData.value.edges.filter((edge: any) => edge.source === tableId);
  
  return { incoming, outgoing };
});

// 高级感配色方案（与useKnowledgeGraph保持一致）
const customColors = [
  '#FF6B6B', // 温暖珊瑚红 - 友好醒目
  '#00C979', // 翠绿色 - 生机活力  
  '#8A64EF', // 优雅紫色 - 创新科技
  '#BCF60B', // 亮黄绿色 - 醒目突出
  '#FD3D3C', // 活力红色 - 重要节点
  '#F8DE05', // 明亮黄色 - 警示提醒
  '#53589A', // 深紫蓝色 - 沉稳内敛
  '#4ECDC4', // 清新薄荷绿 - 清爽自然
  '#D0F205', // 鲜黄绿色 - 新颖独特
  '#FF9F43'  // 温暖橙色 - 活力温馨
];

// 获取节点颜色（基于节点ID的哈希值分配颜色）
const getNodeColor = (nodeId: string) => {
  // 使用简单的哈希函数为每个节点分配一个颜色
  let hash = 0;
  for (let i = 0; i < nodeId.length; i++) {
    const char = nodeId.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // 转换为32位整数
  }
  const index = Math.abs(hash) % customColors.length;
  return customColors[index];
};

// 获取边框颜色（比填充色稍深）
const getNodeStrokeColor = (fillColor: string) => {
  // 将颜色转换为更深的边框色
  const colorMap: Record<string, string> = {
    '#FF6B6B': '#E55555',
    '#00C979': '#00B366',
    '#8A64EF': '#7A54DF',
    '#BCF60B': '#A8D90A',
    '#FD3D3C': '#E63533',
    '#F8DE05': '#E0C604',
    '#53589A': '#484D87',
    '#4ECDC4': '#45B8B1',
    '#D0F205': '#BBD904',
    '#FF9F43': '#E8913A'
  };
  return colorMap[fillColor] || fillColor;
};

// 数据库类型ID到名称的映射
const getDbTypeName = (dbTypeId: string | number) => {
  const dbTypeMap: Record<string, string> = {
    '1': 'MySQL',
    '2': 'Oracle', 
    '3': 'SQL Server',
    '4': 'MySQL 5.7+',
    '5': 'MariaDB',
    '6': 'PostgreSQL',
    '7': 'H2',
    '8': 'KingBase',
    '9': 'Oscar',
    '10': 'SQLite',
    '11': 'DB2',
    '12': 'HSQLDB',
    '13': 'Derby',
    '14': 'H2',
    '15': 'Unknown'
  };
  return dbTypeMap[String(dbTypeId)] || String(dbTypeId);
};

// 获取数据库类型对应的颜色
const getDbTypeColor = (dbTypeName: string) => {
  const colorMap: Record<string, string> = {
    'mysql': 'pink',
    'mysql 5.7+': 'blue',
    'postgresql': 'green',
    'sqlite': 'orange',
    'oracle': 'red',
    'sql server': 'purple',
    'mariadb': 'cyan',
    'h2': 'geekblue',
    'kingbase': 'volcano',
    'oscar': 'magenta',
    'db2': 'gold',
    'hsqldb': 'lime',
    'derby': 'purple'
  };
  return colorMap[dbTypeName?.toLowerCase()] || 'default';
};

// 方法
const loadGraphData = async () => {
  if (!props.connectionId) {
    message.warning('请先选择数据库连接');
    return;
  }

  loading.value = true;
  try {
    const data = await graphVisualizationApi.getGraphData(props.connectionId);
    graphData.value = data;
    connectionInfo.value = data.connection_info;
    
    nextTick(() => {
      renderGraph();
    });
  } catch (error) {
    console.error('加载知识图谱数据失败:', error);
    message.error('加载知识图谱数据失败');
  } finally {
    loading.value = false;
  }
};

const renderGraph = () => {
  if (!graphRef.value || !graphData.value.nodes || !graphData.value.edges) return;
  
  // 清空容器
  d3.select(graphRef.value).selectAll('*').remove();
  
  const width = graphRef.value.clientWidth;
  const height = graphRef.value.clientHeight;
  
  // 创建SVG
  svg = d3.select(graphRef.value)
    .append('svg')
    .attr('width', width)
    .attr('height', height)
    .attr('viewBox', [0, 0, width, height]);
    
  const g = svg.append('g');
  
  // 定义箭头标记
  svg.append('defs')
    .append('marker')
    .attr('id', 'arrowhead')
    .attr('viewBox', '0 -5 10 10')
    .attr('refX', 10)
    .attr('refY', 0)
    .attr('orient', 'auto')
    .attr('markerWidth', 8)
    .attr('markerHeight', 8)
    .append('path')
    .attr('d', 'M 0,-5 L 10,0 L 0,5')
    .attr('fill', isDarkMode.value ? '#a6a6a6' : '#666');
  
  // 创建力导向图模拟
  simulation = d3.forceSimulation(graphData.value.nodes)
    .force('link', d3.forceLink(graphData.value.edges)
      .id((d: any) => d.id)
      .distance(300))
    .force('charge', d3.forceManyBody().strength(-400)) // 排斥力
        .force('center', d3.forceCenter(width / 2, height / 2).strength(0.1)) // 增加中心力强度参数
        .force('collision', d3.forceCollide().radius(65)) // 避免节点重叠
        .force('x', d3.forceX(width / 2).strength(0.03)) // 添加X方向聚集力
        .force('y', d3.forceY(height / 2).strength(0.03)); // 添加Y方向聚集力
   
  
  // 绘制连接线（改为path以支持自环）
  const link = g.append('g')
    .attr('class', 'links')
    .selectAll('path')
    .data(graphData.value.edges)
    .join('path')
    .attr('stroke', isDarkMode.value ? '#737373' : '#999')
    .attr('stroke-opacity', 0.6)
    .attr('stroke-width', 2)
    .attr('fill', 'none')
    .attr('marker-end', 'url(#arrowhead)');

  // 添加关系标签
  const linkText = g.append('g')
    .attr('class', 'link-labels')
    .selectAll('text')
    .data(graphData.value.edges)
    .join('text')
    .text((d: any) => d.relationship_type || `${d.source_column} → ${d.target_column}`)
    .attr('font-size', 10)
    .attr('text-anchor', 'middle')
    .attr('dy', -5)
    .attr('fill', isDarkMode.value ? '#a6a6a6' : '#666')
    .style('pointer-events', 'none');
  
  // 创建表节点组
  const nodeGroup = g.append('g')
    .attr('class', 'nodes')
    .selectAll('g')
    .data(graphData.value.nodes)
    .join('g')
    .on('mouseover', handleNodeMouseOver)
    .on('mouseout', handleNodeMouseOut)
    .on('click', handleNodeClick)
    .on('dblclick', handleNodeDoubleClick)
    .call(d3.drag<SVGGElement, any>()
      .on('start', dragstarted)
      .on('drag', dragged)
      .on('end', dragended));
  
  // 添加表节点矩形
  nodeGroup.append('rect')
    .attr('width', 100)
    .attr('height', 50)
    .attr('x', -50)
    .attr('y', -25)
    .attr('fill', (d: any) => getNodeColor(d.id))
    .attr('stroke', (d: any) => getNodeStrokeColor(getNodeColor(d.id)))
    .attr('stroke-width', 2)
    .attr('rx', 12);
  
  // 添加表名文本
  nodeGroup.append('text')
    .attr('text-anchor', 'middle')
    .attr('dominant-baseline', 'middle')
    .attr('font-size', 13)
    .attr('font-weight', 'bold')
    .attr('fill', 'white')
    .attr('pointer-events', 'none')
    .text((d: any) => d.name.length > 12 ? d.name.substring(0, 12) + '...' : d.name);
  
  // 更新力导向图
  simulation.on('tick', () => {
    link.attr('d', (d: any) => {
      // 检查是否为自环
      if (d.source.id === d.target.id) {
        // 自环：绘制一个从节点右侧出发的弧形路径
        const nodeRadius = 50; // 节点半宽
        const loopRadius = 30; // 自环的半径
        const x = d.source.x;
        const y = d.source.y;
        
        // 计算自环路径的控制点
        const startX = x + nodeRadius;
        const startY = y;
        const endX = x + nodeRadius;
        const endY = y;
        
        // 使用贝塞尔曲线创建自环
        const controlX1 = x + nodeRadius + loopRadius;
        const controlY1 = y - loopRadius;
        const controlX2 = x + nodeRadius + loopRadius;
        const controlY2 = y + loopRadius;
        
        return `M ${startX} ${startY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${endX} ${endY}`;
      } else {
        // 普通连接：绘制直线但从节点边界开始和结束
        const dx = d.target.x - d.source.x;
        const dy = d.target.y - d.source.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance === 0) return '';
        
        const nodeRadius = 50; // 节点矩形的半宽
        const nodeHeight = 25; // 节点矩形的半高
        
        // 计算从源节点边界开始的连线
        const startX = d.source.x + (dx / distance) * nodeRadius;
        const startY = d.source.y + (dy / distance) * nodeHeight;
        
        // 计算到目标节点边界结束的连线（为箭头留出空间）
        const endX = d.target.x - (dx / distance) * (nodeRadius + 10);
        const endY = d.target.y - (dy / distance) * (nodeHeight + 10);
        
        return `M ${startX} ${startY} L ${endX} ${endY}`;
      }
    });
    
    linkText
      .attr('x', (d: any) => {
        if (d.source.id === d.target.id) {
          // 自环标签位置
          return d.source.x + 80;
        } else {
          return (d.source.x + d.target.x) / 2;
        }
      })
      .attr('y', (d: any) => {
        if (d.source.id === d.target.id) {
          // 自环标签位置
          return d.source.y - 20;
        } else {
          return (d.source.y + d.target.y) / 2;
        }
      });
    
    nodeGroup.attr('transform', (d: any) => `translate(${d.x},${d.y})`);
  });
  
  // 添加缩放功能
  zoom = d3.zoom<SVGSVGElement, unknown>()
    .scaleExtent([0.1, 3])
    .on('zoom', (event) => {
      g.attr('transform', event.transform);
    });
    
  svg.call(zoom);
  
  // 点击空白处关闭详情面板
  svg.on('click', (event) => {
    if (event.target === svg?.node()) {
      selectedTable.value = null;
    }
  });
};

// 事件处理函数
const handleNodeMouseOver = (event: any, _d: any) => {
  d3.select(event.currentTarget)
    .select('rect')
    .attr('stroke', '#FFD700')
    .attr('stroke-width', 3);
};

const handleNodeMouseOut = (event: any, d: any) => {
  const isSelected = d3.select(event.currentTarget).classed('selected');
  if (!isSelected) {
    const originalColor = getNodeColor(d.id);
    const strokeColor = getNodeStrokeColor(originalColor);
    d3.select(event.currentTarget)
      .select('rect')
      .attr('stroke', strokeColor)
      .attr('stroke-width', 2);
  }
};

const handleNodeClick = (event: any, d: any) => {
  // 移除所有选中状态
  d3.selectAll('.nodes g').classed('selected', false)
    .selectAll('rect')
    .attr('stroke', function(this: any, node: any) {
      const originalColor = getNodeColor(node.id);
      return getNodeStrokeColor(originalColor);
    })
    .attr('stroke-width', 2);
  
  // 设置当前节点为选中状态
  d3.select(event.currentTarget)
    .classed('selected', true)
    .select('rect')
    .attr('stroke', '#FFD700')
    .attr('stroke-width', 3);
  
  // 获取SVG容器的位置信息
  const svgRect = svg?.node()?.getBoundingClientRect();
  const containerRect = graphRef.value?.getBoundingClientRect();
  
  if (svgRect && containerRect && svg) {
    // 计算节点在容器中的实际位置
    const nodeX = d.x;
    const nodeY = d.y;
    
    // 获取当前的缩放变换
    const svgElement = svg.node();
    if (svgElement) {
      const transform = d3.zoomTransform(svgElement);
      
      // 计算变换后的节点位置
      const transformedX = transform.applyX(nodeX);
      const transformedY = transform.applyY(nodeY);
      
      // 相对于容器的位置
      clickPosition.value = {
        x: transformedX,
        y: transformedY
      };
    } else {
      // 备用方案：使用鼠标位置
      clickPosition.value = { x: event.layerX || event.offsetX, y: event.layerY || event.offsetY };
    }
  } else {
    // 备用方案：使用鼠标位置
    clickPosition.value = { x: event.layerX || event.offsetX, y: event.layerY || event.offsetY };
  }
  
  // 设置选中的表
  selectedTable.value = { ...d };
  
  // 发送事件（虽然父组件不再处理，但保持接口一致性）
  emit('table-selected', d.id);
  
  event.stopPropagation();
};

const handleNodeDoubleClick = (event: any, d: any) => {
  if (!svg || !zoom) return;
  
  event.stopPropagation();
  
  const width = graphRef.value?.clientWidth || 800;
  const height = graphRef.value?.clientHeight || 600;
  
  svg.transition()
    .duration(750)
    .call(
      zoom.transform,
      d3.zoomIdentity
        .translate(width / 2, height / 2)
        .scale(1.5)
        .translate(-d.x, -d.y)
    );
};

// 拖拽函数
const dragstarted = (event: any, d: any) => {
  if (!event.active && simulation) simulation.alphaTarget(0.3).restart();
  d.fx = d.x;
  d.fy = d.y;
};

const dragged = (event: any, d: any) => {
  d.fx = event.x;
  d.fy = event.y;
};

const dragended = (event: any, d: any) => {
  if (!event.active && simulation) simulation.alphaTarget(0);
  d.fx = null;
  d.fy = null;
};

// 工具函数
const getTableName = (tableId: string) => {
  const table = graphData.value.nodes.find((node: any) => node.id === tableId);
  return table ? table.name : tableId;
};

const selectTableById = (tableId: string) => {
  const table = graphData.value.nodes.find((node: any) => node.id === tableId);
  if (table) {
    selectedTable.value = { ...table };
    emit('table-selected', tableId);
  }
};

const closeTableDetail = () => {
  selectedTable.value = null;
  // 移除所有选中状态
  if (svg) {
    d3.selectAll('.nodes g').classed('selected', false)
      .selectAll('rect')
      .attr('stroke', function(this: any, node: any) {
        const originalColor = getNodeColor(node.id);
        return getNodeStrokeColor(originalColor);
      })
      .attr('stroke-width', 2);
  }
};

const refreshGraph = () => {
  loadGraphData();
};

const zoomIn = () => {
  if (svg && zoom) {
    svg.transition().call(zoom.scaleBy, 1.2);
  }
};

const zoomOut = () => {
  if (svg && zoom) {
    svg.transition().call(zoom.scaleBy, 0.8);
  }
};

const resetView = () => {
  if (svg && zoom) {
    svg.transition().call(zoom.transform, d3.zoomIdentity);
  }
};

const toggleFullscreen = () => {
  if (!graphContainerRef.value) return;
  
  if (!isFullscreen.value) {
    if (graphContainerRef.value.requestFullscreen) {
      graphContainerRef.value.requestFullscreen();
    }
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  }
};

const handleFullscreenChange = () => {
  isFullscreen.value = !!(document.fullscreenElement);
  
  // 全屏状态变化后重新渲染
  setTimeout(() => {
    if (graphData.value.nodes && graphData.value.nodes.length > 0) {
      renderGraph();
    }
  }, 100);
};

const handleResize = () => {
  if (graphData.value.nodes && graphData.value.nodes.length > 0) {
    renderGraph();
  }
};

// 生命周期
onMounted(() => {
  if (props.connectionId) {
    loadGraphData();
  }
  
  // 添加事件监听
  document.addEventListener('fullscreenchange', handleFullscreenChange);
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  // 清理资源
  if (simulation) {
    simulation.stop();
  }
  
  // 移除事件监听
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  window.removeEventListener('resize', handleResize);
});

// 监听connectionId变化
watch(() => props.connectionId, (newId) => {
  if (newId) {
    loadGraphData();
  } else {
    graphData.value = { nodes: [], edges: [] };
    selectedTable.value = null;
  }
});

// 暴露方法给父组件
defineExpose({
  refresh: loadGraphData,
  selectTable: selectTableById,
  graphData: graphData
});
</script>

<style scoped lang="less">
@import './styles/DatabaseKnowledgeGraph.less';
</style>
