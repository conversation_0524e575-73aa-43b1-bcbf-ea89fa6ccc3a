// 2D知识图谱 - 主容器样式
@import './common.less';

.knowledge-graph-container {
  height: 100%;
  width: 100%;
  
  .graph-container {
    .graph-container-base();
    .graph-controls-style();
    border: 1px solid @border-color-light;
    border-radius: @radius-small;
    
    // 2D特有的节点详情面板样式
    .node-detail-panel {
      .entity-detail-base();
    }
  }
}

// 2D全屏模式下的统计信息悬浮窗样式
.statistics-floating-panel {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid @border-color-light;
  border-radius: @radius-normal;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  padding: 12px;
  min-width: 140px;
  font-size: 12px;
  transition: all @animation-duration ease;
  
  &:hover {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
  
  .statistics-header {
    font-weight: 600;
    color: @text-color;
    margin-bottom: 8px;
    font-size: 13px;
    text-align: center;
    border-bottom: 1px solid @border-color-light;
    padding-bottom: 6px;
  }
  
  .statistics-content {
    .statistic-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 6px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .statistic-icon {
        font-size: 14px;
        margin-right: 6px;
      }
      
      .statistic-label {
        color: @text-color-secondary;
        font-weight: 500;
        flex: 1;
      }
      
      .statistic-value {
        color: @primary-color;
        font-weight: bold;
        font-size: 13px;
      }
    }
  }
  
  // 在较小的屏幕上调整位置
  @media (max-width: 768px) {
    top: 10px;
    left: 10px;
    min-width: 120px;
    font-size: 11px;
    
    .statistics-header {
      font-size: 12px;
    }
    
    .statistics-content .statistic-item {
      .statistic-icon {
        font-size: 12px;
      }

      .statistic-value {
        font-size: 12px;
      }
    }
  }
}

// ========== 暗黑模式适配 ==========
[data-theme='dark'] {
  .knowledge-graph-container {
    background: @dark-bg-primary;
    color: @dark-text-color;

    .graph-container {
      .graph-container-base();
      .graph-controls-style();
      border: 1px solid @dark-border-color;
      border-radius: @radius-small;
      background: @dark-bg-primary;

      // 2D特有的节点详情面板样式
      .node-detail-panel {
        .entity-detail-base();

        .type-with-color {
          display: flex;
          align-items: center;
          gap: 6px;

          .type-color-marker {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            flex-shrink: 0;
          }
        }

        .ant-descriptions {
          .ant-descriptions-item-label {
            color: @dark-text-color-secondary !important;
          }

          .ant-descriptions-item-content {
            color: @dark-text-color !important;
          }
        }

        .node-relations {
          .relation-item {
            background: @dark-bg-secondary;
            border: 1px solid @dark-border-color;
            color: @dark-text-color;

            &:hover {
              background: @dark-bg-tertiary;
              border-color: @primary-color;
            }
          }
        }
      }
    }
  }

  // 2D全屏模式下的统计信息悬浮窗样式
  .statistics-floating-panel {
    background: @dark-bg-translucent;
    backdrop-filter: blur(10px);
    border: 1px solid @dark-border-color;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    color: @dark-text-color;

    &:hover {
      background: rgba(38, 38, 38, 0.98);
      box-shadow: 0 12px 48px rgba(0, 0, 0, 0.4);
    }

    .statistics-header {
      color: @dark-text-color;
      border-bottom: 1px solid @dark-border-color;
    }

    .statistics-content {
      .statistic-item {
        color: @dark-text-color-secondary;

        .statistic-icon {
          color: @primary-color;
        }

        .statistic-value {
          color: @dark-text-color;
        }

        .statistic-label {
          color: @dark-text-color-secondary;
        }
      }
    }
  }
}