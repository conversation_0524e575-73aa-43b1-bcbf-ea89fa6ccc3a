<template>
  <Card :title="t('dashboard.databaseStats.title')" :loading="loading">
    <div class="table-stats-container">
      <!-- 核心统计数据 -->
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-icon">
            <Icon icon="ant-design:table-outlined" size="20" color="#1890ff" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ dashboardStats.database_count || 0 }}</div>
            <div class="stat-label">{{ t('dashboard.databaseStats.dataTables') }}</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon">
            <Icon icon="ant-design:ordered-list-outlined" size="20" color="#52c41a" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ dashboardStats.total_records || 0 }}</div>
            <div class="stat-label">{{ t('dashboard.databaseStats.totalFields') }}</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon">
            <Icon icon="ant-design:share-alt-outlined" size="20" color="#fa8c16" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ dashboardStats.relationship_count || 0 }}</div>
            <div class="stat-label">{{ t('dashboard.databaseStats.relationships') }}</div>
          </div>
        </div>
        
        <div class="stat-item">
          <div class="stat-icon">
            <Icon icon="ant-design:database-outlined" size="20" color="#722ed1" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ dashboardStats.datasource_count || 0 }}</div>
            <div class="stat-label">{{ t('dashboard.databaseStats.dataSources') }}</div>
          </div>
        </div>
      </div>
      
      <!-- 简洁的图表区域 -->
      <div class="chart-section">
        <div ref="chartRef" :style="{ width: '100%', height: '140px' }"></div>
      </div>
      
      <!-- 简化的底部统计 -->
      <div class="summary-stats">
        <span class="summary-text">{{ t('dashboard.databaseStats.avgFields') }} {{ dashboardStats.avg_fields_coverage || 0 }} {{ t('dashboard.databaseStats.fieldsUnit') }}</span>
      </div>
    </div>
  </Card>
</template>

<script lang="ts" setup>
import { Ref, ref, onMounted, watch } from 'vue';
import { Card } from 'ant-design-vue';
import { useECharts } from '/@/hooks/web/useECharts';
import { useI18n } from '/@/hooks/web/useI18n';
import Icon from '/@/components/Icon';
import { databaseService } from '@/api/statistics/database';
import type { DashboardStats, DatabaseChartDistribution } from '@/api/statistics/database';

const props = defineProps({
  loading: Boolean,
});

const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
const { t } = useI18n();

// 统计数据
const dashboardStats = ref<DashboardStats>({
  database_count: 0,
  total_records: 0,
  relationship_count: 0,
  datasource_count: 0,
  avg_fields_coverage: 0
});

const chartData = ref<DatabaseChartDistribution>({
  chart_data: []
});

// 获取仪表板概览数据
const fetchDashboardStats = async () => {
  try {
    const data = await databaseService.getDashboardOverview();
    dashboardStats.value = data;
  } catch (error) {
    console.error(t('dashboard.errors.loadStatsFailed') + ':', error);
    // 如果接口失败，保持默认值
  }
};

// 获取图表数据
const fetchChartData = async () => {
  try {
    const data = await databaseService.getChartDistribution();
    chartData.value = data;
    setupChart();
  } catch (error) {
    console.error(t('dashboard.errors.loadChartFailed') + ':', error);
    // 如果接口失败，使用默认图表数据
    setupChart();
  }
};

// 设置图表
const setupChart = () => {
  if (props.loading) return;
  
  // 使用真实数据或默认数据（更新为关系类型分布）
  const xAxisData = chartData.value.chart_data.length > 0 
    ? chartData.value.chart_data.map(item => item.label)
    : [
        t('dashboard.databaseStats.relationshipTypes.oneToOne'),
        t('dashboard.databaseStats.relationshipTypes.oneToMany'),
        t('dashboard.databaseStats.relationshipTypes.manyToOne'),
        t('dashboard.databaseStats.relationshipTypes.foreignKey'),
        t('dashboard.databaseStats.relationshipTypes.uncategorized')
      ];
    
  const seriesData = chartData.value.chart_data.length > 0 
    ? chartData.value.chart_data.map(item => item.value)
    : [45, 128, 23, 156, 89];
  
  setOptions({
    grid: {
      top: 20,
      left: 30,
      right: 30,
      bottom: 20,
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => `${params[0].name}: ${params[0].value}${t('dashboard.databaseStats.tooltip.relationships')}`
    },
    xAxis: {
      type: 'category',
      data: xAxisData,
      axisLabel: {
        fontSize: 10,
        rotate: 45
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 10
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0'
        }
      }
    },
    series: [
      {
        data: seriesData,
        type: 'bar',
        itemStyle: {
          color: '#5470c6',
          borderRadius: [4, 4, 0, 0]
        },
        barWidth: '60%'
      }
    ]
  });
};

// 监听loading状态变化
watch(
  () => props.loading,
  () => {
    if (!props.loading) {
      setupChart();
    }
  },
  { immediate: true }
);

// 初始化数据
const initData = async () => {
  await Promise.all([
    fetchDashboardStats(),
    fetchChartData()
  ]);
};

onMounted(() => {
  initData();
});
</script>

<style scoped>
.table-stats-container {
  padding: 8px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fafafa;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  background: #f0f0f0;
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 20px;
  font-weight: bold;
  color: #262626;
  line-height: 1;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
}

.chart-section {
  margin-bottom: 16px;
}

.summary-stats {
  text-align: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.summary-text {
  font-size: 12px;
  color: #666;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

/* 暗黑模式样式 */
[data-theme='dark'] .stat-item {
  background: #1f1f1f;
  border: 1px solid #303030;
}

[data-theme='dark'] .stat-item:hover {
  background: #262626;
}

[data-theme='dark'] .stat-icon {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme='dark'] .stat-number {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .stat-label {
  color: rgba(255, 255, 255, 0.45);
}

[data-theme='dark'] .summary-stats {
  border-top-color: #303030;
}

[data-theme='dark'] .summary-text {
  color: rgba(255, 255, 255, 0.45);
}
</style>