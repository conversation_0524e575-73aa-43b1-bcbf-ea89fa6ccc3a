<template>
  <div class="history-container">
    <a-row :gutter="[24, 24]">
      <a-col :span="24">
        <div class="page-header">
          <h1 class="page-title">
            <history-outlined /> {{ t('text2sql.train.history.title') }}
          </h1>
          <p class="page-description">{{ t('text2sql.train.history.description') }}</p>
        </div>
      </a-col>

      <a-col :span="24">
        <a-card :bordered="false" class="history-card">
          <template #extra>
            <a-button type="primary" @click="loadHistory" :loading="loading">
              <reload-outlined /> {{ t('text2sql.train.history.refresh') }}
            </a-button>
          </template>

          <a-spin :spinning="loading">
            <a-empty
              v-if="!questionHistory || questionHistory.length === 0"
              :description="t('text2sql.train.history.empty')"
              class="empty-container"
            >
              <template #image>
                <inbox-outlined style="font-size: 64px; color: #bfbfbf" />
              </template>
              <template #description>
                <span>{{ t('text2sql.train.history.noQuestionsYet') }}</span>
              </template>
            </a-empty>

            <a-list
              v-else
              class="history-list"
              :grid="{ gutter: 16, xs: 1, sm: 1, md: 2, lg: 2, xl: 3, xxl: 3 }"
            >
              <a-list-item v-for="item in questionHistory" :key="item.id">
                <a-card
                  hoverable
                  class="question-card"
                  @click="loadQuestion(item.id)"
                >
                  <template #title>
                    <div class="question-title">
                      <question-circle-outlined />
                      <span>{{ item.question }}</span>
                    </div>
                  </template>
                  <template #extra>
                    <a-tag color="blue">{{ t('text2sql.train.history.view') }}</a-tag>
                  </template>
                  <div class="question-id">ID: {{ item.id }}</div>
                </a-card>
              </a-list-item>
            </a-list>
          </a-spin>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useText2SqlTrain } from '@/store/modules/text2sqlTrain';
import { getQuestionHistory } from '@/api/text2sql';
import { message } from 'ant-design-vue';
import { useI18n } from 'vue-i18n';
import {
  HistoryOutlined,
  ReloadOutlined,
  InboxOutlined,
  HomeOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons-vue';

const router = useRouter();
const store = useText2SqlTrain();
const { t } = useI18n();
const loading = ref(false);
const questionHistory = ref<Array<{ id: string; question: string }>>([]);

// 加载历史记录
const loadHistory = async () => {
  loading.value = true;
  try {
    const response = await getQuestionHistory();
    questionHistory.value = response.questions || [];
    if (questionHistory.value.length > 0) {
      message.success(t('text2sql.train.history.historyLoadSuccess', { count: questionHistory.value.length }));
    } else {
      message.info(t('text2sql.train.history.noHistoryRecords'));
    }
  } catch (error) {
    console.error('加载历史记录失败:', error);
    message.error(t('text2sql.train.history.loadHistoryFailed'));
  } finally {
    loading.value = false;
  }
};

// 加载问题
const loadQuestion = async (id: string) => {
  message.loading(t('text2sql.train.history.loadingQuestion'), 1);
  await store.loadSavedQuestion(id);

  // 通过事件总线通知父组件切换到首页
  router.push('/text2sql/base/dbchat');

  // 如果使用的是组件切换而不是路由，则需要获取父组件的引用
  // 这里我们可以通过emit事件来通知父组件
  // const event = new CustomEvent('switchToHome', { bubbles: true });
  // document.dispatchEvent(event);
};

// 组件挂载时加载历史记录
onMounted(async () => {
  await loadHistory();
});
</script>

<style scoped>
.history-container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--heading-color);
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-description {
  color: var(--text-color-secondary);
  font-size: 16px;
  margin-bottom: 0;
}

.history-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.empty-container {
  padding: 48px 0;
}

.history-list {
  margin-top: 16px;
}

.question-card {
  height: 100%;
  transition: all 0.3s;
  border-radius: 8px;
  overflow: hidden;
}

.question-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.question-title {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.question-id {
  color: var(--text-color-secondary);
  font-size: 12px;
  margin-top: 8px;
}

/* 暗黑模式样式 */
[data-theme='dark'] .history-container {
  .page-title {
    color: rgba(255, 255, 255, 0.85);
  }

  .page-description {
    color: rgba(255, 255, 255, 0.45);
  }

  .history-card {
    background: #262626;
    border-color: #404040;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .question-card {
    background: #262626;
    border-color: #404040;

    &:hover {
      box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
      border-color: #1890ff;
    }

    .ant-card-head {
      background: #262626;
      border-bottom-color: #404040;

      .ant-card-head-title {
        color: rgba(255, 255, 255, 0.85);
      }
    }

    .ant-card-body {
      background: #262626;
      color: rgba(255, 255, 255, 0.65);
    }
  }

  .question-title {
    color: rgba(255, 255, 255, 0.85);
  }

  .question-id {
    color: rgba(255, 255, 255, 0.45);
  }

  /* 空状态样式 */
  .ant-empty {
    .ant-empty-description {
      color: rgba(255, 255, 255, 0.45);
    }
  }

  /* 分页样式 */
  .ant-pagination {
    .ant-pagination-item {
      background: #262626;
      border-color: #404040;

      a {
        color: rgba(255, 255, 255, 0.65);
      }

      &:hover {
        border-color: #1890ff;

        a {
          color: #1890ff;
        }
      }
    }

    .ant-pagination-item-active {
      background: #1890ff;
      border-color: #1890ff;

      a {
        color: white;
      }
    }

    .ant-pagination-prev,
    .ant-pagination-next {
      background: #262626;
      border-color: #404040;
      color: rgba(255, 255, 255, 0.65);

      &:hover {
        border-color: #1890ff;
        color: #1890ff;
      }

      &.ant-pagination-disabled {
        background: #1a1a1a;
        border-color: #404040;
        color: rgba(255, 255, 255, 0.25);
      }
    }
  }
}
</style>
