<template>
  <div class="home-container">
    <a-row :gutter="[24, 24]">
      <a-col :span="24">
        <div class="hero-section">
          <div class="hero-title-container">
                      <h1 class="hero-title">{{ t('text2sql.train.home.title').substring(0, 9) }}<span class="highlight">{{ t('text2sql.train.home.title').substring(9) }}</span></h1>
          <div class="hero-badge">{{ t('text2sql.chat.subtitle') }}</div>
          </div>
          <p class="hero-subtitle">{{ t('text2sql.train.home.subtitle') }} | <span class="highlight">{{ t('text2sql.train.home.features.intelligentAnalysis') }}</span> | <span class="highlight">{{ t('text2sql.train.home.features.realTimeVisualization') }}</span></p>

          <a-form layout="vertical" class="search-form">
            <a-form-item>
              <div class="search-input-wrapper">
                <a-input-search
                  v-model:value="question"
                  :placeholder="t('text2sql.train.home.search.placeholder')"
                  :enter-button="t('text2sql.train.home.search.button')"
                  size="large"
                  :loading="store.loading"
                  @search="handleSearch"
                >
                  <template #prefix>
                    <search-outlined />
                  </template>
                </a-input-search>
                <a-tooltip :title="t('text2sql.train.home.search.clear')" v-if="question || store.sql || store.dataFrame">
                  <a-button 
                    type="text" 
                    size="small" 
                    @click="handleClear"
                    :disabled="store.loading"
                    class="clear-button-absolute"
                  >
                    <close-outlined />
                  </a-button>
                </a-tooltip>
              </div>
            </a-form-item>
          </a-form>

          <div v-if="suggestedQuestions && suggestedQuestions.length > 0" class="suggested-questions">
            <h3>{{ suggestedQuestionsHeader }}</h3>
            <div class="question-tags">
              <a-tag
                v-for="(q, index) in suggestedQuestions"
                :key="index"
                color="blue"
                :class="['question-tag', { 'question-tag-disabled': store.loading }]"
                @click="handleSuggestedQuestion(q)"
              >
                {{ q }}
              </a-tag>
            </div>
          </div>
        </div>
      </a-col>

      <a-col :span="24" v-if="store.error">
        <a-alert type="error" :message="store.error" show-icon />
      </a-col>

      <a-col :span="24" v-if="store.sql">
        <a-card
          :title="t('text2sql.train.home.results.sqlTitle')"
          :bordered="false"
          class="result-card"
        >
          <template #extra>
            <a-tooltip :title="t('text2sql.train.home.results.copy')">
              <a-button type="text" @click="copySql">
                <copy-outlined />
              </a-button>
            </a-tooltip>
          </template>
          <div class="code-block">
            <pre><code>{{ store.sql }}</code></pre>
          </div>
        </a-card>
      </a-col>

      <a-col :span="24" v-if="store.dataFrame && Array.isArray(store.dataFrame) && store.dataFrame.length > 0">
        <a-card
          :title="t('text2sql.train.home.results.dataTitle')"
          :bordered="false"
          class="result-card"
        >
          <template #extra>
            <a-space>
              <a-tooltip :title="t('text2sql.train.home.results.download')">
                <a-button type="text" @click="handleDownloadCsv">
                  <download-outlined />
                </a-button>
              </a-tooltip>
              <a-tooltip :title="t('text2sql.train.home.results.refresh')">
                <a-button type="text" @click="refreshData">
                  <reload-outlined />
                </a-button>
              </a-tooltip>
            </a-space>
          </template>
          <a-table
            :dataSource="store.dataFrame"
            :columns="tableColumns"
            :scroll="{ x: 'max-content' }"
            :pagination="{ pageSize: 10, showSizeChanger: true }"
            size="middle"
            bordered
            class="data-table"
          />
        </a-card>
      </a-col>

      <a-col :span="24" v-if="store.figure">
        <a-card
          :title="t('text2sql.train.home.results.chartTitle')"
          :bordered="false"
          class="result-card"
        >
          <template #extra>
            <a-tooltip :title="t('text2sql.train.home.results.refreshChart')">
              <a-button type="text" @click="refreshChart">
                <bar-chart-outlined />
              </a-button>
            </a-tooltip>
          </template>
          <div ref="plotlyContainer" class="chart-container"></div>
        </a-card>
      </a-col>

      <!-- 用户确认组件 -->
      <a-col :span="24" v-if="showConfirmation && store.sql">
        <a-card
          :bordered="false"
          class="result-card"
        >
          <div class="confirmation-container">
            <div class="confirmation-icon">
              <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMTg5MGZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1oZWxwLWNpcmNsZSI+PGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiPjwvY2lyY2xlPjxwYXRoIGQ9Ik05LjA5IDkuYTMgMyAwIDAgMSA1LjgzIDFjMCAyLTMgMy0zIDMiPjwvcGF0aD48bGluZSB4MT0iMTIiIHkxPSIxNyIgeDI9IjEyLjAxIiB5Mj0iMTciPjwvbGluZT48L3N2Zz4=" alt="确认图标" />
            </div>
            <div class="confirmation-text">{{ t('text2sql.train.home.confirmation.question') }}</div>
            <div class="confirmation-buttons">
              <a-button type="primary" @click="handleConfirmYes" :loading="confirmationLoading">Yes</a-button>
              <a-button @click="handleConfirmNo" :disabled="confirmationLoading">No</a-button>
            </div>
          </div>
        </a-card>
      </a-col>

      <a-col :span="24" v-if="store.followupQuestions && store.followupQuestions.length > 0">
        <a-card
          :title="t('text2sql.train.home.followup.title')"
          :bordered="false"
          class="result-card"
        >
          <div class="question-tags">
            <a-tag
              v-for="(q, index) in store.followupQuestions"
              :key="index"
              color="green"
              :class="['question-tag', 'followup-tag', { 'question-tag-disabled': store.loading }]"
              @click="handleSuggestedQuestion(q)"
              style="color: #000000; font-weight: 500;"
            >
              <question-circle-outlined /> {{ q }}
            </a-tag>
          </div>
        </a-card>
      </a-col>

    </a-row>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { useText2SqlTrain } from '/@/store/modules/text2sqlTrain';
import { getGenerateQuestions, train } from '/@/api/text2sql';
import { message } from 'ant-design-vue';
import { useI18n } from '/@/hooks/web/useI18n';
import { useTrainSession } from '../composables/useTrainSession';
import {
  SearchOutlined,
  CopyOutlined,
  DownloadOutlined,
  ReloadOutlined,
  BarChartOutlined,
  QuestionCircleOutlined,
  CloseOutlined
} from '@ant-design/icons-vue';

// 如果使用 CDN 引入 Plotly，则使用全局对象
// 如果使用 npm 包，则需要取消下面的注释并注释掉 declare const Plotly: any;
// import Plotly from 'plotly.js-dist';
declare const Plotly: any;

const { t } = useI18n();
const store = useText2SqlTrain();
const question = ref('');
// 初始化为空数组，避免undefined
const suggestedQuestions = ref<string[]>([]);
const suggestedQuestionsHeader = ref(t('text2sql.train.home.suggestions.title'));
const plotlyContainer = ref<HTMLElement | null>(null);

// 使用会话管理composable
const { currentSessionId, getSessionId, clearSession } = useTrainSession();

// 用户确认相关状态
const showConfirmation = ref(false);
const confirmationLoading = ref(false);

// 计算表格列
const tableColumns = computed(() => {
  if (!store.dataFrame || !Array.isArray(store.dataFrame) || store.dataFrame.length === 0) return [];

  const firstRow = store.dataFrame[0];
  return Object.keys(firstRow).map(key => ({
    title: key,
    dataIndex: key,
    key,
    ellipsis: true,
    sorter: (a: any, b: any) => {
      const valueA = a[key];
      const valueB = b[key];

      if (typeof valueA === 'number' && typeof valueB === 'number') {
        return valueA - valueB;
      }

      if (valueA === null) return -1;
      if (valueB === null) return 1;

      return String(valueA).localeCompare(String(valueB));
    },
  }));
});

// 处理搜索
const handleSearch = async () => {
  if (!question.value.trim()) {
    message.warning('请输入问题');
    return;
  }
  // 重置确认状态
  showConfirmation.value = false;

  // 获取会话ID并传递给store
  const currentSessionId = getSessionId();
  await store.processQuestion(question.value, currentSessionId);

  // 成功后滚动到结果区域
  if (store.sql) {
    await nextTick();
    const resultElement = document.querySelector('.result-card');
    if (resultElement) {
      resultElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    // 显示确认组件
    showConfirmation.value = true;
  }
};

// 处理推荐问题点击
const handleSuggestedQuestion = async (q: string) => {
  // 如果正在加载中，则不允许触发新的查询
  if (store.loading) {
    message.warning('请等待当前查询完成');
    return;
  }
  handleClear();
  question.value = q;
  // 重置确认状态
  showConfirmation.value = false;
  await handleSearch();
};

// 处理下载 CSV
const handleDownloadCsv = () => {
  if (!store.dataFrame || !Array.isArray(store.dataFrame) || store.dataFrame.length === 0) {
    message.warning('没有数据可以下载');
    return;
  }

  try {
    // 生成CSV内容
    const csvContent = generateCsvContent(store.dataFrame);
    
    // 创建Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    
    // 创建下载链接
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    
    // 生成文件名（包含时间戳）
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    link.setAttribute('download', `查询结果_${timestamp}.csv`);
    
    // 隐藏链接并触发下载
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 释放URL对象
    URL.revokeObjectURL(url);
    
    message.success('CSV 文件下载成功');
  } catch (error) {
    console.error('生成CSV文件失败:', error);
    message.error('生成CSV文件失败');
  }
};

// 生成CSV内容的辅助函数
const generateCsvContent = (data: any[]): string => {
  if (!data || data.length === 0) return '';
  
  // 获取表头
  const headers = Object.keys(data[0]);
  
  // 转义CSV字段的函数
  const escapeCsvField = (field: any): string => {
    if (field === null || field === undefined) return '';
    
    const str = String(field);
    // 如果包含逗号、双引号或换行符，需要用双引号包围并转义内部双引号
    if (str.includes(',') || str.includes('"') || str.includes('\n') || str.includes('\r')) {
      return `"${str.replace(/"/g, '""')}"`;
    }
    return str;
  };
  
  // 生成CSV行
  const csvRows: string[] = [];
  
  // 添加表头
  csvRows.push(headers.map(header => escapeCsvField(header)).join(','));
  
  // 添加数据行
  data.forEach(row => {
    const csvRow = headers.map(header => escapeCsvField(row[header])).join(',');
    csvRows.push(csvRow);
  });
  
  // 添加BOM以支持中文字符在Excel中正确显示
  return '\uFEFF' + csvRows.join('\n');
};

// 复制 SQL
const copySql = async () => {
  if (!store.sql) return;

  try {
    await navigator.clipboard.writeText(store.sql);
    message.success('SQL 已复制到剪贴板');
  } catch (err) {
    console.error('复制失败:', err);
    message.error('复制失败');
  }
};

// 刷新数据
const refreshData = async () => {
  if (!store.currentId) return;

  try {
    await store.executeSql();
    message.success('数据已刷新');
  } catch (err) {
    message.error('刷新数据失败');
  }
};

// 刷新图表
const refreshChart = async () => {
  if (!store.currentId) return;

  try {
    await store.generateFigure();
    message.success('图表已刷新');
  } catch (err) {
    message.error('刷新图表失败');
  }
};

// 加载推荐问题
const loadSuggestedQuestions = async () => {
  try {
    const response = await getGenerateQuestions();
    suggestedQuestions.value = response?.questions || [];
    suggestedQuestionsHeader.value = response?.header || '推荐问题';
  } catch (error) {
    console.error('加载推荐问题失败:', error);
    // 出错时设置为空数组和默认标题
    suggestedQuestions.value = [];
    suggestedQuestionsHeader.value = '推荐问题';
  }
};

// 渲染 Plotly 图表
const renderPlotly = () => {
  if (!store.figure || typeof store.figure !== 'object' || !plotlyContainer.value) return;

  try {
    // 确保figure对象有必要的属性
    if (!store.figure.data || !store.figure.layout) {
      console.error('图表数据格式不正确');
      return;
    }

    Plotly.newPlot(
      plotlyContainer.value,
      store.figure.data,
      store.figure.layout,
      { responsive: true }
    );
  } catch (error) {
    console.error('渲染图表失败:', error);
  }
};

// 监听图表数据变化
watch(() => store.figure, (newVal) => {
  if (newVal) {
    setTimeout(renderPlotly, 100);
  }
}, { immediate: true });

// 用户确认结果正确
const handleConfirmYes = async () => {
  if (!store.sql || !store.question) return;

  confirmationLoading.value = true;
  try {
    // 调用train接口保存问答数据
    await train({
      question: store.question,
      sql: store.sql
    });
    message.success('感谢您的反馈，数据已保存！');
    // 隐藏确认组件
    showConfirmation.value = false;
  } catch (error) {
    console.error('保存训练数据失败:', error);
    message.error('保存失败，请稍后再试');
  } finally {
    confirmationLoading.value = false;
  }
};

// 用户确认结果不正确
const handleConfirmNo = () => {
  // 仅隐藏确认组件
  showConfirmation.value = false;
  message.info('感谢您的反馈！');
};

// 清空内容
const handleClear = () => {
  question.value = '';
  store.resetState();
  showConfirmation.value = false;
  // 清空会话ID，下次查询时会生成新的会话ID
  clearSession();
  message.success('已清空所有内容');
};

// 组件挂载时加载推荐问题
onMounted(async () => {
  await loadSuggestedQuestions();
});
</script>

<style scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
  min-height: calc(100vh - 200px);
  padding-bottom: 40px;
}

.hero-section {
  text-align: center;
  padding: 50px 20px;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f0f7ff 100%);
  border-radius: 12px;
  color: #333;
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.15);
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(24, 144, 255, 0.05) 0%, transparent 60%);
  animation: pulse 15s infinite linear;
  z-index: 0;
}

.hero-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCAwaDEwMHYxMDBIMHoiIGZpbGw9Im5vbmUiLz48cGF0aCBkPSJNOTkuOTkyIDAgMCAxMDBoMTAwVjB6IiBmaWxsPSJyZ2JhKDI0LCAxNDQsIDI1NSwgMC4wMykiLz48L3N2Zz4=');
  opacity: 0.5;
  z-index: 0;
}

.hero-title-container {
  position: relative;
  display: inline-flex;
  align-items: center;
  margin-bottom: 16px;
  z-index: 1;
}

.hero-title {
  font-size: 3rem;
  font-weight: 800;
  letter-spacing: -1px;
  background: linear-gradient(to right, #1a1a1a, #1890ff);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(24, 144, 255, 0.1);
  position: relative;
}

.hero-title .highlight {
  background: linear-gradient(to right, #1890ff, #36cfc9);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

.hero-badge {
  background: linear-gradient(45deg, #1890ff, #36cfc9);
  color: white;
  font-size: 1rem;
  font-weight: bold;
  padding: 4px 8px;
  border-radius: 8px;
  margin-left: 12px;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.5);
  position: relative;
  top: -10px;
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 32px;
  opacity: 0.8;
  position: relative;
  z-index: 1;
  color: #333;
  text-shadow: none;
}

.hero-subtitle .highlight {
  color: #1890ff;
  font-weight: 600;
}

@keyframes pulse {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.search-form {
  max-width: 800px;
  margin: 0 auto 24px;
  position: relative;
  z-index: 1;
}

.search-form :deep(.ant-input-affix-wrapper) {
  border-radius: 24px;
  padding: 8px 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(24, 144, 255, 0.2);
  transition: all 0.3s ease;
}

.search-form :deep(.ant-input-affix-wrapper:hover) {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
}

.search-form :deep(.ant-input-affix-wrapper-focused) {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.search-form :deep(.ant-input) {
  font-size: 16px;
}

.search-form :deep(.ant-input-search-button) {
  height: 40px;
  border-radius: 0 24px 24px 0 !important;
  width: 100px;
  background: linear-gradient(90deg, #1890ff, #36cfc9);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.search-form :deep(.ant-input-search-button:hover) {
  background: linear-gradient(90deg, #40a9ff, #5cdbd3);
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.suggested-questions {
  margin-top: 24px;
}

.question-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.question-tag {
  cursor: pointer;
  padding: 8px 16px;
  font-size: 14px;
  border-radius: 20px;
  transition: all 0.3s;
  margin-bottom: 8px;
  background: rgba(24, 144, 255, 0.1);
  border: 1px solid rgba(24, 144, 255, 0.2);
  color: #333;
  position: relative;
  overflow: hidden;
}

.question-tag::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, rgba(24, 144, 255, 0.1), rgba(54, 207, 201, 0.1));
  transform: translateX(-100%);
  transition: transform 0.5s ease;
}

.question-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
  border-color: rgba(24, 144, 255, 0.5);
  color: white;
}

/* 绿色标签的特定样式 */
.result-card .question-tag {
  color: #000000 !important;
  font-weight: 500;
}

/* 确保标签内的文字颜色 */
.followup-tag :deep(span) {
  color: #000000 !important;
}

.question-tag:hover::before {
  transform: translateX(0);
}

.question-tag-disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
  pointer-events: none;
}

.search-input-wrapper {
  position: relative;
}

.clear-button-absolute {
  position: absolute;
  right: 110px; /* 留出查询按钮的空间 */
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  color: #999;
  transition: all 0.3s;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.clear-button-absolute:hover {
  color: #ff4d4f;
  background-color: rgba(255, 77, 79, 0.1);
}

.result-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
  border: 1px solid rgba(24, 144, 255, 0.05);
  overflow: hidden;
  position: relative;
}

.result-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #1890ff, #36cfc9);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.result-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.result-card:hover::before {
  opacity: 1;
}

.result-card :deep(.ant-card-head) {
  border-bottom: 1px solid rgba(24, 144, 255, 0.1);
  background-color: rgba(24, 144, 255, 0.02);
}

.result-card :deep(.ant-card-head-title) {
  font-weight: 600;
  font-size: 16px;
  color: #1890ff;
}

.result-card :deep(.ant-btn-text) {
  color: #1890ff;
  transition: all 0.3s;
}

.result-card :deep(.ant-btn-text:hover) {
  background-color: rgba(24, 144, 255, 0.1);
  color: #40a9ff;
  transform: scale(1.1);
}

.code-block {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;
  border: 1px solid rgba(24, 144, 255, 0.1);
  position: relative;
}

.code-block::before {
  content: 'SQL';
  position: absolute;
  top: 0;
  right: 0;
  background: linear-gradient(90deg, #1890ff, #36cfc9);
  color: white;
  padding: 2px 8px;
  font-size: 12px;
  border-radius: 0 8px 0 8px;
  font-weight: 500;
}

.code-block pre {
  margin: 0;
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  font-size: 14px;
  line-height: 1.5;
  color: #003a70;
}

.data-table {
  margin-top: 8px;
}

.data-table :deep(.ant-table) {
  border-radius: 8px;
  overflow: hidden;
}

.data-table :deep(.ant-table-thead > tr > th) {
  background-color: rgba(24, 144, 255, 0.05);
  color: #1890ff;
  font-weight: 600;
  border-bottom: 1px solid rgba(24, 144, 255, 0.1);
}

.data-table :deep(.ant-table-tbody > tr:hover > td) {
  background-color: rgba(24, 144, 255, 0.03);
}

.data-table :deep(.ant-pagination-item-active) {
  border-color: #1890ff;
}

.data-table :deep(.ant-pagination-item-active a) {
  color: #1890ff;
}

.chart-container {
  width: 100%;
  height: 400px;
  margin-top: 16px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.followup-card {
  background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
  border: 1px solid rgba(82, 196, 26, 0.2);
}

.followup-card :deep(.ant-card-head) {
  background-color: rgba(82, 196, 26, 0.05);
  border-bottom: 1px solid rgba(82, 196, 26, 0.1);
}

.followup-card :deep(.ant-card-head-title) {
  color: #52c41a;
}

/* 确认组件样式 */
.confirmation-card {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f5ff 100%);
  border: 1px solid rgba(24, 144, 255, 0.2);
}

.confirmation-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
}

.confirmation-icon {
  margin-bottom: 16px;
}

.confirmation-icon img {
  width: 48px;
  height: 48px;
}

.confirmation-text {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 20px;
}

.confirmation-buttons {
  display: flex;
  gap: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-section {
    padding: 30px 16px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-subtitle {
    font-size: 1rem;
  }
}

/* 暗黑模式样式 */
[data-theme='dark'] .home-container {
  .hero-section {
    background: linear-gradient(135deg, #1f1f1f 0%, #262626 100%);
    color: rgba(255, 255, 255, 0.85);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);

    &::before {
      background: radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, transparent 60%);
    }

    &::after {
      background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCAwaDEwMHYxMDBIMHoiIGZpbGw9Im5vbmUiLz48cGF0aCBkPSJNOTkuOTkyIDAgMCAxMDBoMTAwVjB6IiBmaWxsPSJyZ2JhKDI0LCAxNDQsIDI1NSwgMC4wNSkiLz48L3N2Zz4=');
      opacity: 0.3;
    }
  }

  .hero-title {
    background: linear-gradient(to right, rgba(255, 255, 255, 0.85), #1890ff);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 10px rgba(24, 144, 255, 0.2);
  }

  .hero-title .highlight {
    background: linear-gradient(to right, #1890ff, #36cfc9);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .hero-badge {
    background: linear-gradient(45deg, #1890ff, #36cfc9);
    color: white;
    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.5);
  }

  .hero-subtitle {
    color: rgba(255, 255, 255, 0.65);
  }

  .features-grid {
    .feature-card {
      background: #262626;
      border-color: #404040;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 8px 24px rgba(24, 144, 255, 0.15);
      }

      .feature-icon {
        background: rgba(24, 144, 255, 0.15);
        color: #1890ff;
      }

      .feature-title {
        color: rgba(255, 255, 255, 0.85);
      }

      .feature-description {
        color: rgba(255, 255, 255, 0.65);
      }
    }
  }

  .quick-start {
    background: #262626;
    border-color: #404040;

    .quick-start-title {
      color: rgba(255, 255, 255, 0.85);
    }

    .quick-start-description {
      color: rgba(255, 255, 255, 0.65);
    }

    .quick-start-buttons {
      .ant-btn {
        &:not(.ant-btn-primary) {
          background: #1a1a1a;
          border-color: #404040;
          color: rgba(255, 255, 255, 0.65);

          &:hover {
            background: #303030;
            border-color: #1890ff;
            color: #1890ff;
          }
        }
      }
    }
  }

  .suggested-questions {
    .suggested-questions-title {
      color: rgba(255, 255, 255, 0.85);
    }

    .question-card {
      background: #262626;
      border-color: #404040;

      &:hover {
        border-color: #1890ff;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
      }

      .question-text {
        color: rgba(255, 255, 255, 0.85);
      }

      .question-meta {
        color: rgba(255, 255, 255, 0.45);
      }
    }
  }

  .stats-section {
    background: #262626;
    border-color: #404040;

    .stats-title {
      color: rgba(255, 255, 255, 0.85);
    }

    .stat-item {
      .stat-number {
        color: #1890ff;
      }

      .stat-label {
        color: rgba(255, 255, 255, 0.65);
      }
    }
  }

  .recent-activity {
    .recent-activity-title {
      color: rgba(255, 255, 255, 0.85);
    }

    .activity-item {
      background: #262626;
      border-color: #404040;

      &:hover {
        background: #303030;
      }

      .activity-icon {
        background: rgba(24, 144, 255, 0.15);
        color: #1890ff;
      }

      .activity-content {
        .activity-title {
          color: rgba(255, 255, 255, 0.85);
        }

        .activity-description {
          color: rgba(255, 255, 255, 0.65);
        }

        .activity-time {
          color: rgba(255, 255, 255, 0.45);
        }
      }
    }
  }

  /* 模态框样式 */
  .confirmation-title {
    color: rgba(255, 255, 255, 0.85);
  }
}

/* 全局暗黑模式下的 Ant Design 组件覆盖 */
[data-theme='dark'] {
  .ant-modal {
    .ant-modal-content {
      background: #262626;

      .ant-modal-header {
        background: #262626;
        border-bottom-color: #404040;

        .ant-modal-title {
          color: rgba(255, 255, 255, 0.85);
        }
      }

      .ant-modal-body {
        background: #262626;
        color: rgba(255, 255, 255, 0.85);
      }

      .ant-modal-footer {
        background: #262626;
        border-top-color: #404040;
      }
    }
  }

  .ant-card {
    background: #262626;
    border-color: #404040;

    .ant-card-head {
      background: #262626;
      border-bottom-color: #404040;

      .ant-card-head-title {
        color: rgba(255, 255, 255, 0.85);
      }
    }

    .ant-card-body {
      background: #262626;
      color: rgba(255, 255, 255, 0.85);
    }
  }

  .ant-btn {
    &:not(.ant-btn-primary):not(.ant-btn-danger) {
      background: #262626;
      border-color: #404040;
      color: rgba(255, 255, 255, 0.65);

      &:hover {
        background: #303030;
        border-color: #1890ff;
        color: #1890ff;
      }
    }
  }
}
</style>
