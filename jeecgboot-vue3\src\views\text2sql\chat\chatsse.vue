<template>
  <div class="text2sql-container">
    <!-- 使用 Ant Design Layout 布局 -->
    <a-layout class="layout">
      <!-- 头部区域 -->
      <a-layout-header class="header">
        <div class="header-content">
          <div class="title-section">
            <h1 class="page-title">{{ t('text2sql.chat.title').substring(0, 9) }}<span class="highlight">{{ t('text2sql.chat.title').substring(9) }}</span></h1>
            <a-tag color="blue" class="ai-badge">{{ t('text2sql.chat.subtitle') }}</a-tag>
          </div>
          
          <!-- 连接状态指示器 -->
          <div 
            :class="['connection-status', getConnectionStatusClass()]" 
            @click="handleConnectionToggle"
          >
            <div class="status-dot"></div>
            <span class="status-text">{{ getConnectionStatusText() }}</span>
          </div>
        </div>
      </a-layout-header>

      <!-- 主要内容区域 -->
      <a-layout class="main-layout">
        <!-- 左侧：输入和控制区域 -->
        <a-layout-sider 
          :width="320" 
          theme="light" 
          class="input-sider"
        >
          <div class="input-panel">
            <!-- 数据库连接选择 -->
            <div class="connection-section">
              <h3>{{ t('text2sql.chat.connection.title') }}</h3>
              <a-select
                v-model:value="selectedConnectionId"
                :placeholder="t('text2sql.chat.connection.placeholder')"
                :loading="connectionsLoading"
                @change="handleConnectionChange"
                style="width: 100%"
              >
                <a-select-option
                  v-for="conn in connections"
                  :key="conn.id"
                  :value="String(conn.id)"
                >
                  {{ conn.name }}
                </a-select-option>
              </a-select>
            </div>

            <!-- 查询输入区域 -->
            <div class="query-section">
              <h3>{{ t('text2sql.chat.query.title') }}</h3>
              <a-textarea
                v-model:value="queryText"
                :placeholder="t('text2sql.chat.query.placeholder')"
                :auto-size="{ minRows: 4, maxRows: 8 }"
                :maxLength="500"
                class="query-input"
                @keydown.enter="handleEnterKeyPress"
              />
              
              <!-- 示例查询 -->
              <div class="example-queries">
                <span class="example-label">{{ t('text2sql.chat.query.examples') }}</span>
                <div class="example-tags">
                  <a-tag 
                    v-for="(example, index) in queryExamples" 
                    :key="index"
                    :color="example.color"
                    class="example-tag"
                    @click="fillExampleQuery(example.query)"
                  >
                    {{ example.label }}
                  </a-tag>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="action-buttons">
                <a-button 
                  type="primary" 
                  size="large"
                  @click="handleSendQuery"
                  :loading="isProcessing"
                  :disabled="!queryText.trim() || !selectedConnectionId"
                  block
                >
                  <template #icon>
                    <SendOutlined />
                  </template>
                  {{ isProcessing ? t('text2sql.chat.query.processing') : t('text2sql.chat.query.startAnalysis') }}
                </a-button>
                <a-button 
                  @click="handleClearResults"
                  :disabled="isProcessing"
                  block
                >
                  {{ t('text2sql.chat.query.clearResults') }}
                </a-button>
              </div>
            </div>

            <!-- 执行状态概览 -->
            <div class="status-overview" v-if="thinkingSteps.some(step => step.status !== 'pending')">
              <h3>执行状态</h3>
              <div class="status-list">
                <div 
                  v-for="(step, index) in thinkingSteps" 
                  :key="step.id"
                  :class="['status-item', `status-${step.status}`]"
                >
                  <div class="status-icon">
                    <CheckCircleOutlined v-if="step.status === 'completed'" />
                    <LoadingOutlined v-else-if="step.status === 'processing'" />
                    <ClockCircleOutlined v-else />
                  </div>
                  <span class="status-label">{{ step.label }}</span>
                </div>
              </div>
            </div>
          </div>
        </a-layout-sider>

        <!-- 右侧：思维链输出区域 -->
        <a-layout-content 
          ref="outputContentRef"
          class="output-content"
          v-auto-scroll="{ 
            active: (isProcessing || hasAnyContent) && !userScrolled && !hasFeedbackRequest, 
            smooth: true,
            offset: 0
          }"
        >
          <div class="thinking-chain-container">
            <!-- 空状态 -->
            <div v-if="!hasAnyContent" class="empty-state">
              <a-empty 
                description="请输入查询内容开始分析"
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
              >
                <template #image>
                  <DatabaseOutlined style="font-size: 64px; color: #d9d9d9;" />
                </template>
              </a-empty>
            </div>

            <!-- 思维链展示 -->
            <div v-else class="thinking-chain-content">
              <div class="chain-header">
                <h2>分析过程</h2>
                <a-tag 
                  :color="isProcessing ? 'processing' : 'success'"
                  class="process-tag"
                >
                  {{ isProcessing ? '分析中' : '分析完成' }}
                </a-tag>
              </div>

              <!-- 思维链步骤 -->
              <div class="chain-steps">
                <div 
                  v-for="(step, index) in thinkingSteps" 
                  :key="step.id"
                  v-show="step.content || step.status !== 'pending' || step.needsFeedback"
                  class="chain-step"
                >
                  <div class="step-card">
                    <div class="step-header">
                      <div class="step-title">
                        <div :class="['step-icon', `icon-${step.status}`]">
                          <CheckCircleOutlined v-if="step.status === 'completed'" />
                          <LoadingOutlined v-else-if="step.status === 'processing'" />
                          <ExclamationCircleOutlined v-else-if="step.status === 'error'" />
                          <span v-else class="step-number">{{ index + 1 }}</span>
                        </div>
                        <h3>{{ step.label }}</h3>
                      </div>
                      <div class="step-time" v-if="step.timestamp">
                        {{ formatTime(step.timestamp) }}
                      </div>
                    </div>
                    
                    <div v-if="step.content" class="step-content">
                      <MarkdownRenderer :content="step.content" :theme="currentTheme" />
                    </div>

                    <!-- 特殊内容展示 -->
                    <div v-if="step.id === 'data' && currentResults.data" class="step-results">
                      <a-divider>查询结果</a-divider>
                      <a-table
                        :dataSource="currentResults.data"
                        :columns="currentResults.columns"
                        :pagination="tablePagination"
                        size="small"
                        :scroll="{ x: 'max-content' }"
                      />
                    </div>

                    <div v-if="step.id === 'visualization' && currentResults.chartConfig" class="step-visualization">
                      <a-divider>数据可视化</a-divider>
                      <div 
                        class="chart-container" 
                        :ref="el => setChartRef(el, `chart-${index}`)"
                      ></div>
                    </div>

                    <!-- 用户反馈区域 -->
                    <div v-if="step.needsFeedback" class="feedback-section">
                      <a-divider>需要您的确认</a-divider>
                      <div class="feedback-prompt">{{ step.feedbackPrompt }}</div>
                      <div class="feedback-actions">
                        <a-textarea 
                          v-model:value="feedbackText"
                          placeholder="请输入您的反馈..."
                          :auto-size="{ minRows: 2, maxRows: 4 }"
                          style="margin-bottom: 12px"
                        />
                        <a-space>
                          <a-button 
                            type="primary" 
                            @click="submitFeedback(step.id)"
                            :disabled="!feedbackText.trim()"
                          >
                            提交反馈
                          </a-button>
                          <a-button 
                            type="default" 
                            @click="approveFeedback(step.id)"
                          >
                            同意继续
                          </a-button>
                        </a-space>
                      </div>
                    </div>

                    <!-- 用户已提交的反馈显示区域 -->
                    <div v-if="step.userFeedback && step.userFeedback.length > 0" class="user-feedback-display">
                      <a-divider>用户反馈</a-divider>
                      <div class="feedback-list">
                        <div 
                          v-for="(feedback, index) in step.userFeedback" 
                          :key="index"
                          class="feedback-item"
                        >
                          <div class="feedback-content">
                            <div class="feedback-icon">
                              <CheckCircleOutlined style="color: #52c41a; margin-right: 8px;" />
                            </div>
                            <div class="feedback-text">{{ feedback }}</div>
                            <div class="feedback-time">第{{ index + 1 }}次反馈</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 导出功能区域 -->
              <div v-if="hasAnyContent && !isProcessing" class="export-section">
                <a-divider>导出功能</a-divider>
                <div class="export-buttons">
                  <a-space size="large" direction="vertical">
                    <a-space size="large">
                      <a-button 
                        type="primary" 
                        size="large"
                        @click="handleExportPDF"
                        :loading="exportingPDF"
                        class="export-btn export-pdf-btn"
                      >
                        <template #icon>
                          <FilePdfOutlined />
                        </template>
                        导出分析报告 (PDF)
                      </a-button>
                      
                      <a-button 
                        type="primary" 
                        size="large"
                        @click="handleExportWord"
                        :loading="exportingWord"
                        class="export-btn export-word-btn"
                      >
                        <template #icon>
                          <FileWordOutlined />
                        </template>
                        导出分析报告 (Word)
                      </a-button>
                    </a-space>
                    
                    <a-button 
                      type="default" 
                      size="large"
                      @click="handleExportExcel"
                      :loading="exportingExcel"
                      :disabled="!currentResults.data || currentResults.data.length === 0"
                      class="export-btn export-excel-btn"
                    >
                      <template #icon>
                        <FileExcelOutlined />
                      </template>
                      导出查询结果 (Excel)
                    </a-button>
                  </a-space>
                  
                  <div class="export-tips">
                    <span class="export-tips-text">
                      <DownloadOutlined style="margin-right: 4px;" />
                      PDF/Word包含完整分析过程，Excel仅包含查询结果数据
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 回到顶部按钮 -->
          <div 
            v-show="showBackTop"
            @click="scrollToTop"
            class="back-to-top"
          >
            <div class="back-to-top-button">
              <UpOutlined />
            </div>
          </div>
        </a-layout-content>
      </a-layout>
    </a-layout>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, watch, onBeforeUnmount, computed } from 'vue'
import { message, Empty } from 'ant-design-vue'
import { useI18n } from '/@/hooks/web/useI18n'
import { useRootSetting } from '/@/hooks/setting/useRootSetting'
import { ThemeEnum } from '/@/enums/appEnum'
import {
  SendOutlined,
  CheckCircleOutlined,
  LoadingOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  DatabaseOutlined,
  UpOutlined,
  DownloadOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  FileWordOutlined
} from '@ant-design/icons-vue'
import MarkdownRenderer from './components/MarkdownRenderer.vue'
import autoScroll from '/@/directives/scroll'
import {
  useConnections,
  useSSEConnection,
  useThinkingChain,
  useQueryProcessing,
  useQueryResults,
  useScrollControl,
  useExport
} from './composables'

// 国际化
const { t } = useI18n()

// 主题
const { getDarkMode } = useRootSetting()
const currentTheme = computed(() => getDarkMode.value === ThemeEnum.DARK ? 'dark' : 'light')

// 模板引用
const outputContentRef = ref<any>(null)

// 使用各个功能模块的 composables
const {
  selectedConnectionId,
  connections,
  connectionsLoading,
  handleConnectionChange,
  loadConnections
} = useConnections()

const {
  sessionId,
  userId,
  connectionStatus,
  getConnectionStatusClass,
  getConnectionStatusText,
  setMessageHandler,
  connectSSE,
  disconnectSSE,
  cancelSession,
  handleConnectionToggle
} = useSSEConnection()

const {
  feedbackText,
  isProcessing,
  thinkingSteps,
  hasAnyContent,
  hasFeedbackRequest,
  formatTime,
  resetThinkingChain,
  updateThinkingStep,
  submitFeedback: submitStepFeedback,
  approveFeedback: approveStepFeedback
} = useThinkingChain()

const {
  queryText,
  queryExamples,
  fillExampleQuery,
  handleSendQuery: handleQuerySend,
  handleEnterKeyPress: handleQueryEnterKeyPress,
  loadQueryExamples
} = useQueryProcessing()

const {
  currentResults,
  tablePagination,
  chartInstances,
  clearResults,
  handleResultMessage,
  handleResize,
  setChartRef
} = useQueryResults()

const {
  showBackTop,
  userScrolled,
  resetUserScroll,
  scrollToTop: scrollToTopFn,
  setupScrollListener,
  cleanup: cleanupScroll
} = useScrollControl()

const {
  exportingPDF,
  exportingWord,
  exportingExcel,
  handleExportPDF: exportPDF,
  handleExportWord: exportWord,
  handleExportExcel: exportExcel
} = useExport()

// 统一的消息处理函数
const handleMessage = (data: any) => {
  // 更新思维链步骤
  updateThinkingStep(data, outputContentRef)
  
  // 处理特殊消息类型
  if (data.result) {
    handleResultMessage(data)
    
    // 如果有可视化配置，确保可视化步骤可见
    if (data.result.visualization_config) {
      const visualizationStep = thinkingSteps.value.find(step => step.id === 'visualization')
      if (visualizationStep) {
        visualizationStep.status = 'completed'
        visualizationStep.timestamp = Date.now()
        if (!visualizationStep.content) {
          visualizationStep.content = '正在生成数据可视化图表...'
        }
      }
    }
  }
}

// 包装的方法
const handleSendQuery = async () => {
  await handleQuerySend(
    selectedConnectionId.value,
    sessionId.value,
    userId.value,
    connectionStatus.value,
    connectSSE,
    resetThinkingChain,
    clearResults,
    (value: boolean) => { isProcessing.value = value },
    resetUserScroll
  )
}

const handleEnterKeyPress = (event: KeyboardEvent) => {
  handleQueryEnterKeyPress(
    event,
    isProcessing.value,
    selectedConnectionId.value,
    handleSendQuery
  )
}

const handleClearResults = () => {
  resetThinkingChain()
  clearResults()
  queryText.value = ''
  message.success('结果已清空')
}

const submitFeedback = async (stepId: string) => {
  await submitStepFeedback(stepId, sessionId.value!)
}

const approveFeedback = async (stepId: string) => {
  await approveStepFeedback(stepId, sessionId.value!)
}

const scrollToTop = () => {
  scrollToTopFn(outputContentRef)
}

const handleExportPDF = async () => {
  await exportPDF(hasAnyContent.value, queryText.value)
}

const handleExportWord = async () => {
  await exportWord(hasAnyContent.value, queryText.value)
}

const handleExportExcel = () => {
  exportExcel(currentResults.value, queryText.value)
}

// 设置消息处理器
setMessageHandler(handleMessage)

// 生命周期
onMounted(async () => {
  await loadConnections()
  await connectSSE()
  await loadQueryExamples()
  // 设置滚动监听
  nextTick(() => {
    setupScrollListener(outputContentRef)
  })
})

onBeforeUnmount(async () => {
  // 取消会话
  await cancelSession()
  
  // 断开SSE连接
  disconnectSSE()
  
  // 清理图表实例
  chartInstances.forEach(chart => chart.dispose())
  chartInstances.clear()
  
  // 清理滚动相关
  cleanupScroll()
  
  // 移除窗口事件监听器
  window.removeEventListener('resize', handleResize)
})

// 监听窗口大小变化，调整图表
window.addEventListener('resize', handleResize)

// 监听思维链步骤变化
watch(
  () => thinkingSteps.value.map(step => ({ status: step.status, content: step.content })),
  () => {
    // 思维链变化的处理逻辑由 v-auto-scroll 指令处理
  },
  { deep: true }
)

// 注册自定义指令
const vAutoScroll = autoScroll
</script>
<style scoped lang="less">
@import './styles/chatsse.less';
</style>
