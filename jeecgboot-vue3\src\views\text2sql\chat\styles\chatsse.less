.text2sql-container {
  height: calc(100vh - 110px);
  background-color: #f5f7fa;
}

.layout {
  height: 100%;
}

/* 头部样式 */
.header {
  background: linear-gradient(135deg, #ffffff 0%, #f0f7ff 100%);
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.15);
  padding: 0 0px;
  height: 64px;
  line-height: 64px;
  position: relative;
  overflow: hidden;
  border-radius: 0 0 12px 12px;
}

.header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(24, 144, 255, 0.05) 0%, transparent 60%);
  z-index: 0;
}

.header::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCAwaDEwMHYxMDBIMHoiIGZpbGw9Im5vbmUiLz48cGF0aCBkPSJNOTkuOTkyIDAgMCAxMDBoMTAwVjB6IiBmaWxsPSJyZ2JhKDI0LCAxNDQsIDI1NSwgMC4wMykiLz48L3N2Zz4=');
  opacity: 0.5;
  z-index: 0;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 100%;
  margin: 0 auto;
  height: 100%;
  position: relative;
  z-index: 1;
}

.title-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  margin: 0;
  text-align: center;
  letter-spacing: 0.5px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  background: linear-gradient(to right, #1a1a1a, #1890ff);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 10px rgba(24, 144, 255, 0.1);
}

.page-title .highlight {
  background: linear-gradient(to right, #1890ff, #36cfc9);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.ai-badge {
  font-weight: bold;
  background: linear-gradient(45deg, #1890ff, #36cfc9);
  color: white;
  border: none;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.4);
  position: relative;
  top: -2px;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 10px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e8e8e8;
  cursor: pointer;
  transition: all 0.3s;
  margin-left: auto;
  margin-right: 1px;
}

.connection-status:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-disconnected .status-dot {
  background-color: #8c8c8c;
}

.status-connecting .status-dot {
  background-color: #1890ff;
  animation: pulse 1.5s infinite;
}

.status-connected .status-dot {
  background-color: #52c41a;
}

.status-error .status-dot {
  background-color: #f5222d;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.4; }
}

.status-text {
  font-size: 14px;
  font-weight: 500;
}

/* 主要布局 */
.main-layout {
  height: calc(100vh - 64px);
}

/* 左侧输入面板 */
.input-sider {
  background: white;
  border-right: 1px solid #f0f0f0;
}

.input-panel {
  padding: 16px;
  height: 100%;
  overflow-y: auto;
}

.connection-section,
.query-section,
.status-overview {
  margin-bottom: 32px;
}

.connection-section h3,
.query-section h3,
.status-overview h3 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.query-input {
  margin-bottom: 16px;
}

.example-queries {
  margin-bottom: 24px;
}

.example-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #8c8c8c;
}

.example-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.example-tag {
  cursor: pointer;
  transition: all 0.3s;
}

.example-tag:hover {
  //transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0,0,0,0.15);
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  background: #fafafa;
  transition: all 0.3s;
}

.status-pending {
  background: #fafafa;
  color: #8c8c8c;
}

.status-processing {
  background: #e6f7ff;
  color: #1890ff;
}

.status-completed {
  background: #f6ffed;
  color: #52c41a;
}

.status-error {
  background: #fff2f0;
  color: #f5222d;
}

.status-icon {
  font-size: 14px;
}

.status-label {
  font-size: 14px;
  font-weight: 500;
}

/* 右侧输出区域 */
.output-content {
  background: white;
  padding: 24px 32px;
  overflow-y: auto;
  position: relative;
}

.thinking-chain-container {
  width: 100%;
  margin: 0;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

.thinking-chain-content {
  height: 100%;
}

.chain-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.chain-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.process-tag {
  font-weight: 500;
}

.chain-steps {
  display: flex;
  flex-direction: column;
  gap: 24px;
  padding-bottom: 24px;
}

.chain-step {
  opacity: 0;
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.step-card {
  background: white;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  transition: all 0.3s;
}

.step-card:hover {
  box-shadow: 0 4px 16px rgba(0,0,0,0.08);
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.step-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s;
}

.icon-pending {
  background-color: #f5f5f5;
  color: #8c8c8c;
  border: 2px solid #e8e8e8;
}

.icon-processing {
  background-color: #e6f7ff;
  color: #1890ff;
  border: 2px solid #1890ff;
}

.icon-completed {
  background-color: #f6ffed;
  color: #52c41a;
  border: 2px solid #52c41a;
}

.icon-error {
  background-color: #fff2f0;
  color: #f5222d;
  border: 2px solid #f5222d;
}

.step-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.step-time {
  font-size: 12px;
  color: #8c8c8c;
}

.step-content {
  color: #595959;
  line-height: 1.6;
  margin-bottom: 16px;
}

.step-results{
  position: relative;
  padding-bottom: 90px;
}
.step-visualization {
  margin-top: 16px;
}

.chart-container {
  width: 100%;
  height: 400px;
  border-radius: 8px;
  background: #fafafa;
}

.feedback-section {
  margin-top: 16px;
  padding: 16px;
  background: #f0f7ff;
  border-radius: 8px;
  border: 1px solid #d6e4ff;
}

.feedback-prompt {
  margin-bottom: 12px;
  color: #1890ff;
  font-weight: 500;
}

.feedback-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 用户反馈显示区域样式 */
.user-feedback-display {
  margin-top: 16px;
  padding: 16px;
  background: #f6ffed;
  border-radius: 8px;
  border: 1px solid #b7eb8f;
}

.feedback-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feedback-item {
  padding: 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  border: 1px solid #d9f7be;
}

.feedback-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
}

.feedback-icon {
  flex-shrink: 0;
  margin-top: 2px;
}

.feedback-text {
  flex: 1;
  color: #52c41a;
  font-weight: 500;
  line-height: 1.5;
}

.feedback-time {
  font-size: 12px;
  color: #8c8c8c;
  margin-left: auto;
  flex-shrink: 0;
}

/* 回到顶部按钮样式 */
.back-to-top {
  position: fixed !important;
  right: 80px;
  bottom: 32px;
  z-index: 1000;
}

.back-to-top-button {
  width: 40px;
  height: 40px;
  background: #1890ff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.2s ease;
}

.back-to-top-button:hover {
  background: #40a9ff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.back-to-top-button:active {
  transform: translateY(0);
}

/* 导出功能区域样式 */
.export-section {
  margin-top: 32px;
  padding: 24px;
  background: #fafafa;
  border-radius: 12px;
  border: 1px solid #f0f0f0;
}

.export-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.export-btn {
  min-width: 200px;
  height: 48px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.export-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.export-pdf-btn {
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  border-color: #ff4d4f;
}

.export-pdf-btn:hover {
  background: linear-gradient(135deg, #ff7875 0%, #ffa39e 100%);
  border-color: #ff7875;
}

.export-word-btn {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border-color: #1890ff;
}

.export-word-btn:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
  border-color: #40a9ff;
}

.export-excel-btn {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border-color: #52c41a;
  color: white;
}

.export-excel-btn:hover {
  background: linear-gradient(135deg, #73d13d 0%, #95de64 100%);
  border-color: #73d13d;
  color: white;
}

.export-excel-btn:disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.export-tips {
  text-align: center;
  margin-top: 8px;
}

.export-tips-text {
  font-size: 12px;
  color: #8c8c8c;
}

/* 响应式设计 */
@media (min-width: 1400px) {
  .input-sider {
    width: 300px !important;
  }
}

@media (max-width: 1200px) {
  .input-sider {
    width: 350px !important;
  }
}

@media (max-width: 992px) {
  .main-layout {
    flex-direction: column;
  }

  .input-sider {
    width: 100% !important;
    max-width: 100% !important;
    flex: none !important;
    height: auto;
  }

  .input-panel {
    height: auto;
    max-height: 300px;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    height: auto;
    padding: 12px 0;
    gap: 16px;
    position: relative;
  }

  .title-section {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    justify-content: center;
  }

  .page-title {
    font-size: 20px;
  }

  .connection-status {
    padding: 6px 12px;
    font-size: 12px;
    margin-left: 0;
    margin-right: 4px;
  }

  .status-dot {
    width: 6px;
    height: 6px;
  }

  .header {
    height: auto;
    line-height: normal;
    padding: 0 16px;
  }

  .main-layout {
    height: calc(100vh - 80px);
  }

  .input-panel,
  .output-content {
    padding: 12px;
  }

  .example-tags {
    flex-direction: column;
  }

  .back-to-top {
    right: 16px;
    bottom: 16px;
  }

  .back-to-top-button {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  /* 导出功能响应式样式 */
  .export-section {
    margin-top: 24px;
    padding: 16px;
  }

  .export-buttons {
    gap: 12px;
  }

  .export-btn {
    min-width: 160px;
    height: 44px;
    font-size: 13px;
  }

  .export-tips {
    font-size: 11px;
  }
}

/* 暗黑模式样式 */
[data-theme='dark'] {
  .text2sql-container {
    background-color: #141414;
  }

  /* 头部样式 */
  .header {
    background: linear-gradient(135deg, #1f1f1f 0%, #262626 100%);
    border-bottom-color: #303030;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);

    &::before {
      background: radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, transparent 60%);
    }

    &::after {
      background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCAwaDEwMHYxMDBIMHoiIGZpbGw9Im5vbmUiLz48cGF0aCBkPSJNOTkuOTkyIDAgMCAxMDBoMTAwVjB6IiBmaWxsPSJyZ2JhKDI0LCAxNDQsIDI1NSwgMC4wNSkiLz48L3N2Zz4=');
      opacity: 0.3;
    }
  }

  .page-title {
    background: linear-gradient(to right, rgba(255, 255, 255, 0.85), #1890ff);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 10px rgba(24, 144, 255, 0.2);
  }

  .page-title .highlight {
    background: linear-gradient(to right, #1890ff, #36cfc9);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .ai-badge {
    background: linear-gradient(45deg, #1890ff, #36cfc9);
    color: white;
    box-shadow: 0 2px 6px rgba(24, 144, 255, 0.5);
  }

  .connection-status {
    background: rgba(31, 31, 31, 0.9);
    border-color: #404040;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
  }

  .status-text {
    color: rgba(255, 255, 255, 0.85);
  }

  /* 左侧输入面板 */
  .input-sider {
    background: #1f1f1f;
    border-right-color: #303030;
  }

  .input-panel {
    background: #1f1f1f;
  }

  .connection-section h3,
  .query-section h3,
  .status-overview h3 {
    color: rgba(255, 255, 255, 0.85);
  }

  .example-label {
    color: rgba(255, 255, 255, 0.45);
  }

  .status-item {
    background: #262626;
    color: rgba(255, 255, 255, 0.65);
  }

  .status-pending {
    background: #262626;
    color: rgba(255, 255, 255, 0.45);
  }

  .status-processing {
    background: rgba(24, 144, 255, 0.15);
    color: #40a9ff;
  }

  .status-completed {
    background: rgba(82, 196, 26, 0.15);
    color: #73d13d;
  }

  .status-error {
    background: rgba(255, 77, 79, 0.15);
    color: #ff7875;
  }

  /* 右侧输出区域 */
  .output-content {
    background: #1f1f1f;
    color: rgba(255, 255, 255, 0.85);
  }

  .chain-header {
    border-bottom-color: #303030;

    h2 {
      color: rgba(255, 255, 255, 0.85);
    }
  }

  .chain-step {
    background: #262626;
    border-color: #404040;

    &.active {
      border-color: #1890ff;
      box-shadow: 0 0 0 1px rgba(24, 144, 255, 0.2);
    }

    &.completed {
      border-color: #52c41a;
      background: rgba(82, 196, 26, 0.05);
    }

    &.error {
      border-color: #f5222d;
      background: rgba(245, 34, 45, 0.05);
    }
  }

  .step-header {
    .step-title {
      color: rgba(255, 255, 255, 0.85);
    }

    .step-status {
      &.processing {
        color: #1890ff;
      }

      &.completed {
        color: #52c41a;
      }

      &.error {
        color: #f5222d;
      }
    }
  }

  .step-content {
    color: rgba(255, 255, 255, 0.65);

    .sql-code {
      background: #1a1a1a;
      border-color: #404040;
      color: rgba(255, 255, 255, 0.85);
    }

    .result-table {
      background: #1a1a1a;
      border-color: #404040;

      .ant-table {
        background: #1a1a1a;

        .ant-table-thead > tr > th {
          background: #262626;
          border-bottom-color: #404040;
          color: rgba(255, 255, 255, 0.85);
        }

        .ant-table-tbody > tr > td {
          border-bottom-color: #303030;
          color: rgba(255, 255, 255, 0.65);
        }

        .ant-table-tbody > tr:hover > td {
          background: #262626;
        }
      }
    }

    .error-message {
      background: rgba(245, 34, 45, 0.1);
      border-color: #ff7875;
      color: #ff7875;
    }
  }

  /* 按钮样式 */
  .ant-btn {
    &:not(.ant-btn-primary):not(.ant-btn-danger) {
      background: #262626;
      border-color: #404040;
      color: rgba(255, 255, 255, 0.65);

      &:hover {
        background: #303030;
        border-color: #1890ff;
        color: #1890ff;
      }
    }
  }

  /* 输入框样式 */
  .ant-input,
  .ant-select-selector {
    background: #262626 !important;
    border-color: #404040 !important;
    color: rgba(255, 255, 255, 0.85) !important;

    &:hover {
      border-color: #1890ff !important;
    }

    &:focus,
    &.ant-select-focused .ant-select-selector {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
    }
  }

  .ant-input::placeholder {
    color: rgba(255, 255, 255, 0.45) !important;
  }

  .ant-select-selection-placeholder {
    color: rgba(255, 255, 255, 0.45) !important;
  }

  .ant-select-selection-item {
    color: rgba(255, 255, 255, 0.85) !important;
  }

  .ant-select-arrow {
    color: rgba(255, 255, 255, 0.45) !important;
  }

  /* 标签样式 */
  .ant-tag {
    background: #262626;
    border-color: #404040;
    color: rgba(255, 255, 255, 0.65);

    &:hover {
      background: rgba(24, 144, 255, 0.1);
      border-color: #1890ff;
      color: #1890ff;
    }
  }

  /* 回到顶部按钮 */
  .back-to-top-button {
    background: #1890ff;
    border-color: #1890ff;
    color: white;
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);

    &:hover {
      background: #40a9ff;
      border-color: #40a9ff;
      box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
    }
  }

  /* 导出按钮 */
  .export-btn {
    background: #262626;
    border-color: #404040;
    color: rgba(255, 255, 255, 0.65);

    &:hover {
      background: #303030;
      border-color: #1890ff;
      color: #1890ff;
    }

    &.primary {
      background: #1890ff;
      border-color: #1890ff;
      color: white;

      &:hover {
        background: #40a9ff;
        border-color: #40a9ff;
      }
    }
  }

  .export-tips {
    color: rgba(255, 255, 255, 0.45);
  }

  /* 思维链步骤卡片暗黑模式 */
  .step-card {
    background: #262626 !important;
    border-color: #404040 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;

    &:hover {
      box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15) !important;
      border-color: #1890ff !important;
    }
  }

  .step-header {
    .step-title {
      h3 {
        color: rgba(255, 255, 255, 0.85) !important;
      }
    }

    .step-time {
      color: rgba(255, 255, 255, 0.45) !important;
    }
  }

  .step-content {
    color: rgba(255, 255, 255, 0.85) !important;
  }

  /* 用户反馈区域暗黑模式 */
  .feedback-section {
    background: rgba(24, 144, 255, 0.1) !important;
    border-color: rgba(24, 144, 255, 0.3) !important;

    .feedback-prompt {
      color: rgba(255, 255, 255, 0.85) !important;
    }
  }

  /* 用户反馈显示区域暗黑模式 */
  .user-feedback-display {
    background: rgba(82, 196, 26, 0.1) !important;
    border-color: rgba(82, 196, 26, 0.3) !important;

    .feedback-item {
      .feedback-content {
        .feedback-text {
          color: rgba(255, 255, 255, 0.85) !important;
        }

        .feedback-time {
          color: rgba(255, 255, 255, 0.45) !important;
        }
      }
    }
  }

  /* 导出功能区域暗黑模式 */
  .export-section {
    background: #262626 !important;
    border-color: #404040 !important;

    .export-tips {
      .export-tips-text {
        color: rgba(255, 255, 255, 0.45) !important;
      }
    }
  }

  /* 查询结果表格暗黑模式 */
  .step-results {
    .ant-table {
      background: #1a1a1a !important;

      .ant-table-thead > tr > th {
        background: #262626 !important;
        border-bottom-color: #404040 !important;
        color: rgba(255, 255, 255, 0.85) !important;
      }

      .ant-table-tbody > tr > td {
        border-bottom-color: #303030 !important;
        color: rgba(255, 255, 255, 0.65) !important;
        background: #1a1a1a !important;
      }

      .ant-table-tbody > tr:hover > td {
        background: #262626 !important;
      }
    }
  }

  /* 可视化图表容器暗黑模式 */
  .step-visualization {
    .chart-container {
      background: #1a1a1a !important;
      border-color: #404040 !important;
    }
  }

  /* 分隔线暗黑模式 */
  .ant-divider {
    border-color: #404040 !important;

    .ant-divider-inner-text {
      color: rgba(255, 255, 255, 0.85) !important;
    }
  }

  /* 空状态暗黑模式 */
  .empty-state {
    .ant-empty {
      .ant-empty-description {
        color: rgba(255, 255, 255, 0.45) !important;
      }
    }
  }

  /* 标签暗黑模式 */
  .process-tag {
    &.ant-tag {
      background: #262626 !important;
      border-color: #404040 !important;
      color: rgba(255, 255, 255, 0.65) !important;
    }
  }

  /* 全局Ant Design组件暗黑模式覆盖 */
  .ant-layout {
    background: #1f1f1f !important;
  }

  .ant-layout-sider {
    background: #1f1f1f !important;
  }

  .ant-layout-content {
    background: #1f1f1f !important;
  }

  /* 文本域暗黑模式 */
  .ant-input {
    background: #262626 !important;
    border-color: #404040 !important;
    color: rgba(255, 255, 255, 0.85) !important;

    &:hover {
      border-color: #1890ff !important;
    }

    &:focus {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
    }

    &::placeholder {
      color: rgba(255, 255, 255, 0.45) !important;
    }
  }

  /* 文本域特殊处理 */
  .ant-input.ant-input-textarea {
    background: #262626 !important;
    border-color: #404040 !important;
    color: rgba(255, 255, 255, 0.85) !important;
  }

  /* 选择器暗黑模式 */
  .ant-select {
    .ant-select-selector {
      background: #262626 !important;
      border-color: #404040 !important;
      color: rgba(255, 255, 255, 0.85) !important;

      &:hover {
        border-color: #1890ff !important;
      }
    }

    &.ant-select-focused .ant-select-selector {
      border-color: #1890ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
    }

    .ant-select-selection-placeholder {
      color: rgba(255, 255, 255, 0.45) !important;
    }

    .ant-select-selection-item {
      color: rgba(255, 255, 255, 0.85) !important;
    }

    .ant-select-arrow {
      color: rgba(255, 255, 255, 0.45) !important;
    }
  }

  /* 按钮暗黑模式 */
  .ant-btn {
    &:not(.ant-btn-primary):not(.ant-btn-danger) {
      background: #262626 !important;
      border-color: #404040 !important;
      color: rgba(255, 255, 255, 0.65) !important;

      &:hover {
        background: #303030 !important;
        border-color: #1890ff !important;
        color: #1890ff !important;
      }

      &:disabled {
        background: #1a1a1a !important;
        border-color: #404040 !important;
        color: rgba(255, 255, 255, 0.25) !important;
      }
    }
  }

  /* 分页组件暗黑模式 */
  .ant-pagination {
    .ant-pagination-item {
      background: #262626 !important;
      border-color: #404040 !important;

      a {
        color: rgba(255, 255, 255, 0.65) !important;
      }

      &:hover {
        border-color: #1890ff !important;

        a {
          color: #1890ff !important;
        }
      }
    }

    .ant-pagination-item-active {
      background: #1890ff !important;
      border-color: #1890ff !important;

      a {
        color: white !important;
      }
    }

    .ant-pagination-prev,
    .ant-pagination-next {
      background: #262626 !important;
      border-color: #404040 !important;
      color: rgba(255, 255, 255, 0.65) !important;

      &:hover {
        border-color: #1890ff !important;
        color: #1890ff !important;
      }

      &.ant-pagination-disabled {
        background: #1a1a1a !important;
        border-color: #404040 !important;
        color: rgba(255, 255, 255, 0.25) !important;
      }
    }
  }

  /* MarkdownRenderer组件暗黑模式强制覆盖 */
  .markdown-container {
    background-color: transparent !important;
    color: rgba(255, 255, 255, 0.85) !important;

    .markdown-content {
      background-color: transparent !important;
      color: rgba(255, 255, 255, 0.85) !important;

      h1, h2, h3, h4, h5, h6 {
        color: rgba(255, 255, 255, 0.85) !important;
        border-bottom-color: #404040 !important;
      }

      p {
        color: rgba(255, 255, 255, 0.85) !important;
      }

      a {
        color: #58a6ff !important;

        &:hover {
          color: #79c0ff !important;
        }
      }

      strong {
        color: rgba(255, 255, 255, 0.85) !important;
      }

      em {
        color: rgba(255, 255, 255, 0.85) !important;
      }

      code {
        background-color: #262626 !important;
        color: #f85149 !important;
        border-color: #404040 !important;
      }

      pre {
        background-color: #1a1a1a !important;
        border-color: #404040 !important;

        code {
          background-color: transparent !important;
          color: rgba(255, 255, 255, 0.85) !important;
        }
      }

      blockquote {
        color: rgba(255, 255, 255, 0.65) !important;
        border-left-color: #404040 !important;
      }

      table {
        border-color: #404040 !important;

        th {
          background-color: #262626 !important;
          border-color: #404040 !important;
          color: rgba(255, 255, 255, 0.85) !important;
        }

        td {
          border-color: #404040 !important;
          color: rgba(255, 255, 255, 0.85) !important;
        }

        tr:nth-child(2n) {
          background-color: #1a1a1a !important;
        }
      }

      ul, ol {
        color: rgba(255, 255, 255, 0.85) !important;

        li {
          color: rgba(255, 255, 255, 0.85) !important;
        }
      }

      hr {
        background-color: #404040 !important;
        border-color: #404040 !important;
      }

      /* 代码高亮暗黑模式 */
      .hljs {
        background: #1a1a1a !important;
        color: rgba(255, 255, 255, 0.85) !important;
      }

      /* Prism代码高亮暗黑模式 */
      .language-sql .token.keyword {
        color: #569cd6 !important;
      }

      .language-sql .token.string {
        color: #ce9178 !important;
      }

      .language-sql .token.number {
        color: #b5cea8 !important;
      }

      .language-sql .token.operator {
        color: #d4d4d4 !important;
      }

      .language-sql .token.punctuation {
        color: #d4d4d4 !important;
      }

      .language-sql .token.comment {
        color: #6a9955 !important;
      }

      .language-sql .token.function {
        color: #dcdcaa !important;
      }
    }
  }

  /* 思维链容器暗黑模式 */
  .thinking-chain-container {
    background: transparent !important;
  }

  .thinking-chain-content {
    background: transparent !important;
  }

  .chain-header {
    h2 {
      color: rgba(255, 255, 255, 0.85) !important;
    }
  }

  .chain-steps {
    background: transparent !important;
  }

  .chain-step {
    background: transparent !important;
  }

  /* 步骤图标暗黑模式 */
  .step-icon {
    color: rgba(255, 255, 255, 0.65) !important;

    &.icon-completed {
      color: #52c41a !important;
    }

    &.icon-processing {
      color: #1890ff !important;
    }

    &.icon-error {
      color: #f5222d !important;
    }

    .step-number {
      background: #1890ff !important;
      color: white !important;
    }
  }

  /* 强制覆盖所有可能的白色背景 */
  * {
    &:not(.ant-btn-primary):not(.ant-btn-danger):not(.ant-tag-processing):not(.ant-tag-success) {
      &[style*="background: white"],
      &[style*="background-color: white"],
      &[style*="background: #fff"],
      &[style*="background-color: #fff"] {
        background: #262626 !important;
      }
    }
  }

  /* 确保所有容器都有正确的背景 */
  div, section, article, main {
    &:not(.ant-btn):not(.ant-tag):not([class*="ant-"]) {
      background-color: transparent !important;
    }
  }

  /* EnhancedCodeHighlight组件强制暗黑模式 */
  .code-highlight-container {
    background: #1a1a1a !important;
    color: rgba(255, 255, 255, 0.85) !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
    border-color: #404040 !important;

    .line-numbers {
      color: rgba(255, 255, 255, 0.45) !important;
      background-color: #262626 !important;
      border-right-color: #404040 !important;
    }

    pre {
      background-color: #1a1a1a !important;
      color: rgba(255, 255, 255, 0.85) !important;

      code {
        background-color: #1a1a1a !important;
        color: rgba(255, 255, 255, 0.85) !important;
      }
    }

    .action-btn {
      background: #262626 !important;
      border-color: #404040 !important;
      color: rgba(255, 255, 255, 0.65) !important;

      &:hover {
        background: #303030 !important;
        border-color: #1890ff !important;
        color: #1890ff !important;
      }
    }
  }

  /* 强制覆盖所有pre和code元素 */
  pre, code {
    background: #1a1a1a !important;
    color: rgba(255, 255, 255, 0.85) !important;
    border-color: #404040 !important;
  }

  /* 强制覆盖highlight.js样式 */
  .hljs {
    background: #1a1a1a !important;
    color: rgba(255, 255, 255, 0.85) !important;
  }

  /* 强制覆盖Prism样式 */
  .token.keyword {
    color: #569cd6 !important;
  }

  .token.string {
    color: #ce9178 !important;
  }

  .token.number {
    color: #b5cea8 !important;
  }

  .token.operator {
    color: #d4d4d4 !important;
  }

  .token.punctuation {
    color: #d4d4d4 !important;
  }

  .token.comment {
    color: #6a9955 !important;
  }

  .token.function {
    color: #dcdcaa !important;
  }
}
