<!--手动录入text-->
<template>
  <BasicModal title="段落详情" destroyOnClose @register="registerModal" :canFullscreen="false" width="600px" :footer="null">
    <div class="p-2">
      <div class="header">
        <a-tag color="#a9c8ff">
          <span>{{hitTextDescData.source}}</span>
        </a-tag>
      </div>
      <div class="content">
        <MarkdownViewer :value="hitTextDescData.content" />
      </div>
    </div>

  </BasicModal>
</template>

<script lang="ts">
  import { ref } from 'vue';
  import BasicModal from '@/components/Modal/src/BasicModal.vue';
  import { useModalInner } from '@/components/Modal';

  import BasicForm from '@/components/Form/src/BasicForm.vue';
  import { MarkdownViewer } from '@/components/Markdown';

  export default {
    name: 'AiTextDescModal',
    components: {
      MarkdownViewer,
      BasicForm,
      BasicModal,
    },
    emits: ['success', 'register'],
    setup(props, { emit }) {
      let hitTextDescData = ref<any>({})
      
      //注册modal
      const [registerModal, { closeModal, setModalProps }] = useModalInner(async (data) => {
        hitTextDescData.value.source = 'score' + ' ' + data.score.toFixed(2);
        hitTextDescData.value.content = data.content;
        setModalProps({ header: '300px' })
      });

      return {
        registerModal,
        hitTextDescData
      };
    },
  };
</script>

<style scoped lang="less">
  .pointer {
    cursor: pointer;
  }
  .header {
    font-size: 16px;
    font-weight: bold;
    margin-top: 10px;
  }
  .content {
    margin-top: 20px;
    max-height: 600px;
    overflow-y: auto;
    overflow-x: auto;
  }
  .title-tag {
    color: #477dee;
  }

  /* 暗黑模式适配 */
  [data-theme='dark'] {
    .p-2 {
      background: #1f1f1f !important;
      color: rgba(255, 255, 255, 0.85) !important;
    }

    .header {
      color: rgba(255, 255, 255, 0.85) !important;

      :deep(.ant-tag) {
        background: rgba(169, 200, 255, 0.2) !important;
        border-color: #58a6ff !important;
        color: #58a6ff !important;
      }
    }

    .content {
      background: #1a1a1a !important;
      border: 1px solid #404040 !important;
      border-radius: 6px !important;
      padding: 16px !important;

      :deep(.markdown-viewer) {
        background: transparent !important;
        color: rgba(255, 255, 255, 0.85) !important;

        h1, h2, h3, h4, h5, h6 {
          color: rgba(255, 255, 255, 0.85) !important;
          border-bottom-color: #404040 !important;
        }

        p {
          color: rgba(255, 255, 255, 0.85) !important;
        }

        a {
          color: #58a6ff !important;

          &:hover {
            color: #79c0ff !important;
          }
        }

        code {
          background-color: #262626 !important;
          color: #f85149 !important;
          border-color: #404040 !important;
        }

        pre {
          background-color: #1a1a1a !important;
          border-color: #404040 !important;

          code {
            background-color: transparent !important;
            color: rgba(255, 255, 255, 0.85) !important;
          }
        }

        blockquote {
          color: rgba(255, 255, 255, 0.65) !important;
          border-left-color: #404040 !important;
        }

        table {
          border-color: #404040 !important;

          th {
            background-color: #262626 !important;
            border-color: #404040 !important;
            color: rgba(255, 255, 255, 0.85) !important;
          }

          td {
            border-color: #404040 !important;
            color: rgba(255, 255, 255, 0.85) !important;
          }

          tr:nth-child(2n) {
            background-color: #1a1a1a !important;
          }
        }

        ul, ol {
          color: rgba(255, 255, 255, 0.85) !important;

          li {
            color: rgba(255, 255, 255, 0.85) !important;
          }
        }

        hr {
          background-color: #404040 !important;
          border-color: #404040 !important;
        }
      }
    }

    .title-tag {
      color: #58a6ff !important;
    }

    :deep(.ant-modal-content) {
      background: #1f1f1f !important;
      color: rgba(255, 255, 255, 0.85) !important;
    }

    :deep(.ant-modal-header) {
      background: #1f1f1f !important;
      border-bottom-color: #404040 !important;

      .ant-modal-title {
        color: rgba(255, 255, 255, 0.85) !important;
      }
    }

    :deep(.ant-modal-close) {
      color: rgba(255, 255, 255, 0.45) !important;

      &:hover {
        color: rgba(255, 255, 255, 0.85) !important;
      }
    }
  }
</style>
