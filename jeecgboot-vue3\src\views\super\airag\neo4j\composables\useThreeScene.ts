import { onBeforeUnmount, computed } from 'vue';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { CSS2DRenderer, CSS2DObject } from 'three/examples/jsm/renderers/CSS2DRenderer.js';
import { useRootSetting } from '/@/hooks/setting/useRootSetting';
import { ThemeEnum } from '/@/enums/appEnum';

/**
 * Three.js场景管理 composable
 * 负责场景的创建、初始化、动画和清理
 */
export function useThreeScene() {
  // ================== 主题检测 ==================
  const { getDarkMode } = useRootSetting();
  const isDarkMode = computed(() => getDarkMode.value === ThemeEnum.DARK);

  // ================== Three.js 核心对象 ==================
  let scene: THREE.Scene | null = null;
  let camera: THREE.PerspectiveCamera | null = null;
  let renderer: THREE.WebGLRenderer | null = null;
  let labelRenderer: CSS2DRenderer | null = null;
  let controls: OrbitControls | null = null;
  let raycaster: THREE.Raycaster | null = null;
  let mouse: THREE.Vector2 | null = null;

  // ================== 动画相关变量 ==================
  let animationId: number | null = null;
  let lastRenderTime = 0;
  let lastBackgroundTime = 0;
  let lastControlsUpdate = 0;
  const TARGET_FPS = 60;
  const FRAME_INTERVAL = 1000 / TARGET_FPS;
  const BACKGROUND_INTERVAL = 1000 / 60;
  const CONTROLS_INTERVAL = 1000 / 60;

  // ================== 背景动画对象缓存 ==================
  let gridHelper: THREE.GridHelper | null = null;
  let particleMesh: THREE.Points | null = null;
  let backgroundInitialized = false;

  // ================== 材质缓存管理 ==================
  const materialCache = new Map<string, THREE.MeshStandardMaterial>();

  /**
   * 获取或创建材质（高效缓存避免频繁创建）
   */
  const getOrCreateMaterial = (key: string, color: string | number, isHighlight: boolean = false): THREE.MeshStandardMaterial => {
    if (materialCache.has(key)) {
      return materialCache.get(key)!;
    }
    
    // 预计算颜色值，避免重复计算
    const colorObj = new THREE.Color(color);
    const emissiveColor = isHighlight ? 0x555500 : colorObj.clone().multiplyScalar(0.15);
    
    const material = new THREE.MeshStandardMaterial({
      color: colorObj,
      roughness: isHighlight ? 0.1 : 0.3,
      metalness: isHighlight ? 0.9 : 0.8,
      emissive: emissiveColor
    });
    
    materialCache.set(key, material);
    return material;
  };

  /**
   * 初始化Three.js场景
   */
  const initThreeJS = (
    container: HTMLElement, 
    onMouseMove?: (event: MouseEvent) => void, 
    onMouseClick?: (event: MouseEvent) => void, 
    onMouseDoubleClick?: (event: MouseEvent) => void
  ): boolean => {
    if (!container) return false;

    // 清空容器
    container.innerHTML = '';

    // 获取容器尺寸
    const width = container.clientWidth;
    const height = container.clientHeight;
    
    // 确保容器尺寸正常，防止异常值导致渲染问题
    if (width <= 0 || height <= 0) {
      console.warn('容器尺寸异常，无法初始化Three.js场景');
      return false;
    }
    
    // 设置明确的尺寸限制，防止超出父容器
    container.style.width = "100%";
    container.style.height = "100%";
    container.style.overflow = "hidden";
    container.style.position = "absolute";
    container.style.top = "0";
    container.style.left = "0";

    // 创建场景 - 使用简洁渐变背景
    scene = new THREE.Scene();

    // 创建主题适配的渐变背景
    const canvasBackground = document.createElement('canvas');
    canvasBackground.width = 512;
    canvasBackground.height = 512;

    const contextBackground = canvasBackground.getContext('2d');
    if (contextBackground) {
      // 根据主题创建不同的径向渐变效果
      const gradient = contextBackground.createRadialGradient(256, 256, 0, 256, 256, 256);

      if (isDarkMode.value) {
        // 暗黑模式：优雅的深蓝灰色渐变
        gradient.addColorStop(0, '#1e1e2e');    // 中心深蓝灰色
        gradient.addColorStop(0.5, '#181825');  // 中间更深的蓝灰色
        gradient.addColorStop(1, '#11111b');    // 边缘最深的蓝黑色
      } else {
        // 亮色模式：从中心的白色到边缘的淡蓝色
        gradient.addColorStop(0, '#ffffff');    // 中心白色
        gradient.addColorStop(0.7, '#f0f7ff');  // 淡蓝色
        gradient.addColorStop(1, '#e6f3ff');    // 边缘更淡的蓝色
      }

      contextBackground.fillStyle = gradient;
      contextBackground.fillRect(0, 0, 512, 512);

      const backgroundTexture = new THREE.CanvasTexture(canvasBackground);
      scene.background = backgroundTexture;
    } else {
      // 回退背景颜色
      scene.background = new THREE.Color(isDarkMode.value ? 0x181825 : 0xf0f7ff);
    }

    // 创建相机 - 使用较小的FOV减少透视变形
    camera = new THREE.PerspectiveCamera(45, width / height, 0.1, 2000);
    camera.position.z = 600;

    // 创建高品质渲染器
    renderer = new THREE.WebGLRenderer({
      antialias: true,               // 抗锯齿
      alpha: true,                   // 透明背景
      powerPreference: 'high-performance' // 优先使用高性能GPU
    });

    renderer.setSize(width, height);
    renderer.setPixelRatio(window.devicePixelRatio);

    // 添加轻微雾效，不影响前景对象的清晰度
    scene.fog = new THREE.FogExp2(isDarkMode.value ? 0x181825 : 0xf0f7ff, 0.0002);

    container.appendChild(renderer.domElement);

    // 创建标签渲染器
    labelRenderer = new CSS2DRenderer();
    labelRenderer.setSize(width, height);
    labelRenderer.domElement.style.position = 'absolute';
    labelRenderer.domElement.style.top = '0px';
    labelRenderer.domElement.style.pointerEvents = 'none';
    container.appendChild(labelRenderer.domElement);

    // 创建轨道控制器
    controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.25;

    // 创建增强型光照系统
    setupLightingSystem();

    // 创建射线检测器
    raycaster = new THREE.Raycaster();
    mouse = new THREE.Vector2();

    // 添加背景效果
    addBackgroundEffects();
    
    // 添加鼠标事件监听
    if (onMouseMove) {
      renderer.domElement.addEventListener('mousemove', onMouseMove, false);
    }
    if (onMouseClick) {
      renderer.domElement.addEventListener('click', onMouseClick, false);
    }
    if (onMouseDoubleClick) {
      renderer.domElement.addEventListener('dblclick', onMouseDoubleClick, false);
    }

    // 启动动画循环
    animate();
    
    return true;
  };

  /**
   * 设置光照系统
   */
  const setupLightingSystem = () => {
    if (!scene) return;

    // 环境光 - 提供基础照明
    const ambientLight = new THREE.AmbientLight(
      isDarkMode.value ? 0x6c7086 : 0x404080,  // 暗黑模式使用蓝灰色调
      isDarkMode.value ? 0.6 : 0.2             // 暗黑模式增强环境光
    );
    scene.add(ambientLight);

    // 半球光 - 模拟天空和地面的反射光，增强场景深度
    const hemisphereLight = new THREE.HemisphereLight(
      isDarkMode.value ? 0x89b4fa : 0xaaccff,  // 暗黑模式使用柔和蓝色
      isDarkMode.value ? 0x313244 : 0x102030,  // 暗黑模式使用深蓝灰色
      isDarkMode.value ? 1.0 : 0.6             // 暗黑模式增强半球光
    );
    scene.add(hemisphereLight);

    // 定向光 - 模拟太阳光，提供主要光源和阴影
    const directionalLight = new THREE.DirectionalLight(
      isDarkMode.value ? 0xf5f5f5 : 0xffffff,  // 暗黑模式使用柔和白光
      isDarkMode.value ? 1.2 : 1.0             // 暗黑模式增强主光源
    );
    directionalLight.position.set(200, 200, 200);
    scene.add(directionalLight);

    // 增加一个从反方向照射的柔光，减少阴影过重
    const backLight = new THREE.DirectionalLight(
      isDarkMode.value ? 0x89b4fa : 0x99ccff,  // 暗黑模式使用蓝色调
      isDarkMode.value ? 0.6 : 0.3             // 暗黑模式增强背光
    );
    backLight.position.set(-100, 50, -100);
    scene.add(backLight);
  };

  /**
   * 添加简洁的背景效果，仅保留清晰的网格线
   */
  const addBackgroundEffects = () => {
    if (!scene) return;
    
    // 使用主题适配的网格线颜色，暗黑模式下增强对比度
    gridHelper = new THREE.GridHelper(
      800,
      16,
      isDarkMode.value ? 0x6c7086 : 0x333333,  // 主网格线：暗黑模式用更亮的灰蓝色
      isDarkMode.value ? 0x45475a : 0x888888   // 次网格线：暗黑模式用中等灰色
    );
    gridHelper.position.y = -100;
    gridHelper.material.transparent = true;
    gridHelper.material.opacity = isDarkMode.value ? 0.8 : 0.5;  // 暗黑模式下增加不透明度
    scene.add(gridHelper);
    
    backgroundInitialized = true;
  };

  /**
   * 简洁的背景动画效果
   */
  const animateBackground = () => {
    if (!scene) return;
    
    // 缓慢旋转网格，营造轻微的动感
    if (gridHelper) {
      gridHelper.rotation.y += 0.001;
    }
  };

  /**
   * 动画循环（高效性能优化）
   */
  const animate = (currentTime: number = 0) => {
    if (!renderer || !scene || !camera || !controls || !labelRenderer) return;

    animationId = requestAnimationFrame(animate);
    
    // 帧率控制
    if (currentTime - lastRenderTime < FRAME_INTERVAL) {
      return;
    }
    
    // 使用微任务分割渲染工作，避免阻塞主线程
    Promise.resolve().then(() => {
      if (!renderer || !scene || !camera || !controls || !labelRenderer) return;
      
      // 控制器更新频率控制
      let needsUpdate = false;
      if (controls.enableDamping && currentTime - lastControlsUpdate >= CONTROLS_INTERVAL) {
        needsUpdate = controls.update();
        lastControlsUpdate = currentTime;
      }
      
      // 背景动画频率控制
      let backgroundUpdated = false;
      if (currentTime - lastBackgroundTime >= BACKGROUND_INTERVAL) {
        animateBackground();
        lastBackgroundTime = currentTime;
        backgroundUpdated = true;
      }

      // 只在需要时渲染
      if (needsUpdate || backgroundUpdated) {
        requestAnimationFrame(() => {
          if (renderer && scene && camera && labelRenderer) {
            renderer.render(scene, camera);
            labelRenderer.render(scene, camera);
          }
        });
        lastRenderTime = currentTime;
      }
    }).catch(() => {
      // 静默处理错误，避免控制台警告
    });
  };

  /**
   * 清理图谱相关的对象（节点和连接线），保留场景基础设置
   */
  const clearGraphObjects = () => {
    if (!scene) return;
    
    // 查找和清理所有图谱相关的对象
    const objectsToRemove: THREE.Object3D[] = [];
    
    scene.traverse((object) => {
      // 清理节点球体、连接线和CSS2D标签
      if (object.userData && (object.userData.id || object.userData.source)) {
        objectsToRemove.push(object);
      }
      // 清理CSS2D对象
      if (object.type === 'CSS2DObject' || object instanceof CSS2DObject) {
        objectsToRemove.push(object);
      }
    });
    
    // 移除所有找到的对象
    objectsToRemove.forEach(obj => {
      // 清理几何体（材质由缓存管理）
      if (obj instanceof THREE.Mesh) {
        if (obj.geometry) {
          obj.geometry.dispose();
        }
      }
      
      // 从场景中移除
      if (obj.parent) {
        obj.parent.remove(obj);
      } else {
        scene!.remove(obj);
      }
    });
    
    // 清理材质缓存（但保留常用的高亮材质）
    const keysToDelete: string[] = [];
    materialCache.forEach((_, key) => {
      if (key !== 'highlight') {
        keysToDelete.push(key);
      }
    });
    keysToDelete.forEach(key => {
      const material = materialCache.get(key);
      if (material) {
        material.dispose();
        materialCache.delete(key);
      }
    });
  };

  /**
   * 清理所有CSS2D对象
   */
  const cleanupCSS2DObjects = () => {
    if (!scene) return;
    
    const css2dObjects: THREE.Object3D[] = [];
    
    scene.traverse((object) => {
      if (object.type === 'CSS2DObject' || object instanceof CSS2DObject) {
        css2dObjects.push(object);
      }
    });
    
    css2dObjects.forEach(obj => {
      if (obj.parent) {
        obj.parent.remove(obj);
      }
    });
  };

  /**
   * 调整渲染器尺寸（优化防闪烁）
   */
  const resizeRenderer = (width: number, height: number) => {
    if (!camera || !renderer || !labelRenderer) return;
    
    // 防止无效尺寸
    if (width <= 0 || height <= 0) return;
    
    // 获取当前渲染器尺寸
    const currentSize = renderer.getSize(new THREE.Vector2());
    
    // 只在尺寸真正发生变化时才调整（避免无效调用）
    if (Math.abs(currentSize.width - width) < 2 && Math.abs(currentSize.height - height) < 2) {
      return;
    }
    
    try {
      // 暂时禁用自动清除，防止闪烁
      const originalAutoClear = renderer.autoClear;
      renderer.autoClear = false;
      
      // 更新相机宽高比
      camera.aspect = width / height;
      camera.updateProjectionMatrix();

      // 设置渲染器尺寸（不强制更新样式，避免闪烁）
      renderer.setSize(width, height, false);
      labelRenderer.setSize(width, height);
      
      // 手动设置canvas样式，确保尺寸正确
      if (renderer.domElement) {
        renderer.domElement.style.width = width + 'px';
        renderer.domElement.style.height = height + 'px';
      }
      
      if (labelRenderer.domElement) {
        labelRenderer.domElement.style.width = width + 'px';
        labelRenderer.domElement.style.height = height + 'px';
      }
      
      // 立即渲染一帧，避免空白
      if (scene && camera) {
        renderer.render(scene, camera);
        labelRenderer.render(scene, camera);
      }
      
      // 恢复自动清除设置
      renderer.autoClear = originalAutoClear;
      
    } catch (error) {
      console.warn('Resize renderer error:', error);
    }
  };

  /**
   * 清理所有Three.js资源
   */
  const dispose = () => {
    // 停止动画循环
    if (animationId) {
      cancelAnimationFrame(animationId);
      animationId = null;
    }

    // 清理Three.js资源
    if (renderer) {
      if (renderer.domElement && renderer.domElement.parentNode) {
        renderer.domElement.parentNode.removeChild(renderer.domElement);
      }
      
      // 主动释放WebGL上下文
      const gl = renderer.getContext();
      if (gl && typeof gl.getExtension === 'function') {
        const extension = gl.getExtension('WEBGL_lose_context');
        if (extension) extension.loseContext();
      }
      
      renderer.dispose();
      renderer = null;
    }
    
    if (labelRenderer && labelRenderer.domElement && labelRenderer.domElement.parentNode) {
      labelRenderer.domElement.parentNode.removeChild(labelRenderer.domElement);
      labelRenderer = null;
    }

    // 清理场景
    if (scene) {
      cleanupCSS2DObjects();
      
      while (scene.children.length > 0) {
        const object = scene.children[0];
        if (object instanceof THREE.Mesh) {
          if (object.geometry) object.geometry.dispose();
          if (object.material) {
            if (Array.isArray(object.material)) {
              object.material.forEach((material) => material.dispose());
            } else {
              object.material.dispose();
            }
          }
        }
        scene.remove(object);
      }
      scene = null;
    }
    
    // 释放控制器
    if (controls) {
      controls.dispose();
      controls = null;
    }
    
    // 清理引用
    camera = null;
    raycaster = null;
    mouse = null;
    gridHelper = null;
    particleMesh = null;
    backgroundInitialized = false;
    
    // 清理材质缓存
    materialCache.forEach((material) => material.dispose());
    materialCache.clear();
  };

  /**
   * 更新场景背景（用于主题切换）
   */
  const updateBackground = () => {
    if (!scene) return;

    // 创建主题适配的渐变背景
    const canvasBackground = document.createElement('canvas');
    canvasBackground.width = 512;
    canvasBackground.height = 512;

    const contextBackground = canvasBackground.getContext('2d');
    if (contextBackground) {
      // 根据主题创建不同的径向渐变效果
      const gradient = contextBackground.createRadialGradient(256, 256, 0, 256, 256, 256);

      if (isDarkMode.value) {
        // 暗黑模式：优雅的深蓝灰色渐变
        gradient.addColorStop(0, '#1e1e2e');    // 中心深蓝灰色
        gradient.addColorStop(0.5, '#181825');  // 中间更深的蓝灰色
        gradient.addColorStop(1, '#11111b');    // 边缘最深的蓝黑色
      } else {
        // 亮色模式：从中心的白色到边缘的淡蓝色
        gradient.addColorStop(0, '#ffffff');    // 中心白色
        gradient.addColorStop(0.7, '#f0f7ff');  // 淡蓝色
        gradient.addColorStop(1, '#e6f3ff');    // 边缘更淡的蓝色
      }

      contextBackground.fillStyle = gradient;
      contextBackground.fillRect(0, 0, 512, 512);

      const backgroundTexture = new THREE.CanvasTexture(canvasBackground);
      scene.background = backgroundTexture;
    } else {
      // 回退背景颜色
      scene.background = new THREE.Color(isDarkMode.value ? 0x181825 : 0xf0f7ff);
    }

    // 更新雾效颜色
    if (scene.fog) {
      (scene.fog as THREE.FogExp2).color.setHex(isDarkMode.value ? 0x181825 : 0xf0f7ff);
    }

    // 更新网格颜色
    if (gridHelper) {
      // 移除旧网格
      scene.remove(gridHelper);
      gridHelper.dispose();

      // 创建新的主题适配网格
      gridHelper = new THREE.GridHelper(
        800,
        16,
        isDarkMode.value ? 0x6c7086 : 0x333333,  // 主网格线：暗黑模式用更亮的灰蓝色
        isDarkMode.value ? 0x45475a : 0x888888   // 次网格线：暗黑模式用中等灰色
      );
      gridHelper.position.y = -100;
      gridHelper.material.transparent = true;
      gridHelper.material.opacity = isDarkMode.value ? 0.8 : 0.5;  // 暗黑模式下增加不透明度
      scene.add(gridHelper);
    }

    // 更新光照系统
    const lights = scene.children.filter(child => child instanceof THREE.Light);
    lights.forEach(light => scene.remove(light));

    // 重新设置光照系统
    setupLightingSystem();
  };

  // 组件卸载时自动清理
  onBeforeUnmount(() => {
    dispose();
  });

  return {
    // 核心对象访问器（只读）
    getScene: () => scene,
    getCamera: () => camera,
    getRenderer: () => renderer,
    getLabelRenderer: () => labelRenderer,
    getControls: () => controls,
    getRaycaster: () => raycaster,
    getMouse: () => mouse,
    
    // 功能方法
    initThreeJS,
    clearGraphObjects,
    cleanupCSS2DObjects,
    resizeRenderer,
    dispose,
    getOrCreateMaterial,
    updateBackground,

    // 状态访问器
    isInitialized: () => scene !== null && renderer !== null,
  };
} 