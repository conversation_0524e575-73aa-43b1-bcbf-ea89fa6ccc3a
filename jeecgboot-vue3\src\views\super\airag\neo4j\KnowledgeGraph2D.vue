<!-- 2D知识图谱组件 -->
<template>
  <div class="knowledge-graph-container">
    <div class="graph-container">
      <div ref="graphRef" class="graph-view"></div>
      <div v-if="loading" class="loading-container">
        <a-spin :tip="t('neo4j.common.loading')" />
      </div>
      <div v-if="!loading && (!graphData.nodes || graphData.nodes.length === 0)" class="empty-container">
        <a-empty :description="t('neo4j.common.noData')" />
      </div>
      <div class="graph-controls" v-if="graphData.nodes && graphData.nodes.length > 0">
        <a-button-group>
          <a-button type="primary" size="small" @click="zoomIn" :title="t('neo4j.controls.zoomIn')">
            <plus-outlined />
          </a-button>
          <a-button type="primary" size="small" @click="zoomOut" :title="t('neo4j.controls.zoomOut')">
            <minus-outlined />
          </a-button>
          <a-button type="primary" size="small" @click="resetView" :title="t('neo4j.controls.resetView')">
            <undo-outlined />
          </a-button>
          <a-button type="primary" size="small" :title="t('neo4j.controls.fullscreen')" @click="toggleFullscreen">
            <fullscreen-outlined v-if="!isFullscreen" />
            <fullscreen-exit-outlined v-else />
          </a-button>
        </a-button-group>
      </div>
      <!-- 全屏模式下的节点详情面板 -->
      <div v-if="isFullscreen && selectedNode" class="node-detail-panel entity-detail-style" :style="detailPanelStyle">
        <div class="node-detail-header entity-detail-header">
          <div class="node-title">
            <div class="node-type-indicator" :style="{ backgroundColor: getEntityTypeColor(selectedNode.type) }"></div>
            <h3>{{ selectedNode.name }}</h3>
          </div>
          <a-button type="text" @click="closeNodeDetail">
            <close-outlined />
          </a-button>
        </div>
        <div class="node-detail-content entity-detail-content">
          <a-descriptions :column="1" size="small">
            <a-descriptions-item :label="t('neo4j.nodeDetail.nodeId')">{{ selectedNode.id }}</a-descriptions-item>
            <a-descriptions-item :label="t('neo4j.nodeDetail.nodeName')">{{ selectedNode.name }}</a-descriptions-item>
            <a-descriptions-item :label="t('neo4j.nodeDetail.nodeType')">
              <div class="type-with-color">
                <span class="type-color-marker" :style="{ backgroundColor: getEntityTypeColor(selectedNode.type) }"></span>
                <span>{{ selectedNode.type }}</span>
              </div>
            </a-descriptions-item>
            <a-descriptions-item v-for="(val, key) in selectedNodeProperties" :key="key" :label="key">
              {{ val }}
            </a-descriptions-item>
          </a-descriptions>
          
          <!-- 出入关系部分 -->
          <div class="node-relations">
            <div v-if="nodeRelations.inbound.length > 0" class="relation-section">
              <div class="relation-title">{{ t('neo4j.nodeDetail.inboundRelations') }}</div>
              <ul class="relation-list">
                <li v-for="(link, index) in nodeRelations.inbound" :key="'in-'+index" class="relation-item">
                  <span class="relation-node" :style="{ backgroundColor: getEntityTypeColor(link.source.type || '') }"></span>
                  <span class="relation-name" @click="handleRelationNodeClick(link.source)">{{ link.source.name || link.source }}</span>
                  <a-tag size="small" color="blue">{{ link.type }}</a-tag>
                  <span class="relation-arrow">→</span>
                  <span class="relation-current">{{ t('neo4j.nodeDetail.currentNode') }}</span>
                </li>
              </ul>
            </div>
            
            <div v-if="nodeRelations.outbound.length > 0" class="relation-section">
              <div class="relation-title">{{ t('neo4j.nodeDetail.outboundRelations') }}</div>
              <ul class="relation-list">
                <li v-for="(link, index) in nodeRelations.outbound" :key="'out-'+index" class="relation-item">
                  <span class="relation-current">{{ t('neo4j.nodeDetail.currentNode') }}</span>
                  <span class="relation-arrow">→</span>
                  <a-tag size="small" color="green">{{ link.type }}</a-tag>
                  <span class="relation-name" @click="handleRelationNodeClick(link.target)">{{ link.target.name || link.target }}</span>
                  <span class="relation-node" :style="{ backgroundColor: getEntityTypeColor(link.target.type || '') }"></span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 2D全屏模式下的统计信息悬浮窗 -->
      <div v-if="isFullscreen && graphData.nodes && graphData.nodes.length > 0" class="statistics-floating-panel">
        <div class="statistics-header">{{ t('neo4j.statistics.title') }}</div>
        <div class="statistics-content">
          <div class="statistic-item">
            <span class="statistic-icon">📊</span>
            <span class="statistic-label">{{ t('neo4j.statistics.nodes') }}:</span>
            <span class="statistic-value">{{ graphData.nodes.length }}</span>
          </div>
          <div class="statistic-item">
            <span class="statistic-icon">🔗</span>
            <span class="statistic-label">{{ t('neo4j.statistics.relationships') }}:</span>
            <span class="statistic-value">{{ graphData.links.length }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  import { defineComponent, onMounted, ref, reactive, watch, nextTick, onBeforeUnmount, computed, PropType } from 'vue';
  import { useKnowledgeGraph } from './composables/useKnowledgeGraph';
  import * as d3 from 'd3';
  import { FullscreenOutlined, FullscreenExitOutlined, CloseOutlined, PlusOutlined, MinusOutlined, UndoOutlined } from '@ant-design/icons-vue';
  import { useRootSetting } from '/@/hooks/setting/useRootSetting';
  import { ThemeEnum } from '/@/enums/appEnum';

  // 导出面板样式类名，便于父组件复用样式
  export const ENTITY_DETAIL_STYLE_CLASS = 'entity-detail-style';

  export default defineComponent({
    name: 'KnowledgeGraph2D',
    components: {
      FullscreenOutlined,
      FullscreenExitOutlined,
      CloseOutlined,
      PlusOutlined,
      MinusOutlined,
      UndoOutlined
    },
    props: {
      queryParam: {
        type: Object as PropType<any>,
        default: () => ({
          entity_name: '',
          entity_type: undefined,
          relation_type: undefined,
          max_nodes: 50,
        }),
      }
    },
    emits: ['show-entity-detail', 'update-statistics', 'fullscreen-change'],
    setup(props, { emit }) {
      // 主题检测
      const { getDarkMode } = useRootSetting();
      const isDarkMode = computed(() => getDarkMode.value === ThemeEnum.DARK);

      // 主题相关的颜色配置
      const themeColors = computed(() => ({
        linkStroke: isDarkMode.value ? '#666' : '#999',
        linkText: isDarkMode.value ? 'rgba(255, 255, 255, 0.65)' : '#666',
        arrowFill: isDarkMode.value ? '#666' : '#666',
        highlightStroke: '#FFFF00', // 高亮颜色保持一致
        nodeText: isDarkMode.value ? 'rgba(255, 255, 255, 0.85)' : '#333'
      }));

      // 使用抽取的 composable 获取通用的数据状态和业务逻辑
      const {
        loading,
        graphData,
        selectedNode,
        clickPosition,
        isFullscreen,
        getEntityTypeColor,
        loadGraphData,
        getNodeById,
        getNodeRelations,
        createDetailPanelStyle,
        selectedNodeProperties,
        nodeRelations,
        closeNodeDetail,
        setNodeDetail,
        createResizeHandler,
        createTimerManager,
        createMessage,
        t
      } = useKnowledgeGraph();
      
      const graphRef = ref<HTMLElement | null>(null);
      
      // 添加simulation引用，确保能够正确停止旧的simulation
      const currentSimulation = ref<any>(null);
      
      // 使用composable中的定时器管理器
      const timerManager = createTimerManager();
      
      // 使用composable中的详情面板功能
      const detailPanelStyle = createDetailPanelStyle(graphRef);

      // 处理关系节点点击事件
      const handleRelationNodeClick = (nodeData: any) => {
        // 获取节点ID
        const nodeId = typeof nodeData === 'string' ? nodeData : nodeData.id;
        if (!nodeId) return;
        
        // 调用selectNodeById进行跳转和高亮
        selectNodeById(nodeId);
      };

      // 防抖版本的数据加载函数
      const debouncedLoadGraphData = () => {
        // 使用定时器管理器设置防抖计时器，300ms延迟
        timerManager.setTimer('debounce', () => {
          load2DGraphData();
        }, 300);
      };

      // 包装 loadGraphData 以支持2D特定的渲染逻辑
      const load2DGraphData = async () => {
        // 在数据加载期间停止当前simulation，避免无效计算
        if (currentSimulation.value) {
          currentSimulation.value.stop();
          currentSimulation.value = null;
        }
        
        const result = await loadGraphData(props.queryParam);
        if (result) {
          nextTick(() => {
            renderGraph();
            
            // 向父组件发送统计信息更新事件
            emit('update-statistics', {
              nodesCount: graphData.value.nodes?.length || 0,
              linksCount: graphData.value.links?.length || 0
            });
          });
        }
        return result;
      };

      // 渲染图谱
      const renderGraph = () => {
        if (!graphRef.value) return;
        if (!graphData.value.nodes || !graphData.value.links) return;
        
        // 停止并清理旧的simulation，这是关键的性能优化点
        if (currentSimulation.value) {
          currentSimulation.value.stop();
          currentSimulation.value = null;
        }
        
        // 清空容器
        d3.select(graphRef.value).selectAll('*').remove();
        
        // 重置缩放状态
        currentZoomTransform = d3.zoomIdentity;
        
        const width = graphRef.value.clientWidth;
        const height = graphRef.value.clientHeight;
        
        // 创建SVG
        const svg = d3
          .select(graphRef.value)
          .append('svg')
          .attr('width', width)
          .attr('height', height)
          .attr('viewBox', [0, 0, width, height])
          .attr('style', 'max-width: 100%; height: auto;');
          
        // 创建一个包含所有元素的g元素，用于整体缩放和平移
        const g = svg.append('g');
        
        // 定义节点圆形基础半径
        const circleRadius = 30; // 基础半径
        
        // 计算节点实际占用空间（包括文本）
        const getNodeCollisionRadius = (transform: any = null) => {
          const scale = transform ? transform.k : 1;
          const nodeScaleFactor = 1 + Math.log10(scale + 0.1) * 0.5;
          const actualNodeRadius = circleRadius * Math.min(nodeScaleFactor, 1.5);
          // 减少文本占用的额外空间，让布局更紧凑
          const textSpaceBuffer = 15; // 从25减少到15
          return actualNodeRadius + textSpaceBuffer;
        };
        
        // 添加箭头标记
        svg
          .append('defs')
          .append('marker')
          .attr('id', 'arrowhead')
          .attr('viewBox', '0 -5 10 10')
          .attr('refX', circleRadius + 5) // 根据节点半径动态调整箭头位置，避免被节点覆盖
          .attr('refY', 0)
          .attr('orient', 'auto')
          .attr('markerWidth', 8) // 增大箭头尺寸
          .attr('markerHeight', 8)
          .attr('xoverflow', 'visible')
          .append('path')
          .attr('d', 'M 0,-5 L 10,0 L 0,5')
          .attr('fill', themeColors.value.arrowFill) // 主题适配的箭头颜色
          .style('stroke', 'none');
        
        // 创建模拟力导向图，并存储引用
        const simulation = d3
          .forceSimulation(graphData.value.nodes)
          .force(
            'link',
            d3
              .forceLink(graphData.value.links)
              .id((d: any) => d.id)
              .distance(160) // 从180减少到160，让连接更紧凑
          )
          .force('charge', d3.forceManyBody().strength(-450)) // 从-600减少到-450，平衡排斥力
          .force('center', d3.forceCenter(width / 2, height / 2).strength(0.08)) // 增加中心力
          .force('collision', d3.forceCollide().radius(getNodeCollisionRadius()).strength(1.0)) // 增强碰撞检测强度
          .force('x', d3.forceX(width / 2).strength(0.05)) // 适度增加X方向聚集力
          .force('y', d3.forceY(height / 2).strength(0.05)) // 适度增加Y方向聚集力
          .alpha(0.4) // 从0.5减少到0.4
          .alphaDecay(0.03) // 从0.02增加到0.03，让动画稍快稳定
          .velocityDecay(0.4); // 从0.3增加到0.4，增加阻尼
        
        // 存储当前simulation的引用
        currentSimulation.value = simulation;
        
        // 绘制连接线
        const link = g
          .append('g')
          .attr('class', 'links')
          .selectAll('line')
          .data(graphData.value.links)
          .join('line')
          .attr('stroke', themeColors.value.linkStroke)
          .attr('stroke-opacity', 0.6)
          .attr('stroke-width', 1.5)
          .attr('marker-end', 'url(#arrowhead)');
        
        // 添加关系文本
        const linkText = g
          .append('g')
          .attr('class', 'link-labels')
          .selectAll('text')
          .data(graphData.value.links)
          .join('text')
          .text((d: any) => d.type)
          .attr('font-size', 10)
          .attr('text-anchor', 'middle')
          .attr('dy', -5)
          .attr('fill', themeColors.value.linkText);
        
        // 创建节点组
        const nodeGroup = g
          .append('g')
          .attr('class', 'nodes')
          .selectAll('g')
          .data(graphData.value.nodes)
          .join('g')
          .on('mouseover', (event, d: any) => {
            // 鼠标悬停时仅高亮节点，不显示详情
            d3.select(event.currentTarget)
              .select('circle')
              .attr('stroke', themeColors.value.highlightStroke)
              .attr('stroke-width', 3);
          })
          .on('mouseout', (event) => {
            // 鼠标移出时取消高亮，除非该节点已被点击选中
            const isSelected = d3.select(event.currentTarget).classed('selected');
            if (!isSelected) {
              d3.select(event.currentTarget)
                .select('circle')
                .attr('stroke', 'none')
                .attr('stroke-width', 0);
            }
          })
          .on('click', (event, d: any) => {
            // 清除之前的点击延时器
            timerManager.clearTimer('click');

            // 延时处理单击事件，如果在延时期间发生双击，则不处理单击
            timerManager.setTimer('click', () => {
              // 移除所有节点的高亮和选中状态
              nodeGroup.classed('selected', false)
                .selectAll('circle')
                .attr('stroke', 'none')
                .attr('stroke-width', 0);
              
              // 给当前节点添加高亮和选中状态
              d3.select(event.currentTarget)
                .classed('selected', true)
                .select('circle')
                .attr('stroke', themeColors.value.highlightStroke)
                .attr('stroke-width', 5);
              
              // 记录点击位置
              clickPosition.value = { x: event.pageX, y: event.pageY };
              
              // 显示节点详情，向父组件发送事件
              emit('show-entity-detail', d.id, event);
              
              // 全屏模式下，显示详情面板
              if (isFullscreen.value) {
                setNodeDetail(d.id);
              }
              
            }, 200); // 延时200ms处理单击事件
            
            event.stopPropagation();
          });
          
        // 添加节点圆形和文本
        nodeGroup
          .append('circle')
          .attr('r', circleRadius)
          .attr('fill', (d: any) => getEntityTypeColor(d.type))
          .on('dblclick', function(event, d: any) {
            // 清除单击延时器，防止触发单击事件
            timerManager.clearTimer('click');

            // 直接在圆形元素上处理双击事件
            event.stopPropagation();
            event.preventDefault();
            
            // 获取当前缩放比例
            const svgElement = svg.node();
            if (svgElement) {
              let currentTransform = d3.zoomTransform(svgElement);
              console.log('圆形上双击节点:', d.name, '原始位置:', d.x, d.y, '当前变换:', currentTransform);
            } else {
              console.log('圆形上双击节点:', d.name, '原始位置:', d.x, d.y);
            }
            
            // 使用改进的方法将节点放在视图中央
            svg.transition()
              .duration(750)
              .call(
                zoom.transform,
                d3.zoomIdentity
                  .translate(width/2, height/2)
                  .scale(2.2)
                  .translate(-d.x, -d.y)
              )
              .on('end', () => {
                console.log('节点缩放动画完成');
              });
          })
          .call(
            d3.drag<SVGCircleElement, any>()
              .filter(event => {
                // 防止双击事件被拖拽处理器捕获
                return !event.button && event.type !== 'dblclick';
              })
              .on('start', dragstarted)
              .on('drag', dragged)
              .on('end', dragended)
          );
        
        // 添加节点文本
        nodeGroup
          .append('text')
          .attr('text-anchor', 'middle') // 文本居中
          .attr('dominant-baseline', 'middle') // 垂直居中
          .attr('font-size', 10) // 基础字体大小
          .attr('fill', 'black') // 文本颜色设为黑色
          .attr('font-weight', 'bold') // 文本加粗
          .attr('pointer-events', 'none') // 防止文本干扰鼠标事件
          .each(function(d: any) {
            // 初始化时不设置文本，留给updateNodeTextVisibility处理
            d3.select(this).text("");
          });

        // 更新节点显示以适应缩放
        const updateNodeTextVisibility = (transform: any) => {
          const scale = transform ? transform.k : 1; // 如果没有提供transform，使用默认缩放级别1
          
          // 更缓慢的节点放大比率，防止节点重叠
          // 使用对数函数：在低缩放时接近线性，高缩放时增长缓慢
          const nodeScaleFactor = 1 + Math.log10(scale + 0.1) * 0.5;
          const nodeRadius = circleRadius * Math.min(nodeScaleFactor, 1.5);
          
          // 更新节点大小
          nodeGroup.selectAll('circle')
            .attr('r', nodeRadius); // 限制最大放大倍数为1.5倍
          
          // 同时更新箭头的位置，避免被放大后的节点覆盖
          svg.select('#arrowhead')
            .attr('refX', nodeRadius + 5);
            
          // 动态更新碰撞检测半径，确保缩放时节点不会重叠
          if (currentSimulation.value) {
            // 清理所有节点的固定位置，让它们都能参与新的碰撞检测
            graphData.value.nodes.forEach((node: any) => {
              node.fx = null;
              node.fy = null;
            });
            
            currentSimulation.value.force('collision', d3.forceCollide()
              .radius(getNodeCollisionRadius(transform))
              .strength(1.0)
            );
            // 重启模拟以应用新的碰撞半径
            currentSimulation.value.alpha(0.3).restart();
          }
            
          // 更新文本内容和大小
          nodeGroup.selectAll('text')
            .each(function(d: any) {
              const name = d.name || "";
              const textEl = d3.select(this);
              textEl.selectAll('*').remove(); // 清除旧的tspan元素
              
              // 根据缩放和节点大小确定每行最大字符数
              const effectiveRadius = nodeRadius * 0.7; // 有效文本半径
              // 根据圆形面积估算可容纳字符数，默认情况考虑中文字符宽度约为10px
              const charWidth = 10; 
              const maxCharsPerLine = Math.max(4, Math.floor((effectiveRadius * 2) / charWidth));
              
              // 根据缩放级别决定行数，在最大缩放时不限制行数
              // 当缩放比例接近最大值(2.5)时，显示所有行
              const isMaxZoom = scale > 2.0;
              let linesToShow = scale < 0.8 ? 1 : (scale >= 1.2 ? (isMaxZoom ? 999 : 2) : 2);
              
              if (name.length <= maxCharsPerLine) {
                // 短文本，单行显示
                textEl.text(name);
              } else {
                // 长文本，需要换行处理
                textEl.text(null); // 清空文本
                
                // 直接按最大字符数分行
                const lines: string[] = [];
                
                // 非常小的缩放比例，只显示前几个字符
                if (scale < 0.6) {
                  textEl.text(name.substring(0, 3) + '...');
                  return;
                }
                
                // 分行处理
                for (let i = 0; i < name.length; i += maxCharsPerLine) {
                  const line = name.substring(i, i + maxCharsPerLine);
                  
                  // 在非最大缩放状态下，如果已经达到了行数限制并且还有更多内容
                  if (!isMaxZoom && lines.length === linesToShow - 1 && i + maxCharsPerLine < name.length) {
                    lines.push(line + '...');
                    break;
                  } else {
                    lines.push(line);
                    // 如果已经达到行数限制且不是最大缩放，则退出循环
                    if (!isMaxZoom && lines.length >= linesToShow) {
                      break;
                    }
                  }
                }
                
                // 添加每行文本
                lines.forEach((line, i) => {
                  textEl.append('tspan')
                    .attr('x', 0)
                    .attr('y', 0)
                    .attr('dy', (i - (lines.length - 1) / 2) * 1.2 + 'em')
                    .text(line);
                });
              }
              
              // 字体大小随缩放变化，但在最大缩放时使用更大的字体
              const maxFontSize = isMaxZoom ? 12 : 10;
              textEl.attr('font-size', Math.min(maxFontSize, 10 * Math.sqrt(Math.min(scale, 1.5))));
            });
        };
        
        // 在初始化时设置节点文本
        updateNodeTextVisibility(null);
        
        // 更新力导向图
        simulation.on('tick', () => {
          link
            .attr('x1', (d: any) => d.source.x)
            .attr('y1', (d: any) => d.source.y)
            .attr('x2', (d: any) => d.target.x)
            .attr('y2', (d: any) => d.target.y);
          
          linkText
            .attr('x', (d: any) => (d.source.x + d.target.x) / 2)
            .attr('y', (d: any) => (d.source.y + d.target.y) / 2);
          
          nodeGroup.attr('transform', (d: any) => `translate(${d.x},${d.y})`);
        });
        
        // 拖拽函数
        function dragstarted(event: any, d: any) {
          if (!event.active) simulation.alphaTarget(0.3).restart();
          d.fx = d.x;
          d.fy = d.y;
        }
        
        function dragged(event: any, d: any) {
          // 获取当前SVG元素和变换
          const svgNode = svg.node();
          if (!svgNode) return;
          
          // 获取当前变换并计算正确的坐标
          const transform = d3.zoomTransform(svgNode);
          // 将屏幕坐标转换为SVG内部坐标
          const point = d3.pointer(event, svgNode);
          const x = (point[0] - transform.x) / transform.k;
          const y = (point[1] - transform.y) / transform.k;
          // 设置节点位置
          d.fx = x;
          d.fy = y;
          d.x = x;
          d.y = y;
        }
        
        function dragended(event: any, d: any) {
          if (!event.active) simulation.alphaTarget(0);
          
          // 简化拖拽结束处理：
          // 1. 短暂固定位置防止立即跳动
          // 2. 几秒后释放固定，让所有节点都能参与碰撞检测
          
          // 暂时固定位置
          d.fx = d.x;
          d.fy = d.y;
          
          // 3秒后释放固定位置，确保所有节点都能参与碰撞检测
          setTimeout(() => {
            d.fx = null;
            d.fy = null;
            // 轻微重启模拟，让节点能够自然调整位置
            if (simulation.alpha() < 0.1) {
              simulation.alpha(0.1).restart();
            }
          }, 1000);
        }
        
        // 添加缩放功能
        const zoom = d3.zoom<SVGSVGElement, unknown>()
          .scaleExtent([0.1, 2.5]) // 限制最大缩放倍数为2.5倍
          .on('zoom', (event) => {
            // 同步更新我们的缩放状态
            currentZoomTransform = event.transform;
            g.attr('transform', event.transform.toString());
            // 在缩放时更新节点文本显示
            updateNodeTextVisibility(event.transform);
          });
          
        // 存储zoom行为的引用，供按钮缩放使用
        zoomBehavior = zoom;
        svg.call(zoom);
        
        // 双击节点时聚焦该节点
        nodeGroup.on('dblclick', function(event, d: any) {
          // 清除单击延时器，防止触发单击事件
          timerManager.clearTimer('click');

          // 阻止事件冒泡和默认行为
          event.stopPropagation();
          event.preventDefault();
          
          // 仅防止事件传播，实际操作在圆形元素上进行
          console.log('节点组双击触发，但不执行缩放');
        });
        
        // 双击背景时重置缩放
        svg.on('dblclick', () => {
          // 清除单击延时器，防止触发单击事件
          timerManager.clearTimer('click');

          // 重置我们的缩放状态
          currentZoomTransform = d3.zoomIdentity;
          svg.transition().duration(750).call(
            zoom.transform,
            d3.zoomIdentity
          );
        });

        // 点击空白处通知父组件
        svg.on('click', () => {
          // 清除之前的点击延时器
          timerManager.clearTimer('click');

          // 延时处理点击空白处事件
          timerManager.setTimer('click', () => {
            // 取消节点高亮
            nodeGroup.selectAll('circle')
              .attr('stroke', 'none')
              .attr('stroke-width', 0);
              
            // 通知父组件关闭悬浮窗
            emit('show-entity-detail', null, null);
            
            // 清除选中节点
            selectedNode.value = null;
            
          }, 200); // 延时200ms处理点击事件
        });
      };

      // 使用composable中的窗口大小变化处理器
      const resizeHandler = createResizeHandler(() => {
        if (graphData.value.nodes && graphData.value.nodes.length > 0) {
          renderGraph();
        }
      });
      
      const { addResizeListener, removeResizeListener, handleResize } = resizeHandler;

      // 进入/退出全屏
      const toggleFullscreen = () => {
        const container = graphRef.value?.closest('.graph-container') as HTMLElement;
        if (!container) return;
        
        if (!isFullscreen.value) {
          // 进入全屏
          if (container.requestFullscreen) {
            container.requestFullscreen();
          } else if ((container as any).webkitRequestFullscreen) {
            (container as any).webkitRequestFullscreen();
          } else if ((container as any).msRequestFullscreen) {
            (container as any).msRequestFullscreen();
          }
        } else {
          // 退出全屏
          if (document.exitFullscreen) {
            document.exitFullscreen();
          } else if ((document as any).webkitExitFullscreen) {
            (document as any).webkitExitFullscreen();
          } else if ((document as any).msExitFullscreen) {
            (document as any).msExitFullscreen();
          }
          
          // 退出全屏时清除选中节点
          selectedNode.value = null;
          // 通知父组件关闭悬浮窗
          emit('show-entity-detail', null, null);
        }
      };

      // 监听全屏状态变化
      const handleFullscreenChange = () => {
        isFullscreen.value = !!(document.fullscreenElement || 
                                (document as any).webkitFullscreenElement || 
                                (document as any).msFullscreenElement);
        
        // 通知父组件2D组件的全屏状态变化
        emit('fullscreen-change', isFullscreen.value);
        
        // 如果退出了全屏，清除选中的节点并关闭外部悬浮窗
        if (!isFullscreen.value) {
          selectedNode.value = null;
          // 通知父组件关闭悬浮窗
          emit('show-entity-detail', null, null);
        }
        
        // 全屏状态变化后，需要延迟一点重新渲染图表，确保尺寸正确
        setTimeout(() => {
          if (graphData.value.nodes && graphData.value.nodes.length > 0) {
            handleResize();
          }
        }, 100);
      };

      // 组件挂载后加载数据
      onMounted(() => {
        load2DGraphData();
        
        // 添加事件监听器
        addResizeListener();
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('msfullscreenchange', handleFullscreenChange);
        
        // 定期清理节点固定位置，确保所有节点都能参与碰撞检测
        const cleanupInterval = setInterval(() => {
          if (graphData.value.nodes && currentSimulation.value) {
            let hasFixedNodes = false;
            graphData.value.nodes.forEach((node: any) => {
              if (node.fx !== null && node.fx !== undefined) {
                hasFixedNodes = true;
                node.fx = null;
                node.fy = null;
              }
            });
            
            // 如果有固定节点被清理，轻微重启模拟
            if (hasFixedNodes && currentSimulation.value.alpha() < 0.1) {
              currentSimulation.value.alpha(0.1).restart();
            }
          }
        }, 10000); // 每10秒清理一次
        
        // 存储定时器ID以便清理
        timerManager.setTimer('cleanup', () => {}, 0);
        (timerManager as any).cleanupInterval = cleanupInterval;
      });
      
      // 当查询参数变化时重新加载数据，使用防抖
      watch(() => props.queryParam, () => {
        debouncedLoadGraphData();
      }, { deep: true });

      // 组件卸载前移除事件监听
      onBeforeUnmount(() => {
        // 移除事件监听器
        removeResizeListener();
        
        // 清理所有定时器
        timerManager.clearAllTimers();
        
        // 清理定期清理定时器
        if ((timerManager as any).cleanupInterval) {
          clearInterval((timerManager as any).cleanupInterval);
        }
        
        // 停止并清理simulation
        if (currentSimulation.value) {
          currentSimulation.value.stop();
          currentSimulation.value = null;
        }
        
        // 移除全屏变化监听
        document.removeEventListener('fullscreenchange', handleFullscreenChange);
        document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.removeEventListener('msfullscreenchange', handleFullscreenChange);
      });

      // 提供一个方法来供父组件调用，触发数据重新加载
      const refresh = () => {
        load2DGraphData();
      };

      // 存储当前的缩放状态和zoom行为
      let currentZoomTransform = d3.zoomIdentity;
      let zoomBehavior: any = null;

      // 放大视图
      const zoomIn = () => {
        if (!graphRef.value || !zoomBehavior) return;
        
        const svg = d3.select(graphRef.value).select('svg');
        if (svg.empty()) return;
        
        // 计算新的缩放比例
        const newScale = Math.min(currentZoomTransform.k * 1.2, 2.5); // 限制最大缩放
        const newTransform = currentZoomTransform.scale(newScale / currentZoomTransform.k);
        
        // 使用存储的zoom行为来应用变换
        svg.transition()
          .duration(300)
          .call(zoomBehavior.transform, newTransform);
      };

      // 缩小视图
      const zoomOut = () => {
        if (!graphRef.value || !zoomBehavior) return;
        
        const svg = d3.select(graphRef.value).select('svg');
        if (svg.empty()) return;
        
        // 计算新的缩放比例
        const newScale = Math.max(currentZoomTransform.k * 0.8, 0.1); // 限制最小缩放
        const newTransform = currentZoomTransform.scale(newScale / currentZoomTransform.k);
        
        // 使用存储的zoom行为来应用变换
        svg.transition()
          .duration(300)
          .call(zoomBehavior.transform, newTransform);
      };

      // 重置视图
      const resetView = () => {
        if (!graphRef.value || !zoomBehavior) return;
        
        const svg = d3.select(graphRef.value).select('svg');
        if (svg.empty()) return;
        
        // 使用存储的zoom行为来重置视图
        svg.transition()
          .duration(750)
          .call(zoomBehavior.transform, d3.zoomIdentity);
      };

      // 通过ID选择并跳转到指定节点
      const selectNodeById = (nodeId: string) => {
        if (!graphRef.value || !graphData.value.nodes) return;
        
        // 查找目标节点
        const targetNode = graphData.value.nodes.find((node: any) => node.id === nodeId);
        if (!targetNode) return;
        
        // 获取SVG元素
        const svg = d3.select(graphRef.value).select('svg');
        if (svg.empty()) return;
        
        // 高亮目标节点
        const nodeGroup = svg.select('.nodes').selectAll('g');
        
        // 移除所有节点的高亮和选中状态
        nodeGroup.classed('selected', false)
          .selectAll('circle')
          .attr('stroke', 'none')
          .attr('stroke-width', 0);
        
        // 给目标节点添加高亮和选中状态
        nodeGroup
          .filter((d: any) => d.id === nodeId)
          .classed('selected', true)
          .select('circle')
          .attr('stroke', themeColors.value.highlightStroke)
          .attr('stroke-width', 5);
        
        // 设置选中节点详情
        setNodeDetail(nodeId);
        
        // 模拟双击节点的缩放效果
        const containerWidth = graphRef.value.clientWidth;
        const containerHeight = graphRef.value.clientHeight;
        
        // 直接操作SVG的transform属性来实现缩放和平移
        const g = svg.select('g');
        if (!g.empty()) {
          const scale = 2.0;
          const translateX = containerWidth / 2 - targetNode.x * scale;
          const translateY = containerHeight / 2 - targetNode.y * scale;
          
          g.transition()
            .duration(750)
            .attr('transform', `translate(${translateX},${translateY}) scale(${scale})`);
        }
      };

      return {
        t,
        graphRef,
        loading,
        graphData,
        getEntityTypeColor,
        refresh,
        selectNodeById,
        handleRelationNodeClick,
        zoomIn,
        zoomOut,
        resetView,
        isFullscreen,
        toggleFullscreen,
        selectedNode,
        selectedNodeProperties,
        closeNodeDetail,
        nodeRelations,
        clickPosition,
        detailPanelStyle
      };
    },
  });
</script>

<style scoped lang="less">
@import './styles/KnowledgeGraph2Ds.less';
</style>

<!-- 可导出供父组件使用的样式 -->
<style lang="less">
@import './styles/KnowledgeGraph2Dg.less';
</style>
