<template>
  <div class="chat-sidebar" :class="{ 'sidebar-visible': showSidebar }">
    <!-- 搜索框 -->
    <div class="search-section">
      <div class="search-input-container">
        <input
          v-model="searchKeyword"
          @input="handleSearchInput"
          @keydown.enter="handleSearch"
          :placeholder="t('chatSidebar.search.placeholder')"
          class="search-input"
          ref="searchInput"
        />
        <button 
          class="search-btn"
          @click="handleSearch"
          :disabled="!searchKeyword.trim()"
          :title="t('chatSidebar.search.button')"
        >
          <Icon icon="ant-design:search-outlined" />
        </button>
        <button 
          v-if="searchKeyword || uniqueSearchResults.length > 0"
          class="clear-search-btn"
          @click="clearSearch"
          :title="t('chatSidebar.search.clearSearch')"
        >
          <Icon icon="ant-design:close-outlined" />
        </button>
      </div>
      
      <!-- 搜索状态提示 -->
      <div v-if="searchState.isSearching" class="search-loading">
        <Icon icon="ant-design:loading-outlined" spin />
        <span>{{ t('chatSidebar.search.searching') }}</span>
      </div>
      
      <!-- 搜索结果统计 -->
      <div v-if="uniqueSearchResults.length > 0 && !searchState.isSearching" class="search-stats">
        {{ t('chatSidebar.search.foundSessions', { count: uniqueSearchResults.length }) }}
      </div>
    </div>
    
    <div class="sidebar-content">
      <!-- 搜索结果 -->
      <div v-if="uniqueSearchResults.length > 0" class="search-results">
        <div class="search-results-header">
          <div class="search-results-title">{{ t('chatSidebar.search.results') }}</div>
        </div>
        <div class="search-results-list">
          <div 
            v-for="result in uniqueSearchResults" 
            :key="result.sessionId"
            class="search-result-item"
            @click="handleSearchResultClick(result.originalMessage)"
          >
            <div class="search-result-content">
              <div class="search-result-text">
                <span v-html="highlightKeyword(result.sessionTitle, searchKeyword)"></span>
              </div>
              <div class="search-result-meta">
                <span class="search-result-role">{{ t('chatSidebar.search.matchCount', { count: result.matchCount }) }}</span>
                <span class="search-result-time">{{ formatTime(result.lastActivity) }}</span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 加载更多搜索结果 -->
        <div v-if="searchState.hasMore" class="load-more-search">
          <button 
            class="load-more-btn"
            @click="loadMoreSearchResults"
            :disabled="searchState.isLoadingMore"
          >
            <Icon v-if="searchState.isLoadingMore" icon="ant-design:loading-outlined" spin />
            {{ searchState.isLoadingMore ? t('chatSidebar.search.loading') : t('chatSidebar.search.loadMore') }}
          </button>
        </div>
      </div>
      
      <!-- 无搜索结果提示 -->
      <div v-if="searchKeyword && uniqueSearchResults.length === 0 && !searchState.isSearching" class="no-search-results">
        <div class="no-results-icon">
          <Icon icon="ant-design:file-search-outlined" />
        </div>
        <div class="no-results-text">{{ t('chatSidebar.search.noResults') }}</div>
        <div class="no-results-desc">{{ t('chatSidebar.search.noResultsDesc') }}</div>
      </div>
      
      <!-- 历史会话列表 -->
      <div v-if="!searchKeyword && uniqueSearchResults.length === 0" class="sessions-list">
        <div class="session-group" v-if="todaySessions.length > 0">
          <div class="session-group-title">{{ t('chatSidebar.sessionGroups.today') }}</div>
          <div 
            v-for="session in todaySessions" 
            :key="session.id"
            class="session-item"
            :class="{ 'active': session.id === currentSessionId }"
            @click="handleSwitchSession(session.id)"
          >
            <div class="session-info">
              <div class="session-name">{{ session.name }}</div>
              <div class="session-meta">
                {{ formatTime(session.created_at) }} · {{ t('chatSidebar.sessionInfo.messageCount', { count: session.message_count || 0 }) }}
              </div>
            </div>
            <div class="session-actions">
              <a-dropdown :trigger="['click']" @click.stop>
                <button class="session-action-btn">
                  <Icon icon="ant-design:more-outlined" />
                </button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleRenameSession(session.id, session.name)">
                      <Icon icon="ant-design:edit-outlined" />
                      {{ t('chatSidebar.sessionActions.rename') }}
                    </a-menu-item>
                    <a-menu-item @click="handleExportSession(session.id)">
                      <Icon icon="ant-design:download-outlined" />
                      {{ t('chatSidebar.sessionActions.export') }}
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="handleDeleteSession(session.id)" danger>
                      <Icon icon="ant-design:delete-outlined" />
                      {{ t('chatSidebar.sessionActions.delete') }}
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </div>
        </div>
        
        <div class="session-group" v-if="yesterdaySessions.length > 0">
          <div class="session-group-title">{{ t('chatSidebar.sessionGroups.yesterday') }}</div>
          <div 
            v-for="session in yesterdaySessions" 
            :key="session.id"
            class="session-item"
            :class="{ 'active': session.id === currentSessionId }"
            @click="handleSwitchSession(session.id)"
          >
            <div class="session-info">
              <div class="session-name">{{ session.name }}</div>
              <div class="session-meta">
                {{ formatTime(session.created_at) }} · {{ t('chatSidebar.sessionInfo.messageCount', { count: session.message_count || 0 }) }}
              </div>
            </div>
            <div class="session-actions">
              <a-dropdown :trigger="['click']" @click.stop>
                <button class="session-action-btn">
                  <Icon icon="ant-design:more-outlined" />
                </button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleRenameSession(session.id, session.name)">
                      <Icon icon="ant-design:edit-outlined" />
                      {{ t('chatSidebar.sessionActions.rename') }}
                    </a-menu-item>
                    <a-menu-item @click="handleExportSession(session.id)">
                      <Icon icon="ant-design:download-outlined" />
                      {{ t('chatSidebar.sessionActions.export') }}
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="handleDeleteSession(session.id)" danger>
                      <Icon icon="ant-design:delete-outlined" />
                      {{ t('chatSidebar.sessionActions.delete') }}
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </div>
        </div>
        
        <div class="session-group" v-if="olderSessions.length > 0">
          <div class="session-group-title">{{ t('chatSidebar.sessionGroups.earlier') }}</div>
          <div 
            v-for="session in olderSessions" 
            :key="session.id"
            class="session-item"
            :class="{ 'active': session.id === currentSessionId }"
            @click="handleSwitchSession(session.id)"
          >
            <div class="session-info">
              <div class="session-name">{{ session.name }}</div>
              <div class="session-meta">
                {{ formatTime(session.created_at) }} · {{ t('chatSidebar.sessionInfo.messageCount', { count: session.message_count || 0 }) }}
              </div>
            </div>
            <div class="session-actions">
              <a-dropdown :trigger="['click']" @click.stop>
                <button class="session-action-btn">
                  <Icon icon="ant-design:more-outlined" />
                </button>
                <template #overlay>
                  <a-menu>
                    <a-menu-item @click="handleRenameSession(session.id, session.name)">
                      <Icon icon="ant-design:edit-outlined" />
                      {{ t('chatSidebar.sessionActions.rename') }}
                    </a-menu-item>
                    <a-menu-item @click="handleExportSession(session.id)">
                      <Icon icon="ant-design:download-outlined" />
                      {{ t('chatSidebar.sessionActions.export') }}
                    </a-menu-item>
                    <a-menu-divider />
                    <a-menu-item @click="handleDeleteSession(session.id)" danger>
                      <Icon icon="ant-design:delete-outlined" />
                      {{ t('chatSidebar.sessionActions.delete') }}
                    </a-menu-item>
                  </a-menu>
                </template>
              </a-dropdown>
            </div>
          </div>
        </div>
        
        <div v-if="sessions.length === 0" class="no-sessions">
          <div class="no-sessions-text">{{ t('chatSidebar.emptyState.noSessions') }}</div>
          <div class="no-sessions-desc">{{ t('chatSidebar.emptyState.noSessionsDesc') }}</div>
        </div>
      </div>
      
      <!-- 分页组件 -->
      <div v-if="!searchKeyword && sessions.length > 0" class="pagination-container">
        <Pagination
          :current="pageNo"
          :page-size="pageSize"
          :page-size-options="pageSizeOptions"
          :total="total"
          :showQuickJumper="false"
          :showSizeChanger="true"
          :showLessItems="true"
          :simple="false"
          @change="handlePageChange"
          size="small"
          class="sidebar-pagination"
        />
        <!-- 自定义总数显示 -->
        <div class="pagination-info">
          {{ t('chatSidebar.sessionInfo.messageCount', { count: total }) }}
        </div>
      </div>
    </div>
    
    <!-- 重命名对话框 -->
    <a-modal 
      v-model:open="renameModal.show"
      :title="t('chatSidebar.confirm.renameSession')"
      @ok="handleRenameConfirm"
      @cancel="renameModal.show = false"
    >
      <a-input 
        v-model:value="renameModal.newName"
        :placeholder="t('chatSidebar.confirm.sessionNamePlaceholder')"
        @keydown.enter="handleRenameConfirm"
      />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { Icon } from '/@/components/Icon';
import { Pagination } from 'ant-design-vue';
import { useSearch } from '../composables/useSearch';
import { useSidebar } from '../composables/useSidebar';
import { useUtils } from '../composables/useUtils';
import type { ChatSession } from '../types';
import { useI18n } from '/@/hooks/web/useI18n';

interface Props {
  showSidebar: boolean;
  sessions: ChatSession[];
  currentSessionId: string;
  userId: string;
  // 分页相关props
  pageNo?: number;
  pageSize?: number;
  total?: number;
}

interface Emits {
  (e: 'switch-session', sessionId: string): void;
  (e: 'create-new-session'): void;
  (e: 'rename-session', sessionId: string, newName: string): void;
  (e: 'delete-session', sessionId: string): void;
  (e: 'export-session', sessionId: string): void;
  // 分页相关事件
  (e: 'page-change', page: number, pageSize: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  pageNo: 1,
  pageSize: 20,
  total: 0
});

const emit = defineEmits<Emits>();

// 国际化hook
const { t } = useI18n();

// 分页相关状态
const pageNo = ref(props.pageNo);
const pageSize = ref(props.pageSize);
const total = ref(props.total);
const pageSizeOptions = ref(['10', '20', '60', '100']);

// 使用搜索相关的composable
const {
  searchKeyword,
  searchResults,
  searchInput,
  searchState,
  uniqueSearchResults,
  handleSearchInput,
  handleSearch,
  loadMoreSearchResults,
  clearSearch,
  handleSearchResultClick: originalHandleSearchResultClick,
  highlightKeyword,
  cleanup: cleanupSearch
} = useSearch(props.userId, computed(() => props.sessions));

// 使用侧边栏相关的composable
const {
  renameModal,
  todaySessions,
  yesterdaySessions,
  olderSessions,
  handleRenameSession,
  handleRenameConfirm: originalHandleRenameConfirm
} = useSidebar(computed(() => props.sessions), props.currentSessionId);

// 使用工具函数
const { formatTime } = useUtils();

// 监听props变化，更新分页状态
import { watch } from 'vue';
watch(() => props.pageNo, (newVal) => {
  pageNo.value = newVal;
});

watch(() => props.pageSize, (newVal) => {
  pageSize.value = newVal;
});

watch(() => props.total, (newVal) => {
  total.value = newVal;
});

// 包装事件处理函数
const handleSwitchSession = (sessionId: string) => {
  emit('switch-session', sessionId);
};

const handleCreateNewSession = () => {
  emit('create-new-session');
};

const handleDeleteSession = (sessionId: string) => {
  emit('delete-session', sessionId);
};

const handleExportSession = (sessionId: string) => {
  emit('export-session', sessionId);
};

const handleSearchResultClick = (originalMessage: any) => {
  originalHandleSearchResultClick(originalMessage, async (sessionId: string) => {
    emit('switch-session', sessionId);
  });
};

const handleRenameConfirm = () => {
  originalHandleRenameConfirm(async (sessionId: string, newName: string) => {
    emit('rename-session', sessionId, newName);
  });
};

// 分页变化处理
const handlePageChange = (page: number, current: number) => {
  pageNo.value = page;
  pageSize.value = current;
  emit('page-change', page, current);
};

// 清理资源
onUnmounted(() => {
  cleanupSearch();
});
</script>

<style scoped lang="less">
// 引入侧边栏相关样式
@import '../styles/sidebar.less';

// 添加分页组件相关样式
.pagination-container {
  padding: 12px 8px 8px;
  border-top: 1px solid #f0f0f0;
  background: #fafafa;
  
  .sidebar-pagination {
    :deep(.ant-pagination) {
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-wrap: wrap;
      gap: 2px;
      
      .ant-pagination-item {
        min-width: 20px;
        height: 20px;
        line-height: 18px;
        font-size: 11px;
        margin-right: 2px;
        border-radius: 3px;
        
        a {
          padding: 0 4px;
          font-size: 11px;
        }
      }
      
      .ant-pagination-item-active {
        border-color: #1890ff;
        background: #1890ff;
        
        a {
          color: #fff;
        }
      }
      
      .ant-pagination-prev,
      .ant-pagination-next {
        min-width: 20px;
        height: 20px;
        line-height: 18px;
        margin-right: 2px;
        border-radius: 3px;
        
        .anticon {
          font-size: 10px;
        }
      }
      
      .ant-pagination-jump-prev,
      .ant-pagination-jump-next {
        min-width: 20px;
        height: 20px;
        line-height: 18px;
        margin-right: 2px;
        
        .ant-pagination-item-link-icon {
          font-size: 10px;
        }
      }
      
      .ant-pagination-options {
        margin-left: 8px;
        
        .ant-select {
          font-size: 11px;
          min-width: 55px;
          
          .ant-select-selector {
            height: 20px;
            padding: 0 6px;
            font-size: 11px;
            border-radius: 3px;
          }
          
          .ant-select-selection-item {
            line-height: 18px;
            padding-right: 12px;
          }
          
          .ant-select-arrow {
            font-size: 10px;
            right: 4px;
          }
        }
      }
    }
  }
  
  .pagination-info {
    text-align: center;
    font-size: 11px;
    color: #666;
    margin-top: 6px;
    line-height: 1;
  }
}

// 调整会话列表的高度，为分页组件留出更多空间
.sessions-list {
  max-height: calc(100vh - 320px);
  overflow-y: auto;
  padding-bottom: 8px;
}

/* 暗黑模式样式 */
[data-theme='dark'] .pagination-container {
  border-top-color: #303030;
  background: #262626;

  .sidebar-pagination {
    :deep(.ant-pagination) {
      .ant-pagination-item {
        background: #1a1a1a;
        border-color: #404040;

        a {
          color: rgba(255, 255, 255, 0.65);
        }

        &:hover {
          border-color: #1890ff;

          a {
            color: #1890ff;
          }
        }
      }

      .ant-pagination-item-active {
        background: #1890ff;
        border-color: #1890ff;

        a {
          color: #fff;
        }
      }

      .ant-pagination-prev,
      .ant-pagination-next {
        background: #1a1a1a;
        border-color: #404040;
        color: rgba(255, 255, 255, 0.65);

        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }

        &.ant-pagination-disabled {
          background: #1a1a1a;
          border-color: #404040;
          color: rgba(255, 255, 255, 0.25);
        }
      }

      .ant-pagination-jump-prev,
      .ant-pagination-jump-next {
        color: rgba(255, 255, 255, 0.45);

        &:hover {
          color: #1890ff;
        }
      }

      .ant-pagination-options {
        .ant-select {
          .ant-select-selector {
            background: #1a1a1a;
            border-color: #404040;
            color: rgba(255, 255, 255, 0.85);

            &:hover {
              border-color: #1890ff;
            }
          }

          .ant-select-arrow {
            color: rgba(255, 255, 255, 0.45);
          }
        }
      }
    }
  }

  .pagination-info {
    color: rgba(255, 255, 255, 0.45);
  }
}
</style>