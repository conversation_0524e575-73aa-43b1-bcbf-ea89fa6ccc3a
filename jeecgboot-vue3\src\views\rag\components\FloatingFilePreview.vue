<template>
  <div 
    v-if="show" 
    class="floating-file-preview"
    :style="{ 
      transform: `translate(${position.x}px, ${position.y}px)`,
      width: size.width + 'px',
      height: size.height + 'px'
    }"
    @mousedown="startDrag"
  >
    <div class="preview-header" @mousedown="startDrag">
      <div class="header-info">
        <Icon :icon="getFileIcon(currentFile?.name || '')" class="file-icon" />
        <span class="file-name">{{ currentFile?.name }}</span>
        <div class="drag-indicator">
          <div class="drag-dots">
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <button class="action-btn" @click="downloadFile" :title="t('floatingFilePreview.header.download')">
          <Icon icon="ant-design:download-outlined" />
        </button>
        <button class="action-btn" @click="openInNewWindow" :title="t('floatingFilePreview.header.openInNewWindow')">
          <Icon icon="ant-design:export-outlined" />
        </button>
        <button class="action-btn close-btn" @click="closePreview" :title="t('floatingFilePreview.header.close')">
          <Icon icon="ant-design:close-outlined" />
        </button>
      </div>
    </div>
    
    <div class="preview-content">
      <div v-if="loading" class="loading-container">
        <Icon icon="ant-design:loading-outlined" spin />
        <span>{{ t('floatingFilePreview.status.loading') }}</span>
      </div>
      
      <div v-else-if="error" class="error-container">
        <Icon icon="ant-design:exclamation-circle-outlined" />
        <span>{{ error }}</span>
        <button @click="retryLoad" class="retry-btn">{{ t('floatingFilePreview.status.retry') }}</button>
      </div>
      
      <div v-else class="content-wrapper">
        <!-- 图片预览 -->
        <div v-if="fileType === 'image'" class="image-preview">
          <img :src="previewUrl" :alt="currentFile?.name" />
        </div>
        
        <!-- 文档预览 -->
        <div v-else-if="fileType === 'document'" class="document-preview">
          <iframe 
            :src="documentUrl" 
            frameborder="0"
            width="100%" 
            height="100%"
            :style="{ visibility: (resizing && optimizeResize) ? 'hidden' : 'visible' }"
          ></iframe>
          <div v-if="resizing && optimizeResize" class="resize-overlay">
            <Icon icon="ant-design:file-pdf-outlined" />
            <span>{{ t('floatingFilePreview.status.resizing') }}</span>
          </div>
        </div>
        
        <!-- 文本预览 -->
        <div v-else-if="fileType === 'text'" class="text-preview">
          <pre>{{ textContent }}</pre>
        </div>
        
        <!-- 不支持的文件类型 -->
        <div v-else class="unsupported-preview">
          <Icon icon="ant-design:file-unknown-outlined" />
          <span>{{ t('floatingFilePreview.fileTypes.unsupported') }}</span>
          <button @click="downloadFile" class="download-btn">{{ t('floatingFilePreview.fileTypes.download') }}</button>
        </div>
      </div>
    </div>
    
    <!-- 调整大小手柄 -->
    <div class="resize-handles">
      <div class="resize-handle resize-se" @mousedown="(event) => startResize('se', event)"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue';
import { message } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';
import { useGlobSetting } from '/@/hooks/setting';
import { encryptByBase64 } from '/@/utils/cipher';
import { useI18n } from '/@/hooks/web/useI18n';

interface FileInfo {
  name: string;
  url: string;
  type?: string;
}

interface Props {
  show: boolean;
  file: FileInfo | null;
}

interface Emits {
  (e: 'close'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const glob = useGlobSetting();

// 国际化hook
const { t } = useI18n();

// 组件状态
const loading = ref(false);
const error = ref('');
const textContent = ref('');
const currentFile = ref<FileInfo | null>(null);

// 位置和大小
const position = reactive({
  x: window.innerWidth - 620, // 默认在右侧
  y: 100
});

const size = reactive({
  width: 600,
  height: 500
});

// 拖动状态
const dragging = ref(false);
const resizing = ref(false);
const dragStart = reactive({ x: 0, y: 0 });
const resizeStart = reactive({ x: 0, y: 0, width: 0, height: 0 });

// 性能优化设置
const optimizeResize = ref(true); // 是否在调整大小时优化性能

// 计算文件类型
const fileType = computed(() => {
  if (!currentFile.value) return 'unknown';
  
  const ext = currentFile.value.name.toLowerCase().split('.').pop() || '';
  
  if (['jpg', 'jpeg', 'png'].includes(ext)) {
    return 'image';
  }
  
  if (['pdf','doc','docx','xls','xlsx','ppt','pptx'].includes(ext)) {
    return 'document';
  }
  
  if (['txt', 'md'].includes(ext)) {
    return 'text';
  }
  
  return 'unknown';
});

// 计算预览URL
const previewUrl = computed(() => {
  if (!currentFile.value) return '';
  return currentFile.value.url;
});

const documentUrl = computed(() => {
  if (!currentFile.value) return '';
  try {
    let preurl = currentFile.value.url.replace("localhost", `${glob.serverIp}`);
    let url = encodeURIComponent(encryptByBase64(preurl));
    return `${glob.viewUrl}?url=${url}`;
  } catch (error) {
    console.error('生成文档预览URL失败:', error);
    return '';
  }
});

// 监听文件变化
watch(() => props.file, (newFile) => {
  if (newFile) {
    currentFile.value = newFile;
    loadFileContent();
  }
}, { immediate: true });

// 加载文件内容
const loadFileContent = async () => {
  if (!currentFile.value) return;
  
  loading.value = true;
  error.value = '';
  
  try {
    if (fileType.value === 'text') {
      // 加载文本内容
      const response = await fetch(currentFile.value.url);
      if (!response.ok) throw new Error(t('floatingFilePreview.errors.loadFailed'));
      textContent.value = await response.text();
    }
  } catch (err) {
    console.error('加载文件内容失败:', err);
    error.value = t('floatingFilePreview.errors.loadFailed');
  } finally {
    loading.value = false;
  }
};

// 重试加载
const retryLoad = () => {
  loadFileContent();
};

// 获取文件图标
const getFileIcon = (fileName: string): string => {
  const ext = fileName.toLowerCase().split('.').pop() || '';
  
  if (['jpg', 'jpeg', 'png'].includes(ext)) {
    return 'ant-design:file-image-outlined';
  }
  
  if (['pdf'].includes(ext)) {
    return 'ant-design:file-pdf-outlined';
  }
  
  if (['doc', 'docx'].includes(ext)) {
    return 'ant-design:file-word-outlined';
  }
  
  if (['xls', 'xlsx'].includes(ext)) {
    return 'ant-design:file-excel-outlined';
  }
  
  if (['ppt', 'pptx'].includes(ext)) {
    return 'ant-design:file-ppt-outlined';
  }
  
  if (['txt', 'md'].includes(ext)) {
    return 'ant-design:file-text-outlined';
  }
  
  return 'ant-design:file-outlined';
};

// 开始拖动
const startDrag = (event: MouseEvent) => {
  // 不响应按钮上的拖动
  if (event.target && (event.target as HTMLElement).classList.contains('action-btn')) {
    return;
  }
  
  // 不响应调整大小手柄上的拖动
  if (event.target && (event.target as HTMLElement).classList.contains('resize-handle')) {
    return;
  }
  
  // 检查是否点击在按钮或调整手柄的父元素上
  const target = event.target as HTMLElement;
  if (target.closest('.action-btn') || target.closest('.resize-handle')) {
    return;
  }
  
  dragging.value = true;
  dragStart.x = event.clientX - position.x;
  dragStart.y = event.clientY - position.y;
  
  document.addEventListener('mousemove', handleDrag);
  document.addEventListener('mouseup', stopDrag);
  event.preventDefault();
};

// 处理拖动
const handleDrag = (() => {
  let animationId: number | null = null;
  let pendingUpdate = false;
  
  return (event: MouseEvent) => {
    if (!dragging.value) return;
    
    // 如果已经有待处理的更新，则跳过
    if (pendingUpdate) return;
    
    pendingUpdate = true;
    
    const clientX = event.clientX;
    const clientY = event.clientY;
    
    // 使用requestAnimationFrame确保流畅的动画
    animationId = requestAnimationFrame(() => {
      if (!dragging.value) {
        pendingUpdate = false;
        return;
      }
      
      position.x = Math.max(0, Math.min(window.innerWidth - size.width, clientX - dragStart.x));
      position.y = Math.max(0, Math.min(window.innerHeight - size.height, clientY - dragStart.y));
      
      pendingUpdate = false;
    });
  };
})();

// 停止拖动
const stopDrag = () => {
  dragging.value = false;
  document.removeEventListener('mousemove', handleDrag);
  document.removeEventListener('mouseup', stopDrag);
};

// 开始调整大小
const startResize = (direction: string, event: MouseEvent) => {
  resizing.value = true;
  resizeStart.x = event.clientX;
  resizeStart.y = event.clientY;
  resizeStart.width = size.width;
  resizeStart.height = size.height;
  
  // 使用requestAnimationFrame优化性能
  let animationId: number | null = null;
  let pendingUpdate = false;
  
  const handleResize = (event: MouseEvent) => {
    if (!resizing.value) return;
    
    // 如果已经有待处理的更新，则跳过
    if (pendingUpdate) return;
    
    pendingUpdate = true;
    
    const deltaX = event.clientX - resizeStart.x;
    const deltaY = event.clientY - resizeStart.y;
    
    // 使用requestAnimationFrame确保流畅的动画
    animationId = requestAnimationFrame(() => {
      if (!resizing.value) {
        pendingUpdate = false;
        return;
      }
      
      if (direction === 'se') {
        // 计算新的尺寸
        const newWidth = Math.max(400, resizeStart.width + deltaX);
        const newHeight = Math.max(300, resizeStart.height + deltaY);
        
        // 确保窗口不会超出屏幕边界
        const maxWidth = window.innerWidth - position.x - 10; // 留出10px边距
        const maxHeight = window.innerHeight - position.y - 10; // 留出10px边距
        
        size.width = Math.min(newWidth, maxWidth);
        size.height = Math.min(newHeight, maxHeight);
      }
      
      pendingUpdate = false;
    });
  };
  
  const stopResize = () => {
    resizing.value = false;
    pendingUpdate = false;
    
    // 清理动画帧
    if (animationId) {
      cancelAnimationFrame(animationId);
      animationId = null;
    }
    
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', stopResize);
  };
  
  document.addEventListener('mousemove', handleResize);
  document.addEventListener('mouseup', stopResize);
  event.preventDefault();
  event.stopPropagation();
};

// 下载文件
const downloadFile = () => {
  if (!currentFile.value) return;
  
  const link = document.createElement('a');
  link.href = currentFile.value.url;
  link.download = currentFile.value.name;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 在新窗口打开
const openInNewWindow = () => {
  if (!currentFile.value) return;
  
  if (fileType.value === 'document') {
    window.open(documentUrl.value, '_blank');
  } else {
    window.open(currentFile.value.url, '_blank');
  }
};

// 关闭预览
const closePreview = () => {
  emit('close');
};
</script>

<style scoped lang="less">
.floating-file-preview {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
  overflow: hidden;
  user-select: none;
  min-width: 400px;
  min-height: 300px;
  will-change: transform, width, height;
  
  .preview-header {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e8e8e8;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: move;
    transition: background 0.2s ease;
    
    &:hover {
      background: linear-gradient(135deg, #f0f4f8 0%, #e8f0f8 100%);
      
      .drag-indicator {
        opacity: 1;
      }
    }
    
    .header-info {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;
      min-width: 0;
      
      .file-icon {
        color: #1890ff;
        font-size: 16px;
        flex-shrink: 0;
      }
      
      .file-name {
        font-size: 14px;
        font-weight: 500;
        color: #333;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 8px;
      }
      
      .drag-indicator {
        opacity: 0.6;
        transition: opacity 0.2s ease;
        
        .drag-dots {
          display: flex;
          gap: 2px;
          align-items: center;
          
          .dot {
            width: 4px;
            height: 4px;
            border-radius: 50%;
            background: #999;
            display: block;
            transition: background 0.2s ease;
          }
        }
        
        &:hover .dot {
          background: #666;
        }
      }
    }
    
          .header-actions {
        display: flex;
        gap: 4px;
        
        .action-btn {
          width: 28px;
          height: 28px;
          border: 1px solid #d9d9d9;
          background: white;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.2s;
          color: #666;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
          
          &:hover {
            background: #f5f5f5;
            color: #333;
            border-color: #40a9ff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transform: translateY(-1px);
          }
          
          &:active {
            transform: translateY(0);
          }
          
          &.close-btn {
            &:hover {
              background: #ff4d4f;
              color: white;
              border-color: #ff4d4f;
            }
          }
          
          .anticon {
            font-size: 14px;
            font-weight: 500;
          }
        }
      }
  }
  
  .preview-content {
    height: calc(100% - 57px);
    position: relative;
    contain: layout style paint;
    will-change: auto;
    
    .loading-container,
    .error-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100%;
      gap: 12px;
      color: #666;
      
      .retry-btn {
        padding: 6px 12px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        background: white;
        cursor: pointer;
        
        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }
      }
    }
    
    .content-wrapper {
      height: 100%;
      
      .image-preview {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
        
        img {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }
      
      .document-preview {
        height: 100%;
        position: relative;
        
        iframe {
          width: 100%;
          height: 100%;
          border: none;
          background: #f5f5f5;
          will-change: auto;
        }
        
        .resize-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(245, 245, 245, 0.8);
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 8px;
          color: #666;
          font-size: 14px;
          backdrop-filter: blur(2px);
          
          .anticon {
            font-size: 24px;
            color: #1890ff;
          }
        }
      }
      
      .text-preview {
        height: 100%;
        overflow: auto;
        padding: 20px;
        
        pre {
          margin: 0;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 13px;
          line-height: 1.5;
          white-space: pre-wrap;
          word-break: break-word;
        }
      }
      
      .unsupported-preview {
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 16px;
        color: #999;
        
        .anticon {
          font-size: 48px;
        }
        
        .download-btn {
          padding: 8px 16px;
          border: 1px solid #1890ff;
          border-radius: 6px;
          background: #1890ff;
          color: white;
          cursor: pointer;
          
          &:hover {
            background: #40a9ff;
          }
        }
      }
    }
  }
  
  .resize-handles {
    .resize-handle {
      position: absolute;
      
      &.resize-se {
        bottom: 0;
        right: 0;
        width: 16px;
        height: 16px;
        cursor: se-resize;
        background: transparent;
        
        &::before {
          content: '';
          position: absolute;
          right: 3px;
          bottom: 3px;
          width: 10px;
          height: 10px;
          background-image: 
            linear-gradient(-45deg, transparent 0px, transparent 2px, #999 2px, #999 4px, transparent 4px, transparent 6px, #999 6px, #999 8px, transparent 8px);
          opacity: 0.6;
        }
        
        &:hover {
          background: rgba(64, 169, 255, 0.1);
          
          &::before {
            opacity: 1;
            background-image: 
              linear-gradient(-45deg, transparent 0px, transparent 2px, #40a9ff 2px, #40a9ff 4px, transparent 4px, transparent 6px, #40a9ff 6px, #40a9ff 8px, transparent 8px);
          }
        }
      }
    }
  }
}

/* 暗黑模式样式 */
[data-theme='dark'] .floating-file-preview {
  background: #1f1f1f;
  border-color: #404040;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);

  .preview-header {
    background: linear-gradient(135deg, #262626 0%, #303030 100%);
    border-bottom-color: #404040;

    &:hover {
      background: linear-gradient(135deg, #303030 0%, #383838 100%);
    }

    .header-info {
      .file-icon {
        color: #1890ff;
      }

      .file-name {
        color: rgba(255, 255, 255, 0.85);
      }

      .drag-indicator {
        .drag-dots {
          .dot {
            background: rgba(255, 255, 255, 0.45);
          }
        }

        &:hover .dot {
          background: rgba(255, 255, 255, 0.65);
        }
      }
    }

    .header-actions {
      .action-btn {
        background: #262626;
        border-color: #404040;
        color: rgba(255, 255, 255, 0.65);

        &:hover {
          background: #303030;
          border-color: #1890ff;
          color: #1890ff;
        }

        &.close-btn:hover {
          background: #ff4d4f;
          border-color: #ff4d4f;
          color: white;
        }
      }
    }
  }

  .preview-content {
    background: #1f1f1f;

    .content-wrapper {
      .file-content {
        background: #1f1f1f;
        color: rgba(255, 255, 255, 0.85);

        .text-content {
          color: rgba(255, 255, 255, 0.85);

          pre {
            background: #262626;
            border-color: #404040;
            color: rgba(255, 255, 255, 0.85);
          }
        }

        .image-content {
          background: #1a1a1a;

          .preview-image {
            background: #1a1a1a;
          }
        }

        .pdf-content {
          background: #1a1a1a;

          iframe {
            background: #1a1a1a;
          }
        }

        .unsupported-content {
          background: #262626;
          border-color: #404040;
          color: rgba(255, 255, 255, 0.65);

          .unsupported-icon {
            color: rgba(255, 255, 255, 0.45);
          }

          .unsupported-text {
            color: rgba(255, 255, 255, 0.65);
          }

          .unsupported-desc {
            color: rgba(255, 255, 255, 0.45);
          }
        }
      }

      .resize-handle {
        &::before {
          background-image:
            linear-gradient(-45deg, transparent 0px, transparent 2px, rgba(255, 255, 255, 0.45) 2px, rgba(255, 255, 255, 0.45) 4px, transparent 4px, transparent 6px, rgba(255, 255, 255, 0.45) 6px, rgba(255, 255, 255, 0.45) 8px, transparent 8px);
        }

        &:hover {
          background: rgba(24, 144, 255, 0.15);

          &::before {
            background-image:
              linear-gradient(-45deg, transparent 0px, transparent 2px, #40a9ff 2px, #40a9ff 4px, transparent 4px, transparent 6px, #40a9ff 6px, #40a9ff 8px, transparent 8px);
          }
        }
      }
    }
  }
}
</style>