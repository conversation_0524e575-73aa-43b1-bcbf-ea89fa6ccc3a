<template>
  <div class="chat-message" :class="[message.type, { 'has-sources': message.sources?.length }]">
    <div class="message-avatar">
      <div v-if="message.type === 'user'" class="user-avatar">
        <Icon icon="ant-design:user-outlined" />
      </div>
      <div v-else class="ai-avatar">
        <div class="ai-head">
          <div class="ai-brain">
            <div class="ai-core"></div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="message-content">
      <div class="message-header">
        <span class="message-sender">
          {{ message.type === 'user' ? t('chatMessage.sender.user') : t('chatMessage.sender.assistant') }}
        </span>
        <span class="message-time">{{ formatTime(message.timestamp) }}</span>
      </div>
      
      <div class="message-body">
        <div 
          v-if="message.type === 'user'" 
          class="message-text user-text"
        >
          {{ message.content }}
          
          <!-- 用户上传的图片显示 -->
          <div v-if="message.images && message.images.length > 0" class="user-images">
            <div 
              v-for="(image, index) in message.images" 
              :key="index"
              class="user-image-item"
            >
              <AImage
                :src="image.url"
                :alt="image.alt || image.name"
                :preview="{
                  zIndex: 2000
                }"
                class="user-uploaded-image"
                :style="{
                  maxWidth: '200px',
                  maxHeight: '200px',
                  width: 'auto',
                  height: 'auto',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  objectFit: 'cover'
                }"
              />
              <div class="image-name">{{ image.name }}</div>
            </div>
          </div>
          
          <!-- 用户上传的文件显示 -->
          <div v-if="message.files && message.files.length > 0" class="user-files">
            <div 
              v-for="(file, index) in message.files" 
              :key="index"
              class="user-file-item"
              @click="previewFile(file)"
            >
              <Icon :icon="getFileIcon(file.name)" class="file-icon" />
              <span class="file-name">{{ file.name }}</span>
            </div>
          </div>
        </div>
        <div v-else class="message-text ai-text">
          <!-- 如果内容为空，显示占位文本 -->
          <span v-if="!message.content" style="color: #ccc; font-style: italic;">
            {{ t('chatMessage.status.waitingReply') }}
          </span>
          <!-- 如果有内容，渲染Markdown -->
          <div 
            v-else
            class="markdown-content" 
            v-html="renderMarkdown(message.content)"
            @click="handlePathLinkClick"
          ></div>
        </div>
        
        <!-- 知识片段 - 内联SourceDisplay组件功能 -->
        <div v-if="message.sources && message.sources.length > 0" class="source-display">
          <div class="source-button-container">
            <button 
              class="source-button"
              @click="handleShowSources"
              :title="t('chatMessage.sources.viewSources', { count: message.sources.length })"
            >
              <Icon icon="ant-design:book-outlined" />
              <span>{{ t('chatMessage.sources.knowledgeSnippets', { count: message.sources.length }) }}</span>
              <Icon icon="ant-design:right-outlined" />
            </button>
          </div>
        </div>
      </div>
      
      <div class="message-actions">
        <button 
          class="action-btn copy-btn"
          @click="copyMessage"
          :title="t('chatMessage.actions.copy')"
        >
          <Icon icon="ant-design:copy-outlined" />
        </button>
        <button 
          v-if="message.type === 'assistant'"
          class="action-btn regenerate-btn"
          @click="regenerateMessage"
          :title="t('chatMessage.actions.regenerate')"
        >
          <Icon icon="ant-design:reload-outlined" />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { message as antMessage, Image as AImage } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';
import type { ChatMessage } from '../types';
// 图片预览所需的导入
import { createImgPreview } from '/@/components/Preview/index';
import { useI18n } from '/@/hooks/web/useI18n';

interface Props {
  message: ChatMessage;
  renderMarkdown: (text: string) => string;
  formatTime: (timestamp: number) => string;
}

interface Emits {
  (e: 'regenerate', messageId: string): void;
  (e: 'showSources', sources: any[]): void;
  (e: 'previewFile', file: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 国际化hook
const { t } = useI18n();

const copyMessage = async () => {
  try {
    await navigator.clipboard.writeText(props.message.content);
    antMessage.success(t('chatMessage.messages.copySuccess'));
  } catch (error) {
    console.error('复制失败:', error);
    antMessage.error(t('chatMessage.messages.copyFailed'));
  }
};

const regenerateMessage = () => {
  emit('regenerate', props.message.id);
};

const handleShowSources = () => {
  if (props.message.sources && props.message.sources.length > 0) {
    emit('showSources', props.message.sources);
  }
};

const handlePathLinkClick = (event: MouseEvent) => {
  const target = event.target as HTMLElement;
  if (target.tagName === 'A' && target.textContent) {
    console.log('点击路径链接:', target.textContent);
  }
};

// 获取文件图标
const getFileIcon = (fileName: string): string => {
  const ext = fileName.toLowerCase().split('.').pop() || '';
  
  // 图片文件
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) {
    return 'ant-design:file-image-outlined';
  }
  
  // 文档文件
  if (['pdf'].includes(ext)) {
    return 'ant-design:file-pdf-outlined';
  }
  
  if (['doc', 'docx'].includes(ext)) {
    return 'ant-design:file-word-outlined';
  }
  
  if (['xls', 'xlsx'].includes(ext)) {
    return 'ant-design:file-excel-outlined';
  }
  
  if (['ppt', 'pptx'].includes(ext)) {
    return 'ant-design:file-ppt-outlined';
  }
  
  // 文本文件
  if (['txt', 'md', 'rtf'].includes(ext)) {
    return 'ant-design:file-text-outlined';
  }
  
  // 压缩文件
  if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
    return 'ant-design:file-zip-outlined';
  }
  
  // 默认文件图标
  return 'ant-design:file-outlined';
};

// 判断是否为图片文件
const isImageFile = (fileName: string): boolean => {
  const ext = fileName.toLowerCase().split('.').pop() || '';
  return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext);
};

// 预览文件
const previewFile = (file: any) => {
  if (!file.url) {
    antMessage.warning(t('chatMessage.messages.previewNotAvailable'));
    return;
  }
  
  const fileName = file.name;
  
  if (isImageFile(fileName)) {
    // 图片文件使用 createImgPreview 预览
    createImgPreview({ imageList: [file.url], maskClosable: true });
  } else {
    // 其他文件发射事件给父组件处理
    emit('previewFile', file);
  }
};
</script>

<style scoped lang="less">
.chat-message {
  display: flex;
  gap: 12px;
  margin-bottom: 40px;

  &:last-child {
    margin-bottom: 50px;
  }
  
  &.user {
    flex-direction: row-reverse;
    
    .message-content {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 20px 20px 8px 20px;
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.25);
      
      .message-header {
        .message-sender {
          color: rgba(255, 255, 255, 0.9);
          font-weight: 600;
        }
        
        .message-time {
          color: rgba(255, 255, 255, 0.7);
        }
      }
      
      .user-text {
        color: white;
        font-weight: 500;
        
        .user-images {
          margin-top: 12px;
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
          
          .user-image-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            
                         .user-uploaded-image {
               box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
               transition: transform 0.2s ease !important;
               
               &:hover {
                 transform: scale(1.05) !important;
               }
             }
            
            .image-name {
              margin-top: 4px;
              font-size: 12px;
              color: rgba(255, 255, 255, 0.8);
              text-align: center;
              max-width: 200px;
              word-break: break-all;
            }
          }
        }
        
        .user-files {
          margin-top: 12px;
          display: flex;
          flex-direction: column;
          gap: 6px;
          
          .user-file-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            
            &:hover {
              background: rgba(255, 255, 255, 0.2);
              border-color: rgba(255, 255, 255, 0.3);
            }
            
            .file-icon {
              color: rgba(255, 255, 255, 0.9);
              font-size: 16px;
              flex-shrink: 0;
            }
            
            .file-name {
              font-size: 13px;
              color: rgba(255, 255, 255, 0.9);
              flex: 1;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
        }
      }
    }
    
    .message-actions {
      flex-direction: row-reverse;
    }
  }
  
  &.assistant {
    .message-content {
      background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
      color: #1e293b;
      border-radius: 20px 20px 20px 8px;
      border: 1px solid rgba(148, 163, 184, 0.1);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      
      .message-header {
        .message-sender {
          color: #475569;
          font-weight: 600;
        }
        
        .message-time {
          color: #94a3b8;
        }
      }
      
      .ai-text {
        font-weight: 400;
        line-height: 1.7;
      }
    }
  }
  
  .message-avatar {
    flex-shrink: 0;
    width: 36px;
    height: 36px;
    
    .user-avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 16px;
      box-shadow: 0 3px 12px rgba(102, 126, 234, 0.35);
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%);
        border-radius: 50%;
      }
      
      .anticon {
        z-index: 1;
        filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
      }
    }
    
    .ai-avatar {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.1) 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(79, 172, 254, 0.15);
      position: relative;
      overflow: hidden;
      border: 0.5px solid rgba(79, 172, 254, 0.2);
      
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.01) 100%);
        border-radius: 50%;
      }
      
              .ai-head {
          width: 24px;
          height: 24px;
          position: relative;
          z-index: 1;
          
          .ai-brain {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.4);
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(2px);
            
            .ai-core {
              width: 8px;
              height: 8px;
              background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
              border-radius: 50%;
              animation: pulse 2s ease-in-out infinite;
              box-shadow: 0 0 12px rgba(79, 172, 254, 0.8);
            }
            
            &::before {
              content: '';
              position: absolute;
              width: 20px;
              height: 20px;
              border: 1.5px solid rgba(79, 172, 254, 0.6);
              border-radius: 50%;
              animation: rotate 4s linear infinite;
            }
            
            &::after {
              content: '';
              position: absolute;
              width: 15px;
              height: 15px;
              border: 1.5px solid rgba(0, 242, 254, 0.7);
              border-top-color: transparent;
              border-left-color: transparent;
              border-radius: 50%;
              animation: rotate 2s linear infinite reverse;
            }
          }
        }
    }
  }
  
  .message-content {
    flex: 1;
    min-width: 0;
    padding: 12px 16px;
    position: relative;
    
    &:hover .message-actions {
      opacity: 1;
    }
    
    .message-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
      
      .message-sender {
        font-size: 12px;
        font-weight: 500;
      }
      
      .message-time {
        font-size: 11px;
      }
    }
    
    .message-body {
      .message-text {
        font-size: 16px;
        line-height: 1.6;
        word-break: break-word;
        
        // Markdown内容样式 - 参考GitHub风格
        &.markdown-content {
          line-height: 1.6;
          color: #24292e;
          font-size: 16px;
          
          :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
          }
          
          :deep(h1) {
            font-size: 2em;
            padding-bottom: 0.3em;
            border-bottom: 1px solid #eaecef;
            color: #333;
          }
          
          :deep(h2) {
            font-size: 1.5em;
            padding-bottom: 0.3em;
            border-bottom: 1px solid #eaecef;
            color: #333;
          }
          
          :deep(h3) {
            font-size: 1.25em;
            color: #333;
          }
          
          :deep(h4) {
            font-size: 1em;
            color: #333;
          }
          
          :deep(p) {
            margin-top: 0;
            margin-bottom: 16px;
          }
          
          :deep(ul), :deep(ol) {
            padding-left: 2em;
            margin-top: 0;
            margin-bottom: 16px;
          }
          
          :deep(ul) {
            list-style-type: disc;
          }
          
          :deep(ol) {
            list-style-type: decimal;
          }
          
          :deep(li) {
            margin: 4px 0;
            line-height: 1.5;
          }
          
          // 行内代码
          :deep(code) {
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            background-color: rgba(27, 31, 35, 0.05);
            border-radius: 3px;
          }
          
          // 代码块
          :deep(pre) {
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
            background-color: #f6f8fa;
            border-radius: 6px;
            padding: 16px;
            overflow: auto;
            margin-bottom: 16px;
            word-wrap: normal;
            
            code {
              padding: 0;
              margin: 0;
              font-size: 100%;
              word-break: normal;
              white-space: pre;
              background: transparent;
              border: 0;
            }
          }
          
          // 引用
          :deep(blockquote) {
            padding: 0 1em;
            color: #6a737d;
            border-left: 0.25em solid #dfe2e5;
            margin: 0 0 16px 0;
          }
          
          // 表格 - 协调的灰色配色方案，使用边框包装器方式
          :deep(.table-wrapper) {
            border: 1px solid #d1d5db !important;
            border-radius: 8px !important;
            overflow: hidden !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
          }
          
          :deep(.markdown-content table),
          :deep(table) {
            display: table !important;
            width: 100% !important;
            max-width: 100%;
            margin: 0 !important;
            border-spacing: 0 !important;
            border-collapse: collapse !important;
            border: none !important;
            background-color: #ffffff !important;
            font-size: 14px !important;
            border-radius: 0 !important;
            
            // 表格容器，支持水平滚动
            overflow-x: auto;
          }
          
          :deep(.markdown-content table tr),
          :deep(table tr) {
            background-color: #ffffff !important;
            border: none !important;
            
            &:nth-child(2n) {
              background-color: #f9fafb !important;
            }
          }
          
          :deep(.markdown-content table th),
          :deep(.markdown-content table td),
          :deep(table th),
          :deep(table td) {
            padding: 8px 12px !important;
            border: none !important;
            border-right: 1px solid #d1d5db !important;
            border-bottom: 1px solid #d1d5db !important;
            text-align: left !important;
            vertical-align: top !important;
            word-wrap: break-word !important;
            min-width: 80px !important;
          }
          
          // 移除最后一列的右边框
          :deep(.markdown-content table th:last-child),
          :deep(.markdown-content table td:last-child),
          :deep(table th:last-child),
          :deep(table td:last-child) {
            border-right: none !important;
          }
          
          // 移除最后一行的下边框
          :deep(.markdown-content table tr:last-child th),
          :deep(.markdown-content table tr:last-child td),
          :deep(table tr:last-child th),
          :deep(table tr:last-child td) {
            border-bottom: none !important;
          }
          :deep(.markdown-content table th),
          :deep(table th) {
            font-weight: 600 !important;
            background-color: #f9fafb !important;
            color: #374151 !important;
          }
          
          :deep(.markdown-content table td),
          :deep(table td) {
            color: #1f2937 !important;
            background-color: #ffffff !important;
          }
          
          // 为表格添加容器包装，支持水平滚动
          :deep(.table-wrapper) {
            overflow-x: auto !important;
            margin: 16px 0 !important;
            background-color: transparent !important;
            
            table {
              margin: 0 !important;
            }
          }
          
          // 分割线
          :deep(hr) {
            height: 0.25em;
            padding: 0;
            margin: 24px 0;
            background-color: #e1e4e8;
            border: 0;
          }
          
          // 链接
          :deep(a) {
            color: #0366d6;
            text-decoration: none;
            
            &:hover {
              text-decoration: underline;
            }
          }
          
          // 强调
          :deep(strong) {
            font-weight: 600;
          }
          
          :deep(em) {
            font-style: italic;
          }
          
          // 删除线
          :deep(del) {
            text-decoration: line-through;
          }
        }
      }
      
      // 知识片段显示样式 - 来自SourceDisplay组件
      .source-display {
        .source-button-container {
          margin-top: 16px;
          display: flex;
          justify-content: flex-start;
          
          .source-button {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            background: #f8f9fa;
            border: 1px solid #e8e8e8;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            font-size: 13px;
            color: #666;
            will-change: transform, box-shadow;
            
            &:hover {
              background: #e8f4ff;
              border-color: #1890ff;
              color: #1890ff;
              transform: translateY(-1px) scale(1.01);
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
            }
            
            &:active {
              transform: translateY(0) scale(0.99);
              box-shadow: 0 1px 4px rgba(24, 144, 255, 0.15);
              transition: all 0.12s ease-out;
            }
            
            &:focus {
              outline: none;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
              transition: box-shadow 0.15s ease;
            }
            
            .anticon {
              font-size: 14px;
            }
            
            span {
              font-weight: 500;
            }
          }
        }
      }
    }
    
    .message-actions {
      position: absolute;
      bottom: -35px;
      right: 0;
      display: flex;
      gap: 4px;
      opacity: 0;
      transition: opacity 0.2s;
      z-index: 10;
      
      .action-btn {
        background: white;
        border: 1px solid #d9d9d9;
        border-radius: 4px;
        padding: 4px 6px;
        cursor: pointer;
        color: #666;
        transition: all 0.2s;
        font-size: 12px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        
        &:hover {
          background: #f0f0f0;
          border-color: #1890ff;
          color: #1890ff;
        }
      }
    }
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 8px rgba(79, 172, 254, 0.8);
  }
  50% {
    transform: scale(1.3);
    box-shadow: 0 0 20px rgba(79, 172, 254, 1), 0 0 40px rgba(0, 242, 254, 0.6);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 暗黑模式样式 */
[data-theme='dark'] .chat-message {
  &.user {
    .message-content {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      // 用户消息保持原有的渐变色，在暗黑模式下也很好看

      .message-header {
        .message-sender {
          color: rgba(255, 255, 255, 0.9);
        }

        .message-time {
          color: rgba(255, 255, 255, 0.7);
        }
      }

      .user-text {
        color: white;

        .user-images {
          .user-image-item {
            .image-name {
              color: rgba(255, 255, 255, 0.8);
            }
          }
        }

        .user-files {
          .user-file-item {
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.25);

            &:hover {
              background: rgba(255, 255, 255, 0.25);
              border-color: rgba(255, 255, 255, 0.35);
            }

            .file-icon {
              color: rgba(255, 255, 255, 0.9);
            }

            .file-name {
              color: rgba(255, 255, 255, 0.9);
            }
          }
        }
      }
    }
  }

  &.assistant {
    .message-content {
      background: linear-gradient(135deg, #262626 0%, #303030 100%);
      color: rgba(255, 255, 255, 0.85);
      border-color: rgba(255, 255, 255, 0.1);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

      .message-header {
        .message-sender {
          color: rgba(255, 255, 255, 0.85);
        }

        .message-time {
          color: rgba(255, 255, 255, 0.45);
        }
      }

      .ai-text {
        color: rgba(255, 255, 255, 0.85);
      }
    }
  }

  .message-avatar {
    .user-avatar {
      // 用户头像保持原有的渐变色
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      box-shadow: 0 3px 12px rgba(102, 126, 234, 0.35);
    }

    .ai-avatar {
      background: linear-gradient(135deg, rgba(79, 172, 254, 0.15) 0%, rgba(0, 242, 254, 0.15) 100%);
      border-color: rgba(79, 172, 254, 0.3);
      box-shadow: 0 2px 8px rgba(79, 172, 254, 0.25);

      .ai-head {
        .ai-brain {
          background: rgba(255, 255, 255, 0.2);
        }
      }
    }
  }

  .message-actions {
    .action-btn {
      background: #262626;
      border-color: #404040;
      color: rgba(255, 255, 255, 0.65);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

      &:hover {
        background: #303030;
        border-color: #1890ff;
        color: #1890ff;
      }
    }
  }
}
</style>
