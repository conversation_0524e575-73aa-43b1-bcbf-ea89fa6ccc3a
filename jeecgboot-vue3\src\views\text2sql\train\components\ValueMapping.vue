<template>
  <div class="value-mapping-container">
    <!-- 页面标题和说明 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h2>
            <TagsOutlined class="title-icon" />
            {{ t('text2sql.train.valueMapping.title') }}
          </h2>
          <p class="description">
            {{ t('text2sql.train.valueMapping.description') }}
          </p>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-wrapper">
      <!-- 顶部选择器区域 -->
      <div class="selector-section">
        <a-card :bordered="false" class="selector-card">
          <div class="selector-row">
            <!-- 数据库连接选择 -->
            <div class="selector-item">
              <label class="selector-label">{{ t('text2sql.train.valueMapping.connection') }}</label>
              <a-select
                v-model:value="selectedConnection"
                :placeholder="t('text2sql.train.valueMapping.connectionPlaceholder')"
                style="width: 100%;"
                :loading="connectionsLoading"
                @change="handleConnectionChange"
                show-search
                :filter-option="filterConnection"
              >
                <a-select-option
                  v-for="conn in connections"
                  :key="conn.id"
                  :value="conn.id"
                >
                  {{ conn.name }}
                </a-select-option>
              </a-select>
            </div>

            <!-- 表选择 -->
            <div class="selector-item">
              <label class="selector-label">{{ t('text2sql.train.valueMapping.table') }}</label>
              <a-select
                v-model:value="selectedTable"
                :placeholder="t('text2sql.train.valueMapping.tablePlaceholder')"
                style="width: 100%;"
                :loading="tablesLoading"
                @change="handleTableChange"
                :disabled="!selectedConnection"
                show-search
                :filter-option="filterTable"
                :not-found-content="tablesLoading ? t('text2sql.train.valueMapping.loading') : t('text2sql.train.valueMapping.noData')"
              >
                <a-select-option
                  v-for="table in tables"
                  :key="table.id"
                  :value="table.id"
                >
                  <div class="table-option">
                    <span class="table-name">{{ table.table_name }}</span>
                    <span class="table-comment" v-if="table.description">{{ table.description }}</span>
                  </div>
                </a-select-option>
              </a-select>
            </div>

            <!-- 列选择 -->
            <div class="selector-item">
              <label class="selector-label">{{ t('text2sql.train.valueMapping.column') }}</label>
              <a-select
                v-model:value="selectedColumn"
                :placeholder="t('text2sql.train.valueMapping.columnPlaceholder')"
                style="width: 100%;"
                :loading="columnsLoading"
                @change="handleColumnChange"
                :disabled="!selectedTable"
                show-search
                :filter-option="filterColumn"
                :not-found-content="!selectedTable ? t('text2sql.train.valueMapping.selectTableFirst') : t('text2sql.train.valueMapping.noData')"
              >
                <a-select-option
                  v-for="column in columns"
                  :key="column.id"
                  :value="column.id"
                >
                  <div class="column-option">
                    <span class="column-name">{{ column.column_name }}</span>
                    <span class="column-type">{{ column.data_type }}</span>
                    <span v-if="column.is_primary_key" class="key-badge pk">PK</span>
                    <span v-if="column.is_foreign_key" class="key-badge fk">FK</span>
                  </div>
                </a-select-option>
              </a-select>
            </div>

            <!-- 刷新按钮 -->
            <div class="selector-item">
              <a-button
                type="primary"
                :icon="h(ReloadOutlined)"
                @click="refreshConnections"
                :loading="connectionsLoading"
              >
                {{ t('text2sql.train.valueMapping.refresh') }}
              </a-button>
            </div>
          </div>
        </a-card>
      </div>

      <!-- 内容区域 -->
      <div class="main-content" v-if="selectedColumn">
        <!-- 左侧：添加值映射 -->
        <div class="left-content">
          <!-- 添加值映射 -->
          <a-card :title="t('text2sql.train.valueMapping.addMapping')" :bordered="false" class="add-mapping-card">
            <!-- 字段信息行 -->
            <div class="field-info-row">
              <a-tooltip placement="top">
                <template #title>
                                    <div class="field-tooltip">
                    <div><strong>{{ t('text2sql.train.valueMapping.tableName') }}：</strong>{{ selectedTableInfo?.table_name }}</div>
                    <div><strong>{{ t('text2sql.train.valueMapping.columnName') }}：</strong>{{ selectedColumnInfo?.column_name }}</div>
                    <div><strong>{{ t('text2sql.train.valueMapping.dataType') }}：</strong>{{ selectedColumnInfo?.data_type }}</div>
                    
                    <div><strong>{{ t('text2sql.train.valueMapping.existingMappings') }}：</strong>{{ mappings.length }} {{ t('text2sql.train.valueMapping.mappingCount') }}</div>
                  </div>
                </template>
                <div class="field-info-compact">
                  <InfoCircleOutlined class="info-icon" />
                  <span class="field-name">{{ selectedColumnInfo?.column_name }}</span>
                  <a-tag size="small" color="blue">{{ selectedColumnInfo?.data_type }}</a-tag>
                </div>
              </a-tooltip>
            </div>

            <!-- 数据字典选择区域 -->
            <div class="dict-selection-row">
              <div class="dict-selector">
                <label class="dict-label">
                  {{ t('text2sql.train.valueMapping.systemDict') }} ({{ sysDictList.length }}{{ t('text2sql.train.valueMapping.items') }})
                  <span v-if="dictLoading" style="color: #1890ff;"> - {{ t('text2sql.train.valueMapping.loading') }}</span>
                  <span v-else-if="sysDictList.length === 0" style="color: #ff4d4f;"> - {{ t('text2sql.train.valueMapping.noData') }}</span>
                </label>
                <a-select
                  v-model:value="selectedDict"
                  :placeholder="t('text2sql.train.valueMapping.dictPlaceholder')"
                  style="width: 100%;"
                  :loading="dictLoading"
                  @change="handleDictChange"
                  show-search
                  :filter-option="filterDict"
                  allow-clear
                  :not-found-content="dictLoading ? t('text2sql.train.valueMapping.loading') : t('text2sql.train.valueMapping.noData')"
                >
                  <a-select-option
                    v-for="dict in sysDictList"
                    :key="dict.id || dict.dictCode"
                    :value="dict.dictCode"
                  >
                    <div class="dict-option">
                      <span class="dict-name">{{ dict.dictName }}</span>
                      <span class="dict-code">{{ dict.dictCode }}</span>
                    </div>
                  </a-select-option>
                </a-select>
              </div>
              <div class="dict-actions">
                <a-button
                  type="default"
                  :icon="h(ReloadOutlined)"
                  @click="loadSysDictList"
                  :loading="dictLoading"
                  size="small"
                >
                  {{ t('text2sql.train.valueMapping.refreshDict') }}
                </a-button>
              </div>
            </div>

            <!-- 批量导入按钮区域 -->
            <div class="batch-import-row" v-if="selectedDict && dictItems.length > 0">
              <a-button
                type="primary"
                ghost
                :loading="addingMapping"
                @click="handleBatchAddFromDict"
                block
              >
                <TagsOutlined />
                {{ t('text2sql.train.valueMapping.batchImport') }} ({{ dictItems.length }}{{ t('text2sql.train.valueMapping.items') }})
              </a-button>
            </div>
            
            <a-form
              :model="newMapping"
              layout="vertical"
              @finish="handleAddMapping"
              ref="addFormRef"
            >
              <a-form-item 
                :label="t('text2sql.train.valueMapping.nlTerm')" 
                name="nl_term"
                :rules="[{ required: true, message: t('text2sql.train.valueMapping.nlTermRequired') }]"
              >
                <a-input
                  v-model:value="newMapping.nl_term"
                  :placeholder="t('text2sql.train.valueMapping.nlTermPlaceholder')"
                />
              </a-form-item>
              <a-form-item 
                :label="t('text2sql.train.valueMapping.dbValue')" 
                name="db_value"
                :rules="[{ required: true, message: t('text2sql.train.valueMapping.dbValueRequired') }]"
              >
                <a-input
                  v-model:value="newMapping.db_value"
                  :placeholder="t('text2sql.train.valueMapping.dbValuePlaceholder')"
                />
              </a-form-item>

              <a-form-item>
                  <a-button
                    type="primary"
                    html-type="submit"
                    :loading="addingMapping"
                  >
                    <PlusOutlined />
                    {{ t('text2sql.train.valueMapping.addMapping') }}
                  </a-button>
                  <a-button @click="resetForm">
                    {{ t('text2sql.train.valueMapping.reset') }}
                  </a-button>
              </a-form-item>
            </a-form>
          </a-card>
        </div>

        <!-- 右侧：映射管理 -->
        <div class="right-content">
          <a-card :title="t('text2sql.train.valueMapping.title')" :bordered="false" class="mappings-card">
            <!-- 工具栏 -->
            <template #extra>
              <a-space>
                <a-input-search
                  v-model:value="searchKeyword"
                  :placeholder="t('text2sql.train.valueMapping.searchMappings')"
                  style="width: 200px;"
                  @search="handleSearch"
                  allow-clear
                />
<!--                <a-button -->
<!--                  type="primary" -->
<!--                  ghost -->
<!--                  :icon="h(ExportOutlined)"-->
<!--                  @click="handleExport"-->
<!--                  :disabled="mappings.length === 0"-->
<!--                >-->
<!--                  导出-->
<!--                </a-button>-->
              </a-space>
            </template>

            <!-- 映射列表 -->
            <div v-if="mappingsLoading" class="loading-state">
              <a-spin size="large" />
              <p>{{ t('text2sql.train.valueMapping.loadingMappings') }}</p>
            </div>
            
            <div v-else-if="filteredMappings.length === 0" class="empty-mappings">
              <TagsOutlined style="font-size: 24px; color: #d9d9d9; margin-bottom: 8px;" />
              <p v-if="searchKeyword">{{ t('text2sql.train.valueMapping.empty') }}</p>
              <p v-else>{{ t('text2sql.train.valueMapping.noMappingsForField') }}</p>
              <p class="sub-text">{{ t('text2sql.train.valueMapping.firstMappingTip') }}</p>
            </div>
            
            <div v-else class="mappings-table">
              <a-table
                :columns="mappingColumns"
                :data-source="paginatedMappings"
                :pagination="paginationConfig"
                :loading="mappingsLoading"
                row-key="id"
                size="middle"
                :scroll="{ y: 'calc(100vh - 480px)' }"
                table-layout="fixed"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column && column.key === 'nl_term'">
                    <a-tag color="blue">{{ record.nl_term }}</a-tag>
                  </template>
                  <template v-else-if="column && column.key === 'db_value'">
                    <code class="db-value-code">{{ record.db_value }}</code>
                  </template>

                  <template v-else-if="column && column.key === 'actions'">
                    <a-space>
                      <a-button
                        type="link"
                        size="small"
                        :icon="h(EditOutlined)"
                        @click="handleEditMapping(record)"
                      >
                        {{ t('text2sql.train.valueMapping.editMapping') }}
                      </a-button>
                      <a-popconfirm
                        :title="t('text2sql.train.valueMapping.deleteConfirm')"
                        @confirm="handleDeleteMapping(record.id)"
                      >
                        <a-button
                          type="link"
                          size="small"
                          danger
                          :icon="h(DeleteOutlined)"
                        >
                          {{ t('text2sql.train.valueMapping.deleteMapping') }}
                        </a-button>
                      </a-popconfirm>
                    </a-space>
                  </template>
                </template>
              </a-table>
            </div>
          </a-card>
        </div>
      </div>

      <!-- 未选择字段时的提示 -->
      <div v-else class="no-selection">
        <div class="selection-guide">
          <div class="guide-icon">
            <TagsOutlined />
          </div>
          <div class="guide-content">
            <h3 class="guide-title">{{ t('text2sql.train.valueMapping.startCreateMapping') }}</h3>
            <p class="guide-description">{{ t('text2sql.train.valueMapping.guideDescription') }}</p>
            <div class="guide-steps">
              <div class="step-item">
                <span class="step-number">1</span>
                <span class="step-text">{{ t('text2sql.train.valueMapping.stepSelectConnection') }}</span>
              </div>
              <div class="step-item">
                <span class="step-number">2</span>
                <span class="step-text">{{ t('text2sql.train.valueMapping.stepSelectTable') }}</span>
              </div>
              <div class="step-item">
                <span class="step-number">3</span>
                <span class="step-text">{{ t('text2sql.train.valueMapping.stepSelectColumn') }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑映射模态框 -->
    <a-modal
      v-model:open="editModalVisible"
      :title="t('text2sql.train.valueMapping.editMapping')"
      @ok="handleUpdateMapping"
      @cancel="handleCancelEdit"
      :confirm-loading="updatingMapping"
      width="600px"
    >
      <a-form
        :model="editingMapping"
        layout="vertical"
        ref="editFormRef"
      >
        <a-form-item 
          :label="t('text2sql.train.valueMapping.nlTerm')" 
          name="nl_term"
          :rules="[{ required: true, message: t('text2sql.train.valueMapping.nlTermRequired') }]"
        >
          <a-input
            v-model:value="editingMapping.nl_term"
            :placeholder="t('text2sql.train.valueMapping.nlTermPlaceholder')"
          />
        </a-form-item>
        <a-form-item 
          :label="t('text2sql.train.valueMapping.dbValue')" 
          name="db_value"
          :rules="[{ required: true, message: t('text2sql.train.valueMapping.dbValueRequired') }]"
        >
          <a-input
            v-model:value="editingMapping.db_value"
            :placeholder="t('text2sql.train.valueMapping.dbValuePlaceholder')"
          />
        </a-form-item>

      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, watch, onMounted, h, reactive } from 'vue';
import { message } from 'ant-design-vue';
import type { FormInstance, TableColumnProps } from 'ant-design-vue';
import {
  TagsOutlined,
  ReloadOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  ExportOutlined
} from '@ant-design/icons-vue';
import { useI18n } from '/@/hooks/web/useI18n';

// Import APIs
import { connectionsApi, schemaApi, valueMappingsApi } from '@/api/text2sql/sys';
import type {
  DBConnection,
  SchemaTableWithRelationships,
  SchemaColumn,
  ValueMapping,
  ValueMappingCreate,
  ValueMappingUpdate
} from '@/api/text2sql/types';

// Import system dictionary APIs
import { getSysDictList, getDictItems } from '@/api/common/api';

// Reactive data
const selectedConnection = ref<string | undefined>();
const selectedTable = ref<number | undefined>();
const selectedColumn = ref<number | undefined>();

const connections = ref<DBConnection[]>([]);
const tables = ref<SchemaTableWithRelationships[]>([]);
const columns = ref<SchemaColumn[]>([]);
const mappings = ref<ValueMapping[]>([]);

// Dictionary related
const { t } = useI18n();

const sysDictList = ref<any[]>([]);
const selectedDict = ref<string | undefined>();
const dictItems = ref<any[]>([]);
const dictLoading = ref(false);
const loadingDictItems = ref(false);

// 搜索和分页
const searchKeyword = ref('');
const currentPage = ref(1);
const pageSize = ref(5);

// 加载状态
const connectionsLoading = ref(false);
const tablesLoading = ref(false);
const columnsLoading = ref(false);
const mappingsLoading = ref(false);
const addingMapping = ref(false);
const updatingMapping = ref(false);

// 表单引用
const addFormRef = ref<FormInstance>();
const editFormRef = ref<FormInstance>();

// 新建映射表单
const newMapping = ref<ValueMappingCreate>({
  column_id: 0,
  nl_term: '',
  db_value: ''
});

// 编辑映射
const editModalVisible = ref(false);
const editingMapping = ref<ValueMappingUpdate & { id: number }>({
  id: 0,
  nl_term: '',
  db_value: ''
});

// 表格列配置
const mappingColumns = computed<TableColumnProps[]>(() => [
  {
    title: t('text2sql.train.valueMapping.nlTerm'),
    dataIndex: 'nl_term',
    key: 'nl_term',
    width: '40%',
  },
  {
    title: t('text2sql.train.valueMapping.dbValue'),
    dataIndex: 'db_value',
    key: 'db_value',
    width: '40%',
  },
  {
    title: t('text2sql.train.valueMapping.actions'),
    key: 'actions',
    width: '20%',
  }
]);

// 计算属性
const selectedTableInfo = computed(() => {
  if (!selectedTable.value) return null;
  return tables.value.find(table => table.id === selectedTable.value);
});

const selectedColumnInfo = computed(() => {
  if (!selectedColumn.value) return null;
  return columns.value.find(col => col.id === selectedColumn.value);
});

const filteredMappings = computed(() => {
  if (!searchKeyword.value) return mappings.value;
  const keyword = searchKeyword.value.toLowerCase();
  return mappings.value.filter(mapping => 
    mapping.nl_term.toLowerCase().includes(keyword) ||
    mapping.db_value.toLowerCase().includes(keyword)
  );
});

const paginatedMappings = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredMappings.value.slice(start, end);
});

const paginationConfig = computed(() => ({
  current: currentPage.value,
  pageSize: pageSize.value,
  total: filteredMappings.value.length,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number, range: [number, number]) => 
    `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
  size: 'small',
  pageSizeOptions: ['5', '10', '20', '50'],
  onChange: (page: number, size: number) => {
    currentPage.value = page;
    pageSize.value = size;
  },
}));

// 过滤函数
const filterConnection = (input: string, option: any) => {
  return option.children.toLowerCase().includes(input.toLowerCase());
};

const filterTable = (input: string, option: any) => {
  const table = tables.value.find(t => t.id === option.value);
  if (!table) return false;
  return table.table_name.toLowerCase().includes(input.toLowerCase()) ||
         (table.description && table.description.toLowerCase().includes(input.toLowerCase()));
};

const filterColumn = (input: string, option: any) => {
  const column = columns.value.find(c => c.id === option.value);
  if (!column) return false;
  return column.column_name.toLowerCase().includes(input.toLowerCase()) ||
         column.data_type.toLowerCase().includes(input.toLowerCase());
};

const filterDict = (input: string, option: any) => {
  const dict = sysDictList.value.find(d => d.dictCode === option.value);
  if (!dict) return false;
  return dict.dictName.toLowerCase().includes(input.toLowerCase()) ||
         dict.dictCode.toLowerCase().includes(input.toLowerCase());
};

// 方法
const refreshConnections = async () => {
  connectionsLoading.value = true;
  try {
    connections.value = await connectionsApi.getConnections();
  } catch (error) {
    message.error(t('text2sql.train.valueMapping.loadConnectionsFailed'));
    console.error('Error loading connections:', error);
  } finally {
    connectionsLoading.value = false;
  }
};

const handleConnectionChange = async (connectionId: string) => {
  selectedTable.value = undefined;
  selectedColumn.value = undefined;
  tables.value = [];
  columns.value = [];
  mappings.value = [];
  
  if (!connectionId) return;

  tablesLoading.value = true;
  try {
    const allTables = await schemaApi.getSchemaMetadata(connectionId);
    // Filter out tables where ui_metadata.is_on_canvas is false
    tables.value = allTables.filter(table => {
              // If no ui_metadata, keep the table
      if (!table.ui_metadata) {
        return true;
      }
      
      try {
        // Parse ui_metadata (if it's a string)
        const uiMetadata = typeof table.ui_metadata === 'string' 
          ? JSON.parse(table.ui_metadata) 
          : table.ui_metadata;
        
        // If ui_metadata exists and is_on_canvas is false, filter it out
        return uiMetadata.is_on_canvas !== false;
      } catch (error) {
              // If parsing ui_metadata fails, keep the table
      console.warn('Failed to parse ui_metadata for table:', table.table_name, error);
        return true;
      }
    });
  } catch (error) {
    message.error(t('text2sql.train.valueMapping.loadTablesFailed'));
    console.error('Error loading tables:', error);
  } finally {
    tablesLoading.value = false;
  }
};

const handleTableChange = (tableId: number) => {
  selectedColumn.value = undefined;
  columns.value = [];
  mappings.value = [];
  
  if (!tableId) return;

  const table = tables.value.find(t => t.id === tableId);
  if (table) {
    columns.value = table.columns || [];
  }
};

const handleColumnChange = async (columnId: number) => {
  mappings.value = [];
  currentPage.value = 1;
  searchKeyword.value = '';
  
  if (!columnId) return;

  await loadMappingsForColumn(columnId);
};

const loadMappingsForColumn = async (columnId: number) => {
  mappingsLoading.value = true;
  try {
    mappings.value = await valueMappingsApi.getValueMappings({ column_id: columnId });
  } catch (error) {
    message.error(t('text2sql.train.valueMapping.loadMappingsFailed'));
    console.error('Error loading mappings:', error);
  } finally {
    mappingsLoading.value = false;
  }
};

const resetForm = () => {
  newMapping.value = {
    column_id: 0,
    nl_term: '',
    db_value: ''
  };
  addFormRef.value?.resetFields();
};

const handleAddMapping = async () => {
  if (!selectedColumn.value) {
    message.error(t('text2sql.train.valueMapping.selectFieldFirst'));
    return;
  }

  addingMapping.value = true;
  try {
    const mappingData: ValueMappingCreate = {
      column_id: selectedColumn.value,
      nl_term: newMapping.value.nl_term,
      db_value: newMapping.value.db_value
    };

    await valueMappingsApi.createValueMapping(mappingData);
    message.success(t('text2sql.train.valueMapping.addMappingSuccess'));
    
    // Reset form
    resetForm();
    
    // Reload mapping list
    await loadMappingsForColumn(selectedColumn.value);
  } catch (error: any) {
    if (error.response?.status === 400) {
      message.error(t('text2sql.train.valueMapping.termAlreadyExists'));
    } else {
      message.error(t('text2sql.train.valueMapping.addMappingFailed'));
    }
    console.error('Error creating mapping:', error);
  } finally {
    addingMapping.value = false;
  }
};

const handleEditMapping = (mapping: ValueMapping) => {
  editingMapping.value = {
    id: mapping.id,
    nl_term: mapping.nl_term,
    db_value: mapping.db_value
  };
  editModalVisible.value = true;
};

const handleUpdateMapping = async () => {
  if (!editFormRef.value) return;
  
  try {
    await editFormRef.value.validate();
  } catch (error) {
    return;
  }

  updatingMapping.value = true;
  try {
    const updateData: ValueMappingUpdate = {
      nl_term: editingMapping.value.nl_term,
      db_value: editingMapping.value.db_value
    };

    await valueMappingsApi.updateValueMapping(editingMapping.value.id, updateData);
    message.success(t('text2sql.train.valueMapping.updateMappingSuccess'));
    
    editModalVisible.value = false;
    
    // Reload mapping list
    if (selectedColumn.value) {
      await loadMappingsForColumn(selectedColumn.value);
    }
  } catch (error: any) {
    if (error.response?.status === 400) {
      message.error(t('text2sql.train.valueMapping.termAlreadyExists'));
    } else {
      message.error(t('text2sql.train.valueMapping.updateMappingFailed'));
    }
    console.error('Error updating mapping:', error);
  } finally {
    updatingMapping.value = false;
  }
};

const handleCancelEdit = () => {
  editModalVisible.value = false;
  editingMapping.value = {
    id: 0,
    nl_term: '',
    db_value: ''
  };
  editFormRef.value?.resetFields();
};

const handleDeleteMapping = async (mappingId: number) => {
  try {
    await valueMappingsApi.deleteValueMapping(mappingId);
    message.success(t('text2sql.train.valueMapping.deleteMappingSuccess'));
    
    // Reload mapping list
    if (selectedColumn.value) {
      await loadMappingsForColumn(selectedColumn.value);
    }
  } catch (error) {
    message.error(t('text2sql.train.valueMapping.deleteMappingFailed'));
    console.error('Error deleting mapping:', error);
  }
};

const handleSearch = () => {
  currentPage.value = 1;
};

const handleExport = () => {
  // TODO: 实现导出功能
  message.info(t('text2sql.train.valueMapping.exportFunctionDeveloping'));
};

const loadSysDictList = async () => {
  dictLoading.value = true;
  try {
    console.log('Loading dictionary list...');
    const response = await getSysDictList({ pageSize: 1000 });
    // console.log('Dict API response:', response);
    
    // 检查响应结构
    if (!response) {
      throw new Error('API响应为空');
    }
    
    if (response.records) {
      console.log('response.records length:', response.records.length);
      if (response.records.length > 0) {
        console.log('First record:', response.records[0]);
      }
    }
    
    let dictList: any[] = [];
    if (response.records && Array.isArray(response.records)) {
      console.log('使用 response.records 路径');
      dictList = response.records;
    } else if (response.result && response.result.records) {
      console.log('使用 response.result.records 路径');
      dictList = response.result.records;
    } else if (response.result && Array.isArray(response.result)) {
      console.log('使用 response.result 路径');
      dictList = response.result;
    } else if (Array.isArray(response)) {
      console.log('使用 response 路径');
      dictList = response;
    } else {
      console.warn('无法解析API响应结构:', response);
      dictList = [];
    }

    // 验证数据结构
    if (dictList.length > 0) {
      console.log('First dict item:', dictList[0]);
    }
    
    sysDictList.value = dictList;
    // console.log('sysDictList.value after assignment:', sysDictList.value);
    // console.log('sysDictList.value length:', sysDictList.value.length);
    
    if (dictList.length > 0) {
      message.success(t('text2sql.train.valueMapping.loadDictListSuccess', { count: dictList.length }));
    } else {
      message.warning(t('text2sql.train.valueMapping.noDictFound'));
    }
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    message.error(t('text2sql.train.valueMapping.loadDictListFailed') + ': ' + errorMessage);
    console.error('Error loading dict list:', error);
  } finally {
    dictLoading.value = false;
    console.log('loadSysDictList completed, dictLoading set to false');
  }
};

const handleDictChange = async (dictCode: string) => {
  dictItems.value = [];
  if (!dictCode) return;

  loadingDictItems.value = true;
  try {
    const items = await getDictItems(dictCode);
    dictItems.value = items?.result || items || [];
  } catch (error) {
    message.error(t('text2sql.train.valueMapping.loadDictItemsFailed'));
    console.error('Error loading dict items:', error);
  } finally {
    loadingDictItems.value = false;
  }
};

const handleBatchAddFromDict = async () => {
  if (!selectedColumn.value || !dictItems.value.length) {
    message.error(t('text2sql.train.valueMapping.selectFieldAndDict'));
    return;
  }

  addingMapping.value = true;
  try {
    let successCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    for (const item of dictItems.value) {
      try {
        const mappingData: ValueMappingCreate = {
          column_id: selectedColumn.value,
          nl_term: item.text, // Dictionary item display text as natural language term
          db_value: item.value // Dictionary item value as database value
        };

        await valueMappingsApi.createValueMapping(mappingData);
        successCount++;
      } catch (error: any) {
        errorCount++;
        if (error.response?.status === 400) {
          errors.push(t('text2sql.train.valueMapping.termAlreadyExistsInBatch', { term: item.text }));
        } else {
          errors.push(t('text2sql.train.valueMapping.termImportFailed', { term: item.text }));
        }
      }
    }

    if (successCount > 0) {
      if (errorCount > 0) {
        message.success(t('text2sql.train.valueMapping.batchImportPartialSuccess', { successCount, errorCount }));
      } else {
        message.success(t('text2sql.train.valueMapping.batchImportSuccess', { successCount }));
      }
    }
    
    if (errors.length > 0 && errors.length <= 5) {
      // If there are not many errors, show specific error messages
      errors.forEach(error => message.warning(error));
    } else if (errors.length > 5) {
      message.warning(t('text2sql.train.valueMapping.batchImportCheckDuplicate', { errorCount }));
    }
    
    // Reload mapping list
    await loadMappingsForColumn(selectedColumn.value);
    
    // Clear dictionary selection
    selectedDict.value = undefined;
    dictItems.value = [];
  } catch (error) {
    message.error(t('text2sql.train.valueMapping.batchImportFailed'));
    console.error('Error batch creating mappings:', error);
  } finally {
    addingMapping.value = false;
  }
};

// 生命周期
onMounted(() => {
  refreshConnections();
  loadSysDictList();
});
</script>

<style scoped>
/* 暗黑模式适配 */
[data-theme='dark'] .value-mapping-container {
  background: #141414;
}

[data-theme='dark'] .page-header {
  background: #1f1f1f;
  border-bottom-color: #303030;
}

[data-theme='dark'] .title-section h2 {
  color: #ffffff;
}

[data-theme='dark'] .description {
  color: #a6a6a6;
}

[data-theme='dark'] .selector-card {
  background: #1f1f1f;
  border-color: #303030;
}

[data-theme='dark'] .selector-label {
  color: #ffffff;
}

[data-theme='dark'] .table-name,
[data-theme='dark'] .column-name {
  color: #ffffff;
}

[data-theme='dark'] .table-comment {
  color: #8c8c8c;
}

[data-theme='dark'] .column-type {
  background: #262626;
  color: #a6a6a6;
}

[data-theme='dark'] .add-mapping-card,
[data-theme='dark'] .mappings-card {
  background: #1f1f1f;
  border-color: #303030;
}

[data-theme='dark'] .field-info-compact {
  background: #262626;
  border-color: #303030;
  color: #ffffff;
}

[data-theme='dark'] .field-info-compact:hover {
  background: #1f3a8a;
  border-color: #3b82f6;
}

[data-theme='dark'] .field-name {
  color: #ffffff;
}

[data-theme='dark'] .dict-label {
  color: #ffffff;
}

[data-theme='dark'] .dict-name {
  color: #ffffff;
}

[data-theme='dark'] .dict-code {
  color: #8c8c8c;
}

[data-theme='dark'] .db-value-code {
  background: #262626;
  color: #a6a6a6;
}

[data-theme='dark'] .guide-title {
  color: #ffffff;
}

[data-theme='dark'] .guide-description {
  color: #a6a6a6;
}

[data-theme='dark'] .step-item {
  background: #262626;
  border-color: #303030;
}

[data-theme='dark'] .step-item:hover {
  background: #1f3a8a;
  border-color: #3b82f6;
}

[data-theme='dark'] .step-text {
  color: #ffffff;
}

[data-theme='dark'] .loading-state,
[data-theme='dark'] .empty-mappings {
  color: #a6a6a6;
}

[data-theme='dark'] .sub-text {
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
    height: auto;
  }
  
  .left-content {
    width: 100%;
  }
  
  .selector-row {
    flex-wrap: wrap;
  }
  
  .selector-item {
    min-width: 160px;
  }
  
  .field-info-compact {
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .field-name {
    font-size: 12px;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 16px;
  }
  
  .content-wrapper {
    padding: 0 16px 16px;
  }
  
  .selector-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .selector-item {
    min-width: 100%;
  }
  
  .title-section h2 {
    font-size: 20px;
  }
  
  .field-info-compact {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .dict-selection-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .dict-actions {
    flex: 1;
  }
  
  .batch-import-row {
    margin-bottom: 12px;
    padding-bottom: 12px;
  }
  
  .selection-guide {
    max-width: 100%;
    padding: 0 16px;
  }
  
  .guide-title {
    font-size: 20px;
  }
  
  .guide-description {
    font-size: 14px;
  }
  
  .guide-icon .anticon {
    font-size: 48px;
  }
  
  .step-item {
    min-width: 100%;
    padding: 12px 16px;
  }
  
  .step-text {
    font-size: 13px;
  }
}

.value-mapping-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  overflow: hidden;
}

.page-header {
  background: white;
  padding: 20px 24px;
  border-bottom: 1px solid #e8e8e8;
  flex-shrink: 0;
}

.header-content {
  max-width: none;
  margin: 0;
}

.title-section h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-icon {
  color: #1890ff;
  font-size: 28px;
}

.description {
  margin: 8px 0 0 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.content-wrapper {
  flex: 1;
  padding: 16px 24px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.selector-section {
  margin-bottom: 16px;
  flex-shrink: 0;
}

.selector-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.selector-row {
  display: flex;
  gap: 16px;
  align-items: end;
}

.selector-item {
  flex: 1;
  min-width: 200px;
}

.selector-item:last-child {
  flex: 0 0 auto;
}

.selector-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #1a1a1a;
}

.table-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.table-name {
  font-weight: 500;
  color: #1a1a1a;
}

.table-comment {
  font-size: 12px;
  color: #999;
}

.column-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.column-name {
  font-weight: 500;
  color: #1a1a1a;
}

.column-type {
  font-size: 12px;
  color: #666;
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
}

.key-badge {
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
  font-weight: 600;
  color: white;
}

.key-badge.pk {
  background: #1890ff;
}

.key-badge.fk {
  background: #52c41a;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 16px;
  overflow: hidden;
  min-height: 0;
}

.left-content {
  width: 380px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow: hidden;
  flex-shrink: 0;
}

.right-content {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.add-mapping-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  flex: 1;
  min-height: 0;
}

.add-mapping-card :deep(.ant-card-body) {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
}

.field-info-row {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.field-info-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  cursor: help;
  transition: all 0.2s;
  border: 1px solid #e8e8e8;
}

.field-info-compact:hover {
  background: #e6f4ff;
  border-color: #91caff;
}

.info-icon {
  color: #1890ff;
  font-size: 14px;
}

.field-name {
  font-family: monospace;
  font-size: 13px;
  color: #1a1a1a;
  font-weight: 500;
}

.add-mapping-card :deep(.ant-form) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.add-mapping-card :deep(.ant-form-item) {
  margin-bottom: 16px;
}

.add-mapping-card :deep(.ant-form-item:last-child) {
  margin-bottom: 0;
}

.add-mapping-card :deep(.ant-form-item .ant-btn) {
  margin-right: 8px;
}

.add-mapping-card :deep(.ant-form-item .ant-btn:last-child) {
  margin-right: 0;
}

.field-tooltip {
  line-height: 1.6;
}

.field-tooltip > div {
  margin-bottom: 4px;
}

.field-tooltip > div:last-child {
  margin-bottom: 0;
}

.mappings-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  flex: 1;
  min-height: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.mappings-card :deep(.ant-card-body) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.no-selection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.selection-guide {
  text-align: center;
  max-width: 480px;
  width: 100%;
}

.guide-icon {
  margin-bottom: 24px;
}

.guide-icon .anticon {
  font-size: 64px;
  color: #1890ff;
  opacity: 0.8;
}

.guide-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
}

.guide-description {
  font-size: 16px;
  color: #666;
  margin: 0 0 32px 0;
  line-height: 1.5;
}

.guide-steps {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  transition: all 0.2s;
  min-width: 200px;
  justify-content: flex-start;
}

.step-item:hover {
  background: #e6f4ff;
  border-color: #91caff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.step-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.step-text {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
}

.loading-state,
.empty-mappings {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  color: #666;
}

.sub-text {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.mappings-table {
  flex: 1;
  overflow: hidden;
}

.mappings-table :deep(.ant-table-wrapper) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.mappings-table :deep(.ant-spin-nested-loading) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.mappings-table :deep(.ant-spin-container) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.mappings-table :deep(.ant-table) {
  flex: 1;
  overflow: hidden;
}

.mappings-table :deep(.ant-table-container) {
  height: 100%;
  overflow: auto;
}

.db-value-code {
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: monospace;
  font-size: 13px;
}



.dict-selection-row {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  gap: 12px;
  align-items: end;
}

.dict-selector {
  flex: 1;
}

.dict-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #1a1a1a;
  font-size: 14px;
}

.dict-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.dict-name {
  font-weight: 500;
  color: #1a1a1a;
  font-size: 14px;
}

.dict-code {
  font-size: 12px;
  color: #999;
  font-family: monospace;
}

.dict-actions {
  flex: 0 0 auto;
}

.batch-import-row {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.batch-import-row .ant-btn {
  height: 40px;
  font-size: 14px;
  font-weight: 500;
}

.add-mapping-card :deep(.ant-form) {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style> 
