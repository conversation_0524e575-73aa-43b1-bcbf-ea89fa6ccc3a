<template>
  <Card :title="t('dashboard.chatStats.title')" :loading="loading">
    <div class="chat-stats-grid">
      <div class="stats-row">
        <div class="stat-card">
          <div class="stat-icon">
            <Icon icon="ant-design:message-outlined" size="24" color="#1890ff" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statsData.total_conversations }}</div>
            <div class="stat-label">{{ t('dashboard.chatStats.totalConversations') }}</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <Icon icon="ant-design:user-outlined" size="24" color="#52c41a" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statsData.active_users }}</div>
            <div class="stat-label">{{ t('dashboard.chatStats.activeUsers') }}</div>
          </div>
        </div>
      </div>
      <div class="stats-row">
        <div class="stat-card">
          <div class="stat-icon">
            <Icon icon="ant-design:clock-circle-outlined" size="24" color="#fa8c16" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statsData.avg_session_duration }}{{ t('dashboard.chatStats.minutes') }}</div>
            <div class="stat-label">{{ t('dashboard.chatStats.avgSessionDuration') }}</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">
            <Icon icon="ant-design:like-outlined" size="24" color="#eb2f96" />
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ statsData.satisfaction_rate }}{{ t('dashboard.chatStats.percentage') }}</div>
            <div class="stat-label">{{ t('dashboard.chatStats.satisfactionRate') }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="chart-section">
      <div class="chart-title">{{ t('dashboard.chatStats.chartTitle') }}</div>
      <div ref="chartRef" :style="{ width: '100%', height: '180px' }"></div>
    </div>
  </Card>
</template>

<script lang="ts" setup>
import { Ref, ref, watch, onMounted } from 'vue';
import { Card } from 'ant-design-vue';
import { useECharts } from '/@/hooks/web/useECharts';
import { useI18n } from '/@/hooks/web/useI18n';
import Icon from '@/components/Icon';
import { statisticsService, type StatisticsOverview, type DailyTrend } from '@/api/statistics/knowledge';

const props = defineProps({
  loading: Boolean,
});

const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
const { t } = useI18n();

// 统计数据
const statsData = ref<StatisticsOverview>({
  total_conversations: 0,
  active_users: 0,
  avg_session_duration: 0,
  satisfaction_rate: 0
});

// 趋势数据
const weeklyTrendData = ref<DailyTrend[]>([]);

// 获取统计概览数据
const fetchStatisticsOverview = async () => {
  try {
    const data = await statisticsService.getOverview();
    statsData.value = data;
  } catch (error) {
    console.error(t('dashboard.errors.loadStatsFailed') + ':', error);
    // 如果接口失败，保持默认值0，避免显示错误
  }
};

// 获取最近7天趋势数据
const fetchWeeklyTrend = async () => {
  try {
    const data = await statisticsService.getWeeklyTrend();
    if (data && data.trends) {
      weeklyTrendData.value = data.trends;
      updateChart();
    }
  } catch (error) {
    console.error(t('dashboard.errors.loadTrendFailed') + ':', error);
    // 如果接口失败，使用空数据
    weeklyTrendData.value = [];
    updateChart();
  }
};

// 更新图表
const updateChart = () => {
  const days = weeklyTrendData.value.map(item => item.date);
  const conversations = weeklyTrendData.value.map(item => item.conversations);

  setOptions({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      top: '10px',
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: days.length > 0 ? days : [
        t('dashboard.chatStats.weekdays.monday'),
        t('dashboard.chatStats.weekdays.tuesday'),
        t('dashboard.chatStats.weekdays.wednesday'),
        t('dashboard.chatStats.weekdays.thursday'),
        t('dashboard.chatStats.weekdays.friday'),
        t('dashboard.chatStats.weekdays.saturday'),
        t('dashboard.chatStats.weekdays.sunday')
      ],
      axisLabel: {
        fontSize: 10
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        fontSize: 10
      }
    },
    series: [
      {
        name: t('dashboard.chatStats.conversationCount'),
        type: 'line',
        smooth: true,
        data: conversations.length > 0 ? conversations : [0, 0, 0, 0, 0, 0, 0],
        itemStyle: {
          color: '#1890ff'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(24, 144, 255, 0.3)'
            }, {
              offset: 1, color: 'rgba(24, 144, 255, 0.1)'
            }]
          }
        }
      }
    ]
  });
};

// 加载所有数据
const loadData = async () => {
  await Promise.all([
    fetchStatisticsOverview(),
    fetchWeeklyTrend()
  ]);
};

watch(
  () => props.loading,
  (newLoading) => {
    if (!newLoading) {
      // 当loading为false时，更新图表
      updateChart();
    }
  },
  { immediate: true }
);

onMounted(() => {
  loadData();
});
</script>

<style scoped>
.chat-stats-grid {
  margin-bottom: 20px;
}

.stats-row {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.stat-card {
  flex: 1;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: #f0f2f5;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 18px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 2px;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
}

.chart-section {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.chart-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 15px;
}

/* 暗黑模式样式 */
[data-theme='dark'] .stat-card {
  background: #1f1f1f;
  border: 1px solid #303030;
}

[data-theme='dark'] .stat-card:hover {
  background: #262626;
  box-shadow: 0 4px 12px rgba(255,255,255,0.1);
}

[data-theme='dark'] .stat-icon {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme='dark'] .stat-number {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .stat-label {
  color: rgba(255, 255, 255, 0.45);
}

[data-theme='dark'] .chart-section {
  border-top-color: #303030;
}

[data-theme='dark'] .chart-title {
  color: rgba(255, 255, 255, 0.85);
}
</style>
