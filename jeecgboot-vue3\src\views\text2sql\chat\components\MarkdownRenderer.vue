<template>
  <div class="markdown-container" :class="{ 'is-dark': theme === 'dark' }">
    <div class="markdown-content" v-html="renderedContent" ref="markdownContent"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { marked } from 'marked';
import DOMPurify from 'dompurify';
import hljs from 'highlight.js';
import 'highlight.js/styles/github.css';
// 可选导入暗色主题
// import 'highlight.js/styles/github-dark.css';

// 引入Prism以保持和EnhancedCodeHighlight组件相同的高亮风格
import Prism from 'prismjs';
import 'prismjs/components/prism-sql';
import 'prismjs/components/prism-javascript';
import 'prismjs/components/prism-typescript';
import 'prismjs/components/prism-python';
import 'prismjs/components/prism-bash';
import 'prismjs/components/prism-json';

const props = defineProps({
  // Markdown内容
  content: {
    type: String,
    required: true,
  },
  // 主题
  theme: {
    type: String,
    default: 'light',
    validator: (value: string) => ['light', 'dark'].includes(value),
  },
  // 是否将链接在新窗口打开
  externalLinks: {
    type: Boolean,
    default: true,
  },
  // 是否渲染表格
  tables: {
    type: Boolean,
    default: true,
  },
  // 是否启用代码高亮
  highlight: {
    type: Boolean,
    default: true,
  },
  // 是否自动链接
  breaks: {
    type: Boolean,
    default: false,
  },
  // 是否渲染GitHub风格的任务列表
  tasklists: {
    type: Boolean,
    default: true,
  },
});

const markdownContent = ref<HTMLElement | null>(null);

// 用于字符转义的工具函数
const escapeHTML = (html: string) => {
  return html
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
};

// 配置marked选项
const setupMarked = () => {
  // 设置高亮代码的函数
  marked.setOptions({
    highlight: props.highlight 
      ? (code, lang) => {
          // SQL语言特殊处理标记，但不直接使用Prism渲染
          // 我们将在后续的DOM处理中应用Prism
          if (lang === 'sql') {
            return code;
          }
          
          // 对其他语言使用hljs
          if (lang && hljs.getLanguage(lang)) {
            try {
              return hljs.highlight(code, { language: lang }).value;
            } catch (err) {
              console.error(err);
            }
          }
          return hljs.highlightAuto(code).value;
        }
      : undefined,
    gfm: true,
    breaks: props.breaks
  });

  // 支持任务列表
  if (props.tasklists) {
    marked.use({
      extensions: [{
        name: 'tasklist',
        level: 'block',
        start(src) {
          return src.match(/^\s*- \[ \]/)?.index;
        },
        tokenizer(src) {
          const rule = /^\s*- \[([ xX])\] (.*)/;
          const match = rule.exec(src);
          if (match) {
            return {
              type: 'html',
              raw: match[0],
              text: `<div class="task-list-item"><input type="checkbox" ${match[1] !== ' ' ? 'checked' : ''} disabled /> ${match[2]}</div>`,
            };
          }
          return undefined;
        }
      }]
    });
  }
};

// 渲染Markdown内容
const renderedContent = computed(() => {
  if (!props.content) return '';
  
  // 使用marked渲染Markdown
  const html = marked.parse(props.content) as string;
  
  // 配置DOMPurify，处理外部链接
  if (props.externalLinks) {
    DOMPurify.addHook('afterSanitizeAttributes', (node) => {
      if (node.tagName === 'A' && node.getAttribute('href')?.startsWith('http')) {
        node.setAttribute('target', '_blank');
        node.setAttribute('rel', 'noopener noreferrer');
      }
    });
  } else {
    // 移除钩子，以防止影响后续渲染
    DOMPurify.removeHook('afterSanitizeAttributes');
  }
  
  // 使用DOMPurify净化HTML，防止XSS攻击
  return DOMPurify.sanitize(html);
});

// 处理SQL代码块，将它们替换为Prism高亮的版本并添加特殊样式
const processSqlCodeBlocks = () => {
  if (!markdownContent.value) return;
  
  // 查找所有代码块
  const codeBlocks = markdownContent.value.querySelectorAll('pre code');
  
  codeBlocks.forEach(codeBlock => {
    // 检查是否是SQL代码块
    if (codeBlock.classList.contains('language-sql')) {
      const parentPre = codeBlock.parentElement;
      if (parentPre) {
        // 添加SQL代码块特殊类
        parentPre.classList.add('sql-code-block');
        
        // 获取代码内容并使用Prism高亮
        const code = codeBlock.textContent || '';
        try {
          codeBlock.innerHTML = Prism.highlight(code, Prism.languages.sql, 'sql');
        } catch (err) {
          console.error('SQL高亮处理错误:', err);
        }
      }
    }
  });
};

// 初始化设置
onMounted(() => {
  setupMarked();
});

// 监视内容变化
watch(() => renderedContent.value, () => {
  nextTick(() => {
    processSqlCodeBlocks();
  });
}, { immediate: true });

// 当配置变化时重新设置
watch(
  () => [
    props.highlight,
    props.externalLinks,
    props.breaks,
    props.tasklists,
  ],
  () => {
    setupMarked();
  }
);
</script>

<style scoped>
.markdown-container {
  width: 100%;
  color: #24292e;
  line-height: 1.6;
  word-wrap: break-word;
  padding: 16px;
  background-color: #fff;
  border-radius: 6px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
  font-size: 16px;
}

.markdown-container.is-dark {
  background-color: #0d1117;
  color: #c9d1d9;
}

.markdown-content {
  max-width: 100%;
}

/* 深度选择器用于CSS渗透 */
.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  margin-top: 24px;
  margin-bottom: 16px;
  font-weight: 600;
  line-height: 1.25;
}

.markdown-content :deep(h1) {
  font-size: 2em;
  padding-bottom: 0.3em;
  border-bottom: 1px solid #eaecef;
}

.is-dark .markdown-content :deep(h1) {
  border-bottom-color: #30363d;
}

.markdown-content :deep(h2) {
  font-size: 1.5em;
  padding-bottom: 0.3em;
  border-bottom: 1px solid #eaecef;
}

.is-dark .markdown-content :deep(h2) {
  border-bottom-color: #30363d;
}

.markdown-content :deep(h3) {
  font-size: 1.25em;
}

.markdown-content :deep(h4) {
  font-size: 1em;
}

.markdown-content :deep(p) {
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-content :deep(a) {
  color: #0366d6;
  text-decoration: none;
}

.is-dark .markdown-content :deep(a) {
  color: #58a6ff;
}

.markdown-content :deep(a:hover) {
  text-decoration: underline;
}

.markdown-content :deep(code) {
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
  padding: 0.2em 0.4em;
  margin: 0;
  font-size: 85%;
  background-color: rgba(27, 31, 35, 0.05);
  border-radius: 3px;
}

.is-dark .markdown-content :deep(code) {
  background-color: rgba(240, 246, 252, 0.15);
}

.markdown-content :deep(pre) {
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  overflow: auto;
  margin-bottom: 16px;
  word-wrap: normal;
}

.is-dark .markdown-content :deep(pre) {
  background-color: #161b22;
}

.markdown-content :deep(pre > code) {
  padding: 0;
  margin: 0;
  font-size: 100%;
  word-break: normal;
  white-space: pre;
  background: transparent;
  border: 0;
}

/* SQL代码块特殊样式 - 与EnhancedCodeHighlight组件样式保持一致 */
.markdown-content :deep(pre code.language-sql) {
  font-family: 'Consolas', monospace;
  font-size: 13px;
  line-height: 1.5;
  background-color: #fafafa !important;
  padding: 0;
  margin: 0;
  position: relative;
  display: block;
  overflow-x: auto;
}

/* 为SQL代码块的父级pre设置特殊样式 - 不使用:has选择器以提高兼容性 */
.markdown-content :deep(pre.sql-code-block) {
  background-color: #fafafa; 
  border: 1px solid #e0e0e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.is-dark .markdown-content :deep(pre.sql-code-block) {
  background-color: #0d1117;
  border-color: #30363d;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
}

/* SQL代码块中的token样式 - 与EnhancedCodeHighlight组件样式保持一致 */
.markdown-content :deep(pre code.language-sql .token) {
  text-shadow: none !important;
  font-weight: normal;
  background: transparent !important;
}

.markdown-content :deep(pre code.language-sql .token.keyword) {
  color: #1a73e8;
  font-weight: bold;
  text-shadow: none !important;
}

.markdown-content :deep(pre code.language-sql .token.function) {
  color: #c2410c;
  text-shadow: none !important;
}

.markdown-content :deep(pre code.language-sql .token.string) {
  color: #e45649;
  text-shadow: none !important;
}

.markdown-content :deep(pre code.language-sql .token.number) {
  color: #986801;
  text-shadow: none !important;
}

.markdown-content :deep(pre code.language-sql .token.operator) {
  color: #a626a4;
  text-shadow: none !important;
}

.markdown-content :deep(pre code.language-sql .token.punctuation) {
  color: #383a42;
  text-shadow: none !important;
}

.markdown-content :deep(pre code.language-sql .token.comment) {
  color: #a0a1a7;
  font-style: italic;
  text-shadow: none !important;
}

/* 调整代码块容器样式 */
.markdown-content :deep(div.code-highlight-container) {
  margin-bottom: 16px;
}

.markdown-content :deep(blockquote) {
  padding: 0 1em;
  color: #6a737d;
  border-left: 0.25em solid #dfe2e5;
  margin: 0 0 16px 0;
}

.is-dark .markdown-content :deep(blockquote) {
  color: #8b949e;
  border-left-color: #30363d;
}

.markdown-content :deep(ul),
.markdown-content :deep(ol) {
  padding-left: 2em;
  margin-top: 0;
  margin-bottom: 16px;
}

.markdown-content :deep(table) {
  display: block;
  width: 100%;
  overflow: auto;
  margin-top: 0;
  margin-bottom: 16px;
  border-spacing: 0;
  border-collapse: collapse;
}

.markdown-content :deep(table tr) {
  background-color: #fff;
  border-top: 1px solid #c6cbd1;
}

.is-dark .markdown-content :deep(table tr) {
  background-color: #0d1117;
  border-top-color: #30363d;
}

.markdown-content :deep(table tr:nth-child(2n)) {
  background-color: #f6f8fa;
}

.is-dark .markdown-content :deep(table tr:nth-child(2n)) {
  background-color: #161b22;
}

.markdown-content :deep(table th),
.markdown-content :deep(table td) {
  padding: 6px 13px;
  border: 1px solid #dfe2e5;
}

.is-dark .markdown-content :deep(table th),
.is-dark .markdown-content :deep(table td) {
  border-color: #30363d;
}

.markdown-content :deep(img) {
  max-width: 100%;
  box-sizing: content-box;
  background-color: #fff;
}

.is-dark .markdown-content :deep(img) {
  background-color: #0d1117;
}

.markdown-content :deep(hr) {
  height: 0.25em;
  padding: 0;
  margin: 24px 0;
  background-color: #e1e4e8;
  border: 0;
}

.is-dark .markdown-content :deep(hr) {
  background-color: #30363d;
}

.markdown-content :deep(.task-list-item) {
  list-style-type: none;
  margin-left: -20px;
  display: flex;
  align-items: center;
}

.markdown-content :deep(.task-list-item input) {
  margin-right: 8px;
}

/* 暗黑模式样式 - 使用项目统一的选择器 */
[data-theme='dark'] .markdown-container {
  background-color: #1a1a1a;
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .markdown-content :deep(h1) {
  border-bottom-color: #404040;
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .markdown-content :deep(h2) {
  border-bottom-color: #404040;
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .markdown-content :deep(h3),
[data-theme='dark'] .markdown-content :deep(h4),
[data-theme='dark'] .markdown-content :deep(h5),
[data-theme='dark'] .markdown-content :deep(h6) {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .markdown-content :deep(p) {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .markdown-content :deep(a) {
  color: #58a6ff;
}

[data-theme='dark'] .markdown-content :deep(a:hover) {
  color: #79c0ff;
}

[data-theme='dark'] .markdown-content :deep(strong) {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .markdown-content :deep(em) {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .markdown-content :deep(code) {
  background-color: #262626;
  color: #f85149;
  border-color: #404040;
}

[data-theme='dark'] .markdown-content :deep(pre) {
  background-color: #1a1a1a;
  border-color: #404040;
}

[data-theme='dark'] .markdown-content :deep(pre code) {
  background-color: transparent;
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .markdown-content :deep(blockquote) {
  color: rgba(255, 255, 255, 0.65);
  border-left-color: #404040;
}

[data-theme='dark'] .markdown-content :deep(table) {
  border-color: #404040;
}

[data-theme='dark'] .markdown-content :deep(th) {
  background-color: #262626;
  border-color: #404040;
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .markdown-content :deep(td) {
  border-color: #404040;
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .markdown-content :deep(tr:nth-child(2n)) {
  background-color: #1a1a1a;
}

[data-theme='dark'] .markdown-content :deep(ul),
[data-theme='dark'] .markdown-content :deep(ol) {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .markdown-content :deep(li) {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme='dark'] .markdown-content :deep(hr) {
  background-color: #404040;
}

[data-theme='dark'] .markdown-content :deep(.task-list-item) {
  color: rgba(255, 255, 255, 0.85);
}
</style>