<template>
  <ConfigProvider :locale="zhCN">
    <div class="app-container">
      <Layout style="min-height: 100vh">
        <ALayoutHeader class="header custom-header">
          <div class="logo">
            <div class="logo-icon">
              <img src="@/assets/dquestion.svg" alt="YQuestionSQL Logo" />
            </div>
            <h1 class="logo-text">YQuestionSQL</h1>
          </div>
          <div class="menu-container">
            <div class="menu-wrapper">
              <Menu
                v-model:selectedKeys="selectedKeys"
                theme="light"
                mode="horizontal"
                :style="{ lineHeight: '64px', flex: 1 }"
                class="main-menu"
                @select="handleMenuSelect"
              >
                <AMenuItem key="home" class="menu-item" data-key="home">
                  <div class="menu-link">
                    <HomeOutlined class="menu-icon" />
                    <span class="menu-text">{{ t('text2sql.train.nav.home') }}</span>
                  </div>
                </AMenuItem>
                <AMenuItem key="history" class="menu-item" data-key="history">
                  <div class="menu-link">
                    <HistoryOutlined class="menu-icon" />
                    <span class="menu-text">{{ t('text2sql.train.nav.history') }}</span>
                  </div>
                </AMenuItem>
                <AMenuItem key="training" class="menu-item" data-key="training">
                  <div class="menu-link">
                    <ExperimentOutlined class="menu-icon" />
                    <span class="menu-text">{{ t('text2sql.train.nav.training') }}</span>
                  </div>
                </AMenuItem>
<!--                <AMenuItem key="modeling" class="menu-item" data-key="modeling">-->
<!--                  <div class="menu-link">-->
<!--                    <PartitionOutlined class="menu-icon" />-->
<!--                    <span class="menu-text">数据建模</span>-->
<!--                  </div>-->
<!--                </AMenuItem>-->
<!--                <AMenuItem key="value-mapping" class="menu-item" data-key="value-mapping">-->
<!--                  <div class="menu-link">-->
<!--                    <TagsOutlined class="menu-icon" />-->
<!--                    <span class="menu-text">值映射</span>-->
<!--                  </div>-->
<!--                </AMenuItem>-->
<!--                <AMenuItem key="knowledge-graph" class="menu-item" data-key="knowledge-graph">-->
<!--                  <div class="menu-link">-->
<!--                    <NodeIndexOutlined class="menu-icon" />-->
<!--                    <span class="menu-text">知识图谱</span>-->
<!--                  </div>-->
<!--                </AMenuItem>-->
              </Menu>
              <!-- <div class="menu-slider" :style="sliderStyle"></div> -->
            </div>
          </div>
        </ALayoutHeader>

        <ALayoutContent class="content">
          <Home v-if="selectedKeys[0] === 'home'" />
          <History v-if="selectedKeys[0] === 'history'" />
          <Training v-if="selectedKeys[0] === 'training'" />
          <DataModeling v-if="selectedKeys[0] === 'modeling'" />
          <ValueMapping v-if="selectedKeys[0] === 'value-mapping'" />
          <KnowledgeGraphView v-if="selectedKeys[0] === 'knowledge-graph'" />
        </ALayoutContent>

        <ALayoutFooter class="footer">
          <div class="footer-content">
            <span>{{ t('text2sql.train.footer.copyright', { year: new Date().getFullYear() }) }}</span>
            <div class="footer-links">
              <a href="#">{{ t('text2sql.train.footer.links.about') }}</a>
              <a href="#">{{ t('text2sql.train.footer.links.docs') }}</a>
              <a href="#">{{ t('text2sql.train.footer.links.api') }}</a>
            </div>
          </div>
        </ALayoutFooter>
      </Layout>
    </div>
  </ConfigProvider>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick, onMounted } from 'vue';
// import { useRoute, useRouter } from 'vue-router';
import { ConfigProvider, Layout, Menu } from 'ant-design-vue';
import { useI18n } from '/@/hooks/web/useI18n';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import Home from './components/Home.vue';
import History from './components/History.vue';
import Training from './components/Training.vue';
import DataModeling from './components/DataModeling.vue';
import ValueMapping from './components/ValueMapping.vue';
import KnowledgeGraphView from './components/KnowledgeGraphView.vue';

const { Header: ALayoutHeader, Content: ALayoutContent, Footer: ALayoutFooter } = Layout;
const { Item: AMenuItem } = Menu;
import {
  HomeOutlined,
  HistoryOutlined,
  ExperimentOutlined,
  PartitionOutlined,
  TagsOutlined,
  NodeIndexOutlined
} from '@ant-design/icons-vue';

// const route = useRoute();
// const router = useRouter();
const { t } = useI18n();
const selectedKeys = ref<string[]>(['home']);
const menuItemRefs = ref<Record<string, HTMLElement | null>>({
  home: null,
  history: null,
  training: null,
  modeling: null,
  'value-mapping': null,
  'knowledge-graph': null
});

// 根据路由更新选中的菜单项
// watch(
//   () => route.path,
//   (path) => {
//     if (path === '/text2sql') {
//       selectedKeys.value = ['home'];
//     } else if (path.startsWith('/text2sql/history')) {
//       selectedKeys.value = ['history'];
//     } else if (path.startsWith('/text2sql/training')) {
//       selectedKeys.value = ['training'];
//     }

//     // 更新滑块位置
//     nextTick(() => {
//       updateMenuItemRefs();
//     });
//   },
//   { immediate: true }
// );

// 获取滑块样式
const sliderStyle = computed(() => {
  const key = selectedKeys.value[0];
  const el = menuItemRefs.value[key];

  if (!el) return { display: 'none' };

  return {
    left: `${el.offsetLeft}px`,
    width: `${el.offsetWidth}px`
  };
});

// 更新菜单项引用
const updateMenuItemRefs = () => {
  const keys = ['home', 'history', 'training', 'modeling', 'value-mapping', 'knowledge-graph'];
  keys.forEach(key => {
    const el = document.querySelector(`.menu-item[data-key="${key}"]`) as HTMLElement;
    if (el) {
      menuItemRefs.value[key] = el;
    }
  });
};

// 切换组件
const switchComponent = (key: string) => {
  selectedKeys.value = [key];

  // 根据key跳转到对应路由
  // if (key === 'home') {
  //   router.push('/text2sql');
  // } else if (key === 'history') {
  //   router.push('/text2sql/history');
  // } else if (key === 'training') {
  //   router.push('/text2sql/training');
  // }

  // 更新滑块位置
  nextTick(() => {
    updateMenuItemRefs();
  });
};

// 处理菜单选择
const handleMenuSelect = (info: any) => {
  // 切换组件
  switchComponent(info.key as string);

  // 更新滑块位置
  nextTick(() => {
    updateMenuItemRefs();
  });
};

// 组件挂载后更新菜单项引用
onMounted(() => {
  nextTick(() => {
    updateMenuItemRefs();
  });

  // 添加事件监听器，监听切换到首页的事件
  document.addEventListener('switchToHome', () => {
    switchComponent('home');
  });
});
</script>

<style>
:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --font-size-base: 14px;
  --heading-color: rgba(0, 0, 0, 0.85);
  --text-color: rgba(0, 0, 0, 0.65);
  --text-color-secondary: rgba(0, 0, 0, 0.45);
  --disabled-color: rgba(0, 0, 0, 0.25);
  --border-radius-base: 4px;
  --border-color-base: #d9d9d9;
  --box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
  --background-color-light: #f5f5f5;
  --background-color-base: #f0f2f5;
}

html, body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
}

.app-container {
  height: 100%;
}

.header {
  display: flex;
  align-items: center;
  padding: 0 24px;
  background: linear-gradient(90deg, #ffffff 0%, #f0f7ff 100%);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1);
  position: relative;
  z-index: 10;
}

.logo {
  display: flex;
  align-items: center;
  margin-right: 24px;
  position: relative;
  overflow: hidden;
}

.logo-icon {
  position: relative;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 8px;
  margin-right: 12px;
  box-shadow: 0 0 10px rgba(24, 144, 255, 0.3);
  overflow: hidden;
}

.logo-icon::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  background: linear-gradient(45deg, #1890ff, #36cfc9);
  z-index: -1;
  animation: rotate 4s linear infinite;
}

.logo-icon img {
  height: 24px;
  width: 24px;
  position: relative;
  z-index: 1;
  filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.5));
  transition: transform 0.3s ease;
}

.logo:hover .logo-icon img {
  transform: scale(1.2);
}

.logo-text {
  color: #1890ff;
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  letter-spacing: 0.5px;
  background: linear-gradient(90deg, #1a1a1a, #1890ff);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 0 10px rgba(24, 144, 255, 0.1);
}

.menu-container {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.menu-wrapper {
  position: relative;
  display: flex;
  flex: 1;
}

.main-menu {
  background: transparent !important;
  border-bottom: none !important;
  width: 100%;
}

.main-menu :deep(.ant-menu-item) {
  color: #333 !important;
}

.main-menu :deep(.ant-menu-item-selected) {
  color: #1890ff !important;
}

.menu-item {
  position: relative;
  margin: 0 4px;
  border-radius: 4px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.menu-item:hover {
  background-color: rgba(24, 144, 255, 0.05);
}

.menu-slider {
  position: absolute;
  bottom: 0;
  height: 3px;
  background: linear-gradient(90deg, #1890ff, #36cfc9);
  border-radius: 3px 3px 0 0;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  box-shadow: 0 0 8px rgba(24, 144, 255, 0.5);
  z-index: 1;
}

.menu-link {
  display: flex;
  align-items: center;
  padding: 0 8px;
}

.menu-icon {
  margin-right: 8px;
  font-size: 16px;
  transition: transform 0.3s ease;
  color: #333;
}

.menu-item:hover .menu-icon {
  transform: translateY(-2px);
  color: #1890ff;
}

.menu-text {
  font-size: 14px;
  transition: all 0.3s ease;
  color: #333;
}

.menu-item:hover .menu-text {
  color: #1890ff;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.content {
  padding: 16px;
  background: var(--background-color-base);
  height: calc(100vh - 64px - 70px);
  overflow: auto;
}

.footer {
  text-align: center;
  padding: 24px 50px;
  color: var(--text-color-secondary);
  background: var(--background-color-light);
}

.footer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.footer-links {
  display: flex;
  gap: 24px;
}

.footer-links a {
  color: var(--text-color-secondary);
  transition: color 0.3s;
}

.footer-links a:hover {
  color: var(--primary-color);
}

/* 自定义头部样式，强制覆盖黑色背景 */
.custom-header {
  background: linear-gradient(90deg, #ffffff 0%, #f0f7ff 100%) !important;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.1) !important;
  z-index: 1000 !important;
}

.custom-header :deep(.ant-menu-dark) {
  background: transparent !important;
}

.custom-header :deep(.ant-menu-dark .ant-menu-item), 
.custom-header :deep(.ant-menu-dark .ant-menu-item-selected),
.custom-header :deep(.ant-menu-dark .ant-menu-submenu-title) {
  color: #333 !important;
}

.custom-header :deep(.ant-menu-dark .ant-menu-item:hover), 
.custom-header :deep(.ant-menu-dark .ant-menu-item-active),
.custom-header :deep(.ant-menu-dark .ant-menu-submenu-active), 
.custom-header :deep(.ant-menu-dark .ant-menu-submenu-open),
.custom-header :deep(.ant-menu-dark .ant-menu-submenu-selected), 
.custom-header :deep(.ant-menu-dark .ant-menu-submenu-title:hover) {
  color: #1890ff !important;
  background-color: rgba(24, 144, 255, 0.05) !important;
}

.custom-header :deep(.ant-menu-dark .ant-menu-item-selected) {
  color: #1890ff !important;
  background-color: rgba(24, 144, 255, 0.1) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 0 12px;
  }

  .logo h1 {
    display: none;
  }

  .content {
    padding: 12px;
  }

  .footer {
    padding: 16px;
  }
}

/* 暗黑模式样式 */
[data-theme='dark'] {
  :root {
    --heading-color: rgba(255, 255, 255, 0.85);
    --text-color: rgba(255, 255, 255, 0.65);
    --text-color-secondary: rgba(255, 255, 255, 0.45);
    --disabled-color: rgba(255, 255, 255, 0.25);
    --border-color-base: #404040;
    --box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.3);
    --background-color-light: #262626;
    --background-color-base: #1f1f1f;
  }

  html, body {
    background: #141414;
    color: rgba(255, 255, 255, 0.85);
  }

  .app-container {
    background: #141414;
  }

  .header {
    background: linear-gradient(90deg, #1f1f1f 0%, #262626 100%);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .logo-icon {
    background: rgba(24, 144, 255, 0.15);
    box-shadow: 0 0 10px rgba(24, 144, 255, 0.4);
  }

  .logo-icon::before {
    background: linear-gradient(45deg, #1890ff, #36cfc9);
  }

  .logo-icon img {
    filter: drop-shadow(0 0 2px rgba(255, 255, 255, 0.3));
  }

  .logo-text {
    color: #40a9ff;
    text-shadow: 0 0 8px rgba(24, 144, 255, 0.3);
  }

  .menu-container {
    .main-menu {
      background: transparent;

      .menu-item {
        .menu-link {
          color: rgba(255, 255, 255, 0.65);

          .menu-icon {
            color: rgba(255, 255, 255, 0.65);
          }

          .menu-text {
            color: rgba(255, 255, 255, 0.65);
          }
        }

        &:hover .menu-link {
          color: #1890ff;

          .menu-icon {
            color: #1890ff;
          }

          .menu-text {
            color: #1890ff;
          }
        }

        &.ant-menu-item-selected .menu-link {
          color: #1890ff;

          .menu-icon {
            color: #1890ff;
          }

          .menu-text {
            color: #1890ff;
          }
        }
      }
    }
  }

  .content {
    background: #1f1f1f;
    color: rgba(255, 255, 255, 0.85);
  }

  .footer {
    background: #1f1f1f;
    border-top-color: #303030;
    color: rgba(255, 255, 255, 0.45);
  }

  /* Ant Design 组件暗黑模式覆盖 */
  .ant-layout {
    background: #141414;
  }

  .ant-layout-header {
    background: linear-gradient(90deg, #1f1f1f 0%, #262626 100%);
  }

  .ant-layout-content {
    background: #1f1f1f;
  }

  .ant-layout-footer {
    background: #1f1f1f;
  }

  .ant-menu {
    background: transparent;
    color: rgba(255, 255, 255, 0.65);

    .ant-menu-item {
      color: rgba(255, 255, 255, 0.65);

      &:hover {
        color: #1890ff;
        background: rgba(24, 144, 255, 0.1);
      }

      &.ant-menu-item-selected {
        color: #1890ff;
        background: rgba(24, 144, 255, 0.15);
      }
    }
  }

  /* 深度选择器样式覆盖 */
  .custom-header :deep(.ant-menu-light .ant-menu-item:hover),
  .custom-header :deep(.ant-menu-light .ant-menu-item-active),
  .custom-header :deep(.ant-menu-light .ant-menu-submenu-active),
  .custom-header :deep(.ant-menu-light .ant-menu-submenu-open),
  .custom-header :deep(.ant-menu-light .ant-menu-submenu-selected),
  .custom-header :deep(.ant-menu-light .ant-menu-submenu-title:hover) {
    color: #1890ff !important;
    background-color: rgba(24, 144, 255, 0.1) !important;
  }

  .custom-header :deep(.ant-menu-light .ant-menu-item-selected) {
    color: #1890ff !important;
    background-color: rgba(24, 144, 255, 0.15) !important;
  }

  .custom-header :deep(.ant-menu-dark .ant-menu-item:hover),
  .custom-header :deep(.ant-menu-dark .ant-menu-item-active),
  .custom-header :deep(.ant-menu-dark .ant-menu-submenu-active),
  .custom-header :deep(.ant-menu-dark .ant-menu-submenu-open),
  .custom-header :deep(.ant-menu-dark .ant-menu-submenu-selected),
  .custom-header :deep(.ant-menu-dark .ant-menu-submenu-title:hover) {
    color: #1890ff !important;
    background-color: rgba(24, 144, 255, 0.1) !important;
  }

  .custom-header :deep(.ant-menu-dark .ant-menu-item-selected) {
    color: #1890ff !important;
    background-color: rgba(24, 144, 255, 0.15) !important;
  }
}
</style>
