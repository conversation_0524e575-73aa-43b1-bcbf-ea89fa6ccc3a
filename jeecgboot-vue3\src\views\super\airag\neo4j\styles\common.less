// 知识图谱公共样式文件
// 包含可复用的样式混合和变量

// ========== 变量定义 ==========
@primary-color: #1890ff;

// 亮色模式变量
@bg-white-translucent: rgba(255, 255, 255, 0.95);
@bg-white-semi: rgba(255, 255, 255, 0.85);
@border-color: #eaeaea;
@border-color-light: #f0f0f0;
@text-color: #333;
@text-color-secondary: #666;
@text-color-light: #999;
@text-color-disabled: #999;
@shadow-light: 0 2px 10px rgba(0, 0, 0, 0.2);
@shadow-hover: 0 6px 16px rgba(0, 0, 0, 0.2);
@shadow-button: 0 2px 8px rgba(24, 144, 255, 0.2);

// 暗黑模式变量
@dark-bg-primary: #1f1f1f;
@dark-bg-secondary: #262626;
@dark-bg-tertiary: #1a1a1a;
@dark-bg-translucent: rgba(38, 38, 38, 0.95);
@dark-bg-semi: rgba(38, 38, 38, 0.85);
@dark-border-color: #404040;
@dark-border-color-light: #303030;
@dark-text-color: rgba(255, 255, 255, 0.85);
@dark-text-color-secondary: rgba(255, 255, 255, 0.65);
@dark-text-color-light: rgba(255, 255, 255, 0.45);
@dark-text-color-disabled: rgba(255, 255, 255, 0.25);
@dark-shadow-light: 0 2px 10px rgba(0, 0, 0, 0.3);
@dark-shadow-hover: 0 6px 16px rgba(0, 0, 0, 0.4);
@dark-shadow-button: 0 2px 8px rgba(24, 144, 255, 0.3);

@radius-small: 2px;
@radius-normal: 4px;
@radius-large: 8px;
@animation-duration: 0.3s;

// ========== 动画定义 ==========
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// ========== 混合样式（Mixins）==========

// 弹性盒居中混合
.flex-center() {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 绝对定位居中混合
.absolute-center() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 文本溢出省略混合
.text-ellipsis(@max-width: 100%) {
  max-width: @max-width;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 毛玻璃效果混合
.glass-effect(@bg-color: @bg-white-semi, @blur: 4px) {
  background-color: @bg-color;
  backdrop-filter: blur(@blur);
}

// 悬停提升效果混合
.hover-lift(@offset: -2px) {
  transition: all @animation-duration ease;
  
  &:hover {
    transform: translateY(@offset);
  }
}

// 卡片阴影混合
.card-shadow(@hover: false) {
  box-shadow: @shadow-light;
  transition: all @animation-duration ease;
  
  & when (@hover = true) {
    &:hover {
      box-shadow: @shadow-hover;
    }
  }
}

// ========== 实体详情面板通用样式 ==========
.entity-detail-base() {
  position: absolute;
  width: 320px;
  max-height: 400px;
  background-color: @bg-white-translucent;
  border-radius: @radius-normal;
  .card-shadow(true);
  overflow-y: auto;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  animation: fadeIn @animation-duration ease-out;
  
  // 响应式设计
  @media (max-width: 768px) {
    width: 280px;
    max-height: 350px;
  }
  
  .entity-detail-header {
    padding: 8px 12px;
    background-color: #f8f8f8;
    border-bottom: 1px solid @border-color;
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .node-title {
      display: flex;
      align-items: center;
      gap: 8px;
      flex: 1;
      
      .node-type-indicator {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        flex-shrink: 0;
      }
      
      .entity-title,
      h3 {
        margin: 0;
        font-size: 14px;
        font-weight: 500;
        .text-ellipsis(250px);
      }
    }
    
    .close-btn {
      .hover-lift();
      cursor: pointer;
      color: @text-color-light;
      
      &:hover {
        color: @text-color;
      }
    }
  }
  
  .entity-detail-content {
    padding: 12px;
    flex: 1;
    overflow-y: auto;
    
    .ant-descriptions-item-label {
      font-weight: 500;
      white-space: nowrap;
      color: @text-color-secondary;
    }
    
    .ant-descriptions-item-content {
      color: @text-color;
    }
    
    .ant-descriptions-item {
      padding: 6px 0;
      border-bottom: 1px solid @border-color-light;
      
      &:last-child {
        border-bottom: none;
      }
    }
    
    .type-with-color {
      display: flex;
      align-items: center;
      
      .type-color-marker {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: @radius-small;
        margin-right: 6px;
        flex-shrink: 0;
      }
    }
  }
}

// ========== 节点关系样式 ==========
.node-relations-style() {
  .node-relations {
    margin-top: 10px;
    
    .relation-section {
      margin-bottom: 12px;
      
      .relation-title {
        font-size: 13px;
        font-weight: 500;
        color: #444;
        margin-bottom: 8px;
        padding-bottom: 4px;
        border-bottom: 1px solid @border-color-light;
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 0;
          width: 30px;
          height: 2px;
          background-color: @primary-color;
        }
      }
      
      .relation-list {
        list-style: none;
        padding: 0;
        margin: 0;
        
        .relation-item {
          display: flex;
          align-items: center;
          margin-bottom: 6px;
          font-size: 12px;
          padding: 4px 0;
          border-radius: @radius-small;
          transition: background-color @animation-duration ease;
          
          &:hover {
            background-color: rgba(24, 144, 255, 0.05);
          }
          
          .relation-node {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
            flex-shrink: 0;
          }
          
          .relation-name {
            flex: 1;
            .text-ellipsis(120px);
            cursor: pointer;
            color: @primary-color;
            transition: color @animation-duration ease;
            
            &:hover {
              color: darken(@primary-color, 10%);
            }
          }
          
          .relation-arrow {
            margin: 0 5px;
            color: @text-color-light;
            font-size: 10px;
          }
          
          .relation-current {
            color: @primary-color;
            font-weight: 500;
          }
        }
      }
    }
  }
}

// ========== 图表控制按钮样式 ==========
.graph-controls-style() {
  .graph-controls {
    position: absolute;
    bottom: 20px;
    right: 50px;
    z-index: 100;
    .glass-effect();
    padding: 8px;
    border-radius: @radius-large;
    .card-shadow(true);
    
    .ant-btn {
      height: 32px;
      width: 32px;
      .flex-center();
      margin: 0 2px;
      border-radius: @radius-normal;
      transition: all @animation-duration ease;
      
      // 使操作区按钮与查询按钮颜色保持一致
      &.ant-btn-primary {
        background: linear-gradient(135deg, @primary-color, lighten(@primary-color, 10%));
        border: none;
        
        &:hover {
          background: linear-gradient(135deg, darken(@primary-color, 5%), @primary-color);
          .hover-lift();
          box-shadow: @shadow-button;
        }
        
        &:focus {
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
      
      &:hover {
        .hover-lift();
        box-shadow: @shadow-button;
      }
      
      &:focus {
        box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
      }
    }
  }
}

// ========== 容器通用样式 ==========
.graph-container-base() {
  height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;
  
  .graph-view {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }
  
  .loading-container,
  .empty-container {
    .absolute-center();
    z-index: 10;
  }
  
  .loading-container {
    .ant-spin {
      color: @primary-color;
    }
  }
  
  .empty-container {
    text-align: center;
    color: @text-color-secondary;
    
    .empty-icon {
      font-size: 48px;
      color: @text-color-disabled;
      margin-bottom: 16px;
    }
    
    .empty-text {
      font-size: 16px;
      line-height: 1.5;
    }
  }
}

// ========== 标签样式 ==========
.label-styles() {
  .node-label {
    color: @text-color;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 12px;
    font-weight: 500;
    padding: 2px 6px;
    border-radius: @radius-normal;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    pointer-events: none;
    white-space: nowrap;
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.1);
  }
  
  .link-label {
    color: @text-color-secondary;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 10px;
    padding: 1px 4px;
    border-radius: @radius-small;
    background-color: rgba(255, 255, 255, 0.8);
    pointer-events: none;
    white-space: nowrap;
    text-align: center;
    border: 1px solid rgba(0, 0, 0, 0.05);
  }
}

// ========== 暗黑模式样式 ==========
[data-theme='dark'] {
  // 暗黑模式下的毛玻璃效果混合
  .glass-effect-dark(@bg-color: @dark-bg-semi, @blur: 4px) {
    background-color: @bg-color;
    backdrop-filter: blur(@blur);
  }

  // 暗黑模式下的卡片阴影混合
  .card-shadow-dark(@hover: false) {
    box-shadow: @dark-shadow-light;
    transition: all @animation-duration ease;

    & when (@hover = true) {
      &:hover {
        box-shadow: @dark-shadow-hover;
      }
    }
  }

  // 暗黑模式下的实体详情面板样式
  .entity-detail-base() {
    position: absolute;
    width: 320px;
    max-height: 400px;
    background: @dark-bg-translucent;
    backdrop-filter: blur(10px);
    border: 1px solid @dark-border-color;
    border-radius: @radius-normal;
    box-shadow: @dark-shadow-light;
    z-index: 1000;
    animation: fadeIn @animation-duration ease;
    overflow: hidden;

    .entity-detail-header {
      padding: 12px 16px;
      border-bottom: 1px solid @dark-border-color;
      background: @dark-bg-secondary;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .node-title {
        display: flex;
        align-items: center;
        gap: 8px;
        flex: 1;

        .node-type-indicator {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          flex-shrink: 0;
        }

        h3 {
          margin: 0;
          color: @dark-text-color;
          font-size: 14px;
          font-weight: 600;
          .text-ellipsis(240px);
        }
      }
    }

    .entity-detail-content {
      padding: 16px;
      max-height: 320px;
      overflow-y: auto;
      background: @dark-bg-primary;

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: @dark-bg-secondary;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: @dark-border-color;
        border-radius: 3px;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
        }
      }
    }
  }

  // 暗黑模式下的标签样式
  .label-styles() {
    .node-label {
      color: @dark-text-color;
      background-color: @dark-bg-translucent;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
      border: 1px solid @dark-border-color;
    }

    .link-label {
      color: @dark-text-color-secondary;
      background-color: @dark-bg-semi;
      border: 1px solid @dark-border-color-light;
    }
  }

  // 暗黑模式下的图表容器样式
  .graph-container-base() {
    position: relative;
    height: 100%;
    width: 100%;
    background: @dark-bg-primary;
    overflow: hidden;

    .graph-view {
      width: 100%;
      height: 100%;
      background: @dark-bg-primary;

      svg {
        background: @dark-bg-primary;
      }
    }

    .loading-container {
      .absolute-center();
      z-index: 10;

      .ant-spin {
        .ant-spin-dot {
          .ant-spin-dot-item {
            background-color: @primary-color;
          }
        }

        .ant-spin-text {
          color: @dark-text-color;
        }
      }
    }

    .empty-container {
      .absolute-center();
      z-index: 10;

      .ant-empty {
        .ant-empty-description {
          color: @dark-text-color-light;
        }
      }
    }
  }

  // 暗黑模式下的图表控制按钮样式
  .graph-controls-style() {
    .graph-controls {
      position: absolute;
      top: 16px;
      right: 16px;
      z-index: 100;

      .ant-btn-group {
        .ant-btn {
          background: @dark-bg-secondary;
          border-color: @dark-border-color;
          color: @dark-text-color;

          &:hover {
            background: @dark-bg-tertiary;
            border-color: @primary-color;
            color: @primary-color;
          }

          &.ant-btn-primary {
            background: @primary-color;
            border-color: @primary-color;
            color: white;

            &:hover {
              background: lighten(@primary-color, 10%);
              border-color: lighten(@primary-color, 10%);
            }
          }
        }
      }
    }
  }
}
