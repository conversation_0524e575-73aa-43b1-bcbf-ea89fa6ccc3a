<!-- 知识图谱视图页面 -->
<template>
  <div class="knowledge-graph-view">
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h2 class="page-title">
            <NodeIndexOutlined class="title-icon" />
            {{ t('text2sql.train.knowledgeGraph.title') }}
          </h2>
          <p class="page-description">{{ t('text2sql.train.knowledgeGraph.description') }}</p>
        </div>
        <div class="header-right">
          <a-space>
            <a-select
              v-model:value="selectedConnectionId"
              :placeholder="t('text2sql.train.knowledgeGraph.selectConnection')"
              style="width: 200px"
              :loading="connectionsLoading"
              @change="handleConnectionChange"
              allow-clear
            >
              <a-select-option
                v-for="connection in connections"
                :key="connection.id"
                :value="connection.id"
              >
                <div class="connection-option">
                  <a-tag :color="getDbTypeColor(getDbTypeName(connection.db_type))" size="small">
                    {{ getDbTypeName(connection.db_type) }}
                  </a-tag>
                  <span>{{ connection.name }}</span>
                </div>
              </a-select-option>
            </a-select>
            <a-button type="primary" @click="refreshConnections" :loading="connectionsLoading">
              <ReloadOutlined />
              {{ t('text2sql.train.knowledgeGraph.refreshConnection') }}
            </a-button>
          </a-space>
        </div>
      </div>
    </div>

    <div class="page-content">
      <div v-if="!selectedConnectionId" class="empty-state">
        <a-empty :description="t('text2sql.train.knowledgeGraph.empty')">
          <template #image>
            <NodeIndexOutlined style="font-size: 64px; color: #d9d9d9;" />
          </template>
          <a-button type="primary" @click="refreshConnections">
            <DatabaseOutlined />
            {{ t('text2sql.train.knowledgeGraph.loadConnections') }}
          </a-button>
        </a-empty>
      </div>

      <div v-else class="graph-content">
        <DatabaseKnowledgeGraph
          ref="knowledgeGraphRef"
          :connection-id="selectedConnectionId"
          @table-selected="handleTableSelected"
        />
      </div>
    </div>

    <!-- 表详情抽屉 -->
    <!-- 已移除抽屉，只使用浮窗显示表详情 -->
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useI18n } from 'vue-i18n';
import { connectionsApi } from '@/api/text2sql/sys';
import DatabaseKnowledgeGraph from './DatabaseKnowledgeGraph.vue';
import {
  NodeIndexOutlined,
  ReloadOutlined,
  DatabaseOutlined
} from '@ant-design/icons-vue';

const { t } = useI18n();

// 响应式数据
const connections = ref<any[]>([]);
const connectionsLoading = ref<boolean>(false);
const selectedConnectionId = ref<string | undefined>(undefined);
const knowledgeGraphRef = ref<any>(null);
// 移除抽屉相关的响应式数据
// const tableDetailVisible = ref<boolean>(false);
// const selectedTableInfo = ref<any>(null);

// 移除表格列配置，因为不再需要抽屉
// const columnTableColumns = [
//   {
//     title: '列名',
//     dataIndex: 'name',
//     key: 'name',
//     width: 120,
//   },
//   {
//     title: '数据类型',
//     dataIndex: 'data_type',
//     key: 'data_type',
//     width: 100,
//   },
//   {
//     title: '描述',
//     dataIndex: 'description',
//     key: 'description',
//     ellipsis: true,
//   }
// ];

// 计算属性
const getDbTypeColor = (dbType: string) => {
  const colorMap: Record<string, string> = {
    'mysql': 'pink',
    'mysql 5.7+': 'blue',
    'postgresql': 'green',
    'sqlite': 'orange',
    'oracle': 'red',
    'sqlserver': 'purple',
    'mongodb': 'cyan'
  };
  return colorMap[dbType?.toLowerCase()] || 'default';
};

// 数据库类型ID到名称的映射
const getDbTypeName = (dbTypeId: string | number) => {
  const dbTypeMap: Record<string, string> = {
    '1': 'MySQL',
    '2': 'Oracle', 
    '3': 'SQL Server',
    '4': 'MySQL 5.7+',
    '5': 'MariaDB',
    '6': 'PostgreSQL',
    '7': 'H2',
    '8': 'KingBase',
    '9': 'Oscar',
    '10': 'SQLite',
    '11': 'DB2',
    '12': 'HSQLDB',
    '13': 'Derby',
    '14': 'H2',
    '15': 'Unknown'
  };
  return dbTypeMap[String(dbTypeId)] || String(dbTypeId);
};

// 方法
const loadConnections = async () => {
  connectionsLoading.value = true;
  try {
    const data = await connectionsApi.getConnections();
    connections.value = data;
    
    // 移除自动选择逻辑，让用户手动选择
    // if (data.length === 1) {
    //   selectedConnectionId.value = data[0].id;
    // }
  } catch (error) {
    console.error('加载数据库连接失败:', error);
    message.error(t('text2sql.errors.loadConnectionsFailed'));
  } finally {
    connectionsLoading.value = false;
  }
};

const refreshConnections = () => {
  loadConnections();
};

const handleConnectionChange = (connectionId: string) => {
  selectedConnectionId.value = connectionId;
  // 清除之前选择的表信息（不再需要，因为没有抽屉）
  // selectedTableInfo.value = null;
  // tableDetailVisible.value = false;
};

const handleTableSelected = (tableId: string) => {
  // 不再处理抽屉显示，表详情将通过浮窗在DatabaseKnowledgeGraph组件内部显示
  console.log(t('text2sql.train.knowledgeGraph.tableDetail.title') + '被选中:', tableId);
};

// 生命周期
onMounted(() => {
  loadConnections();
});
</script>

<style lang="less" scoped>
/* 暗黑模式适配 */
[data-theme='dark'] .knowledge-graph-view {
  background: #141414;

  .page-header {
    background: #1f1f1f;
    border-bottom-color: #303030;
  }

  .page-title {
    color: #ffffff;
  }

  .page-description {
    color: #a6a6a6;
  }

  .connection-option {
    .connection-name {
      color: #ffffff;
    }

    .connection-type {
      color: #a6a6a6;
    }
  }

  .empty-state {
    background: #1f1f1f !important;
    color: #a6a6a6 !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;

    .ant-empty-description {
      color: #a6a6a6 !important;
    }

    .anticon {
      color: #404040 !important;
    }
  }

  .graph-content {
    background: #1f1f1f !important;
  }

  .page-content {
    background: #141414 !important;
  }
}

.knowledge-graph-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;

  .page-header {
    background: white;
    border-bottom: 1px solid #e8e8e8;
    padding: 12px 16px;
    flex-shrink: 0;
    border-radius: 16px 16px 0 0;

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;

      .header-left {
        .page-title {
          margin: 0 0 4px 0;
          display: flex;
          align-items: center;
          gap: 12px;
          color: #1890ff;
          font-size: 18px;
          font-weight: 600;

          .title-icon {
            font-size: 20px;
          }
        }

        .page-description {
          margin: 0;
          color: #666;
          font-size: 12px;
        }
      }

      .header-right {
        .connection-option {
          display: flex;
          align-items: center;
          gap: 8px;
        }
        .ant-btn-primary {
            // 与KnowledgeGraph3D保持一致的主色调渐变效果
            background: linear-gradient(135deg, #1890ff, #40a9ff) !important;
            border: none !important;
            transition: all 0.3s ease;
            
            &:hover {
              background: linear-gradient(135deg, #096dd9, #1890ff) !important;
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
            }
            
            &:focus {
              background: linear-gradient(135deg, #1890ff, #40a9ff) !important;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            }
            
            &:active {
              transform: translateY(0);
            }
        }
      }
    }
  }

  .page-content {
    flex: 1;
    padding: 12px;
    overflow: hidden;
    min-height: 0;

    .empty-state {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background: white;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .graph-content {
      height: 100%;
      min-height: 500px;
    }
  }
}

// 移除抽屉相关样式，因为已经不再使用抽屉
// .table-detail-drawer {
//   .column-name-cell {
//     .column-name {
//       display: block;
//       font-weight: 600;
//       margin-bottom: 4px;
//       color: #333;
//     }

//     .column-tags {
//       display: flex;
//       gap: 4px;
//       flex-wrap: wrap;
//     }
//   }

//   .no-columns {
//     text-align: center;
//     padding: 40px 0;
//   }
// }

// 全局样式覆盖
// 移除抽屉相关的全局样式
// :global(.ant-drawer-body) {
//   padding: 16px;
// }

:global(.ant-descriptions-item-label) {
  font-weight: 600;
}
</style> 