.knowledge-graph-chat {
  height: calc(100vh - 110px); // 减去顶部导航栏高度
  background: #f5f7fa;
  position: relative;
  display: flex;



  .chat-main {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: white;
    transition: width 0.3s ease, margin-left 0.3s ease; // 添加过渡效果，为以后的侧边栏展开动画做准备
    
    .chat-header {
      padding: 16px 24px;
      background: linear-gradient(135deg, #ffffff 0%, #f0f7ff 100%);
      border-bottom: 1px solid #e8e8e8;
      box-shadow: 0 8px 24px rgba(24, 144, 255, 0.15);
      display: flex;
      align-items: center;
      justify-content: space-between;
      position: relative;
      overflow: hidden;
      
      &::before {
        content: '';
        position: absolute;
        top: -50%;
        left: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(24, 144, 255, 0.05) 0%, transparent 60%);
        z-index: 0;
      }
      
      &::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNMCAwaDEwMHYxMDBIMHoiIGZpbGw9Im5vbmUiLz48cGF0aCBkPSJNOTkuOTkyIDAgMCAxMDBoMTAwVjB6IiBmaWxsPSJyZ2JhKDI0LCAxNDQsIDI1NSwgMC4wMykiLz48L3N2Zz4=');
        opacity: 0.5;
        z-index: 0;
      }
      
              .header-left {
          display: flex;
          align-items: center;
          gap: 12px;
          position: relative;
          z-index: 1;
        
        .toggle-sidebar-btn,
        .new-chat-btn {
          width: 36px;
          height: 36px;
          border: 1px solid #d9d9d9;
          border-radius: 8px;
          background: white;
          cursor: pointer;
          transition: all 0.2s;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #666;
          
          &:hover {
            border-color: #1890ff;
            color: #1890ff;
          }
        }
        
        .new-chat-btn {
          // 新建对话按钮稍微突出一些
          border-color: #1890ff;
          color: #1890ff;
          
          &:hover {
            background: #f0f8ff;
            box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
          }
        }
        
        .chat-title {
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }
        
        .chat-status {
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
          
          &.connected {
            background: #f6ffed;
            color: #52c41a;
          }
          
          &.typing {
            background: #fff7e6;
            color: #fa8c16;
          }
          
          &.disconnected {
            background: #fff1f0;
            color: #ff4d4f;
          }
        }
      }
      
              .header-right {
          display: flex;
          align-items: center;
          gap: 12px;
          position: relative;
          z-index: 1;
        
        .collection-selector {
          .ant-select {
            border-radius: 8px;
            
            .ant-select-selector {
              border-radius: 8px;
              border: 1px solid #d9d9d9;
              
              &:hover {
                border-color: #1890ff;
              }
            }
          }
        }
        
        .session-btn {
          min-width: 80px;
          justify-content: center;
        }
      }
      
      .header-actions {
        display: flex;
        gap: 8px;
        
        .action-btn {
          padding: 8px 12px;
          border: 1px solid #d9d9d9;
          border-radius: 8px;
          background: white;
          cursor: pointer;
          transition: all 0.2s;
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 14px;

          &:hover {
            border-color: #1890ff;
            color: #1890ff;
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }

          // 取消按钮的红色样式
          &.cancel-btn {
            border-color: #ff4d4f;
            color: #ff4d4f;

            &:hover:not(:disabled) {
              border-color: #ff7875;
              color: #ff7875;
              background: #fff2f0;
            }

            &:active:not(:disabled) {
              border-color: #d9363e;
              color: #d9363e;
              background: #ffebee;
            }
          }
        }
      }
    }
    
    .chat-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      position: relative; // 为滚动按钮提供定位基准
      
      .messages-container {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 24px;
        background: white;
        scroll-behavior: smooth;
        display: flex;
        flex-direction: column;
        align-items: center;
        
        // 优化滚动性能和体验
        &::-webkit-scrollbar {
          width: 6px;
        }
        
        &::-webkit-scrollbar-track {
          background: transparent;
          border-radius: 3px;
        }
        
        &::-webkit-scrollbar-thumb {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 3px;
          transition: background-color 0.2s ease;
          
          &:hover {
            background: rgba(0, 0, 0, 0.3);
          }
        }
        
        // 提升滚动性能
        transform: translateZ(0);
        -webkit-overflow-scrolling: touch;
        
        // 消息包装器样式
        .messages-wrapper {
          width: 100%;
          display: flex;
          flex-direction: column;
          align-items: center;
          
          .message-wrapper {
            width: 100%;
            max-width: 900px; // 限制最大宽度
            margin-bottom: 16px;
            // 添加轻微的进入动画
            animation: fadeInUp 0.3s ease-out;
            
            &:last-child {
              margin-bottom: 24px;
            }
          }
        }
        
        // 为所有其他元素添加居中容器
        .welcome-message,
        .typing-indicator {
          width: 100%;
          max-width: 900px; // 限制最大宽度
          margin: 0 auto; // 水平居中
        }
        
        // 消息列表容器
        .message-list {
          min-height: 100%;
          display: flex;
          flex-direction: column;
          width: 100%;
        }
        
        .welcome-message {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          min-height: 60vh;
          
          .welcome-card {
            text-align: center;
            padding: 40px 20px;
            background: transparent;
            max-width: 800px;
            width: 100%;
            
            .welcome-icon {
              font-size: 60px;
              color: #1890ff;
              margin-bottom: 0px;
              opacity: 0.8;
              
              // 为图片添加样式
              &[src] {
                width: 150px;
                height: 150px;
                object-fit: contain;
                filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
                animation: knowledgeFloat 3s ease-in-out infinite;
                transition: all 0.3s ease;
                
                &:hover {
                  transform: scale(1.1);
                  filter: drop-shadow(0 6px 16px rgba(24, 144, 255, 0.3));
                }
              }
            }
            
            // 知识库图标浮动动画
            //@keyframes knowledgeFloat {
            //  0%, 100% {
            //    transform: translateY(0px);
            //    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
            //  }
            //  50% {
            //    transform: translateY(-8px);
            //    filter: drop-shadow(0 8px 16px rgba(24, 144, 255, 0.2));
            //  }
            //}
            
            h3 {
              font-size: 24px;
              font-weight: 600;
              color: #333;
              margin-bottom: 40px;
              letter-spacing: 0.5px;
            }
            
            .main-input-container {
              position: relative;
              max-width: 800px;
              margin: 0 auto 40px;
              z-index: 10;
              width: 100%;
              
              .main-input {
                width: 100%;
                height: 60px;
                padding: 0 120px 0 60px;
                border: 1px solid #e8e8e8;
                border-radius: 30px;
                font-size: 16px;
                background: white;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
                transition: all 0.3s ease;
                
                &:focus {
                  outline: none;
                  border-color: #1890ff;
                  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15);
                }
                
                &::placeholder {
                  color: #999;
                  font-size: 16px;
                }
                
                &:hover {
                  border-color: #40a9ff;
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                }
              }
              
              .input-actions {
                position: absolute;
                left: 20px;
                top: 50%;
                transform: translateY(-50%);
                display: flex;
                gap: 4px;
                
                .action-btn {
                  width: 28px;
                  height: 28px;
                  border: none;
                  background: transparent;
                  cursor: pointer;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  transition: all 0.3s ease;
                  
                  &:hover {
                    background: #f0f0f0;
                    transform: scale(1.1);
                  }
                  
                  .anticon {
                    font-size: 16px;
                    color: #666;
                  }
                }
              }
              
              .send-btn {
                position: absolute;
                right: 10px;
                top: 50%;
                transform: translateY(-50%);
                width: 40px;
                height: 40px;
                border: none;
                background: #1890ff;
                color: white;
                border-radius: 50%;
                cursor: pointer;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
                box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
                
                &:hover:not(:disabled) {
                  background: #40a9ff;
                  transform: translateY(-50%) scale(1.05);
                  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
                }
                
                &:active {
                  transform: translateY(-50%) scale(0.95);
                }
                
                &:disabled {
                  background: #d9d9d9;
                  cursor: not-allowed;
                  transform: translateY(-50%) scale(1);
                  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                }
              }
            }
            
            .quick-questions {
              display: flex;
              justify-content: center;
              align-items: center;
              gap: 12px;
              flex-wrap: wrap;
              max-width: 100%;
              
              .quick-question {
                padding: 12px 16px;
                background: white;
                border: 1px solid #e8e8e8;
                border-radius: 20px;
                cursor: pointer;
                transition: all 0.3s ease;
                font-size: 14px;
                color: #333;
                display: flex;
                align-items: center;
                gap: 8px;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
                white-space: nowrap;
                
                &:hover {
                  border-color: #1890ff;
                  background: #f0f8ff;
                  transform: translateY(-2px);
                  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.15);
                }
                
                .anticon {
                  font-size: 16px;
                  color: #1890ff;
                  flex-shrink: 0;
                }
              }
            }
          }
        }
        
        .typing-indicator {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 16px 0;
          
          .typing-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(79, 172, 254, 0.1) 0%, rgba(0, 242, 254, 0.1) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(79, 172, 254, 0.15);
            position: relative;
            overflow: hidden;
            border: 0.5px solid rgba(79, 172, 254, 0.2);
            
            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: linear-gradient(135deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.01) 100%);
              border-radius: 50%;
            }
            
                          .typing-head {
                width: 24px;
                height: 24px;
                position: relative;
                z-index: 1;
                
                .typing-brain {
                  width: 24px;
                  height: 24px;
                  border-radius: 50%;
                  background: rgba(255, 255, 255, 0.4);
                  position: relative;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  backdrop-filter: blur(2px);
                  
                  .typing-core {
                    width: 8px;
                    height: 8px;
                    background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
                    border-radius: 50%;
                    animation: pulse 2s ease-in-out infinite;
                    box-shadow: 0 0 12px rgba(79, 172, 254, 0.8);
                  }
                  
                  &::before {
                    content: '';
                    position: absolute;
                    width: 20px;
                    height: 20px;
                    border: 1.5px solid rgba(79, 172, 254, 0.6);
                    border-radius: 50%;
                    animation: rotate 4s linear infinite;
                  }
                  
                  &::after {
                    content: '';
                    position: absolute;
                    width: 15px;
                    height: 15px;
                    border: 1.5px solid rgba(0, 242, 254, 0.7);
                    border-top-color: transparent;
                    border-left-color: transparent;
                    border-radius: 50%;
                    animation: rotate 2s linear infinite reverse;
                  }
                }
              }
          }
          
          .typing-bubble {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 1px solid rgba(148, 163, 184, 0.1);
            border-radius: 20px 20px 20px 8px;
            padding: 12px 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            
            .typing-text {
              font-size: 14px;
              color: #475569;
              margin-bottom: 8px;
              font-weight: 500;
            }
            
            .typing-dots {
              display: flex;
              gap: 4px;
              
              .dot {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%);
                animation: typingPulse 1.5s infinite;
                box-shadow: 0 0 4px rgba(79, 172, 254, 0.4);
                
                &:nth-child(1) {
                  animation-delay: 0s;
                }
                
                &:nth-child(2) {
                  animation-delay: 0.3s;
                }
                
                &:nth-child(3) {
                  animation-delay: 0.6s;
                }
              }
            }
          }
        }
      }
      
    }
      
      .chat-input {
        padding: 16px 24px;
        background: white;
        border-top: 1px solid #e8e8e8;
        
        .input-container {
          position: relative;
          max-width: 800px;
          margin: 0 auto;
          
          .message-input {
            width: 100%;
            height: 60px;
            padding: 0 120px 0 80px;
            border: 1px solid #e8e8e8;
            border-radius: 30px;
            font-size: 16px;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            resize: none;
            
            &:focus {
              outline: none;
              border-color: #1890ff;
              box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15);
            }
            
            &::placeholder {
              color: #999;
              font-size: 16px;
            }
            
            &:hover {
              border-color: #40a9ff;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            }
            
            &:disabled {
              background: #f5f5f5;
              color: #999;
              cursor: not-allowed;
            }
          }
          
          .input-actions {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 4px;
            
            .action-btn {
              width: 28px;
              height: 28px;
              border: none;
              background: transparent;
              cursor: pointer;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              transition: all 0.3s ease;
              
              &:hover {
                background: #f0f0f0;
                transform: scale(1.1);
              }
              
              .anticon {
                font-size: 16px;
                color: #666;
              }
            }
          }
          
          .send-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            border: none;
            background: #1890ff;
            color: white;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
            
            &:hover:not(:disabled) {
              background: #40a9ff;
              transform: translateY(-50%) scale(1.05);
              box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
            }
            
            &:active {
              transform: translateY(-50%) scale(0.95);
            }
            
            &:disabled {
              background: #d9d9d9;
              cursor: not-allowed;
              transform: translateY(-50%) scale(1);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
          }
        }
      }
    }

    .collection-count {
      color: #999;
      font-size: 12px;
      margin-left: 4px;
    }
    
    // 复制成功状态样式
    .action-btn {
      transition: all 0.3s ease;
      
      &:has(.anticon-check-outlined) {
        color: #52c41a !important;
        border-color: #52c41a !important;
        
        &:hover {
          color: #73d13d !important;
          border-color: #73d13d !important;
        }
      }
    }
    
    // 文件上传相关样式
    .uploaded-files-preview {
      display: flex;
      flex-wrap: wrap;
      gap: 6px;
      margin-top: 0;
      padding: 8px 16px 12px 16px;
      border: 1px solid #e8e8e8;
      border-top: none;
      border-radius: 0 0 20px 20px;
      background: white;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      
      .file-tag {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        padding: 4px 8px;
        background: #f5f5f5;
        border: 1px solid #d9d9d9;
        border-radius: 16px;
        font-size: 12px;
        color: #333;
        max-width: 200px;
        transition: all 0.3s ease;
        
        &:hover {
          background: #e6f7ff;
          border-color: #40a9ff;
        }
        
        &.uploading {
          background: #e6f7ff;
          border-color: #40a9ff;
          color: #1890ff;
        }
        
        &.converting {
          background: #fff7e6;
          border-color: #ffad12;
          color: #fa8c16;
          
          .file-icon {
            color: #fa8c16;
          }
          
          .file-name {
            color: #fa8c16;
            font-weight: 500;
          }
          
          .file-remove-btn {
            opacity: 0.5;
            cursor: not-allowed;
            
            &:hover {
              background: rgba(0, 0, 0, 0.06);
              color: rgba(0, 0, 0, 0.45);
            }
          }
        }
        
        &.error {
          background: #fff2f0;
          border-color: #ffccc7;
          color: #ff4d4f;
        }
        
        .file-icon {
          color: #1890ff;
          flex-shrink: 0;
        }
        
        .file-name {
          flex: 1;
          min-width: 0;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          font-weight: 500;
          
          &.clickable {
            cursor: pointer;
            color: #1890ff;
            transition: color 0.3s ease;
            
            &:hover {
              color: #40a9ff;
              text-decoration: underline;
            }
          }
        }
        
        .file-preview-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 16px;
          height: 16px;
          border: none;
          background: rgba(24, 144, 255, 0.1);
          color: #1890ff;
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.3s ease;
          flex-shrink: 0;
          
          &:hover {
            background: #1890ff;
            color: #fff;
          }
        }
        
        .file-remove-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 16px;
          height: 16px;
          border: none;
          background: rgba(0, 0, 0, 0.06);
          color: rgba(0, 0, 0, 0.45);
          border-radius: 50%;
          cursor: pointer;
          transition: all 0.3s ease;
          flex-shrink: 0;
          
          &:hover {
            background: #ff4d4f;
            color: #fff;
          }
        }
      }
    }
    
    // 修复输入框布局，将输入框和按钮放在同一个相对定位的容器中
    .input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
    }
    
    // 调整主输入框的布局
    .main-input-container {
      .input-wrapper {
        .main-input {
          flex: 1;
          height: 60px;
          padding: 0 120px 0 80px;
          border: 1px solid #e8e8e8;
          border-radius: 30px;
          font-size: 16px;
          background: white;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
          transition: all 0.3s ease;
          
          // 当有文件列表时，下方圆角变为0
          &.has-files {
            border-radius: 30px 30px 0 0 !important;
            border-bottom: none !important;
          }
          
          &:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15);
            
            &.has-files {
              border-radius: 30px 30px 0 0 !important;
              border-bottom: none !important;
            }
          }
          
          &::placeholder {
            color: #999;
            font-size: 16px;
          }
          
          &:hover {
            border-color: #40a9ff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            
            &.has-files {
              border-radius: 30px 30px 0 0 !important;
              border-bottom: none !important;
            }
          }
        }
        
        .input-actions {
          position: absolute;
          left: 20px;
          top: 50%;
          transform: translateY(-50%);
          display: flex;
          gap: 4px;
          z-index: 10;
        }
        
        .send-btn {
          position: absolute;
          right: 10px;
          top: 50%;
          transform: translateY(-50%);
          z-index: 10;
        }
      }
    }
    
    // 调整底部输入框的布局
    .chat-input {
      .input-container {
        .input-wrapper {
          .message-input {
            flex: 1;
            height: 60px;
            padding: 0 120px 0 80px;
            border: 1px solid #e8e8e8;
            border-radius: 30px;
            font-size: 16px;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            resize: none;
            
            // 当有文件列表时，下方圆角变为0
            &.has-files {
              border-radius: 30px 30px 0 0 !important;
              border-bottom: none !important;
            }
            
            &:focus {
              outline: none;
              border-color: #1890ff;
              box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15);
              
              &.has-files {
                border-radius: 30px 30px 0 0 !important;
                border-bottom: none !important;
              }
            }
            
            &::placeholder {
              color: #999;
              font-size: 16px;
            }
            
            &:hover {
              border-color: #40a9ff;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
              
              &.has-files {
                border-radius: 30px 30px 0 0 !important;
                border-bottom: none !important;
              }
            }
            
            &:disabled {
              background: #f5f5f5;
              color: #999;
              cursor: not-allowed;
            }
          }
          
          .input-actions {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 4px;
            z-index: 10;
            overflow: visible !important;
          }
          
          .send-btn {
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
          }
        }
      }
    }
    
    // 重置上传组件的默认样式
    :deep(.ant-upload) {
      margin: 0 !important;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .upload-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      border: none;
      border-radius: 50%;
      background: transparent;
      cursor: pointer;
      transition: all 0.3s ease;
      color: #666;
      
      &:hover:not(:disabled) {
        background: #f0f0f0;
        color: #1890ff;
        transform: scale(1.1);
      }
      
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }
    
    // 发送按钮的样式
    .send-btn {
      width: 40px;
      height: 40px;
      border: none;
      background: #1890ff;
      color: white;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
      
      &:hover:not(:disabled) {
        background: #40a9ff;
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
      }
      
      &:active {
        transform: scale(0.95);
      }
      
      &:disabled {
        background: #d9d9d9;
        cursor: not-allowed;
        transform: scale(1);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }
  
  // 图片预览弹窗
  .image-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(10px);
    animation: fadeIn 0.3s ease;
    
    .image-preview-container {
      position: relative;
      max-width: 90vw;
      max-height: 90vh;
      
      .close-btn {
        position: absolute;
        top: -50px;
        right: 0;
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        border-radius: 50%;
        color: white;
        font-size: 18px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        
        &:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(1.1);
        }
      }
      
      .image-preview-content {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        
        .preview-image {
          display: block;
          max-width: 80vw;
          max-height: 70vh;
          min-width: 400px;
          min-height: 300px;
          object-fit: contain;
          background: #f5f5f5;
        }
        
        .image-info {
          padding: 16px 20px;
          border-top: 1px solid #f0f0f0;
          display: flex;
          justify-content: space-between;
          align-items: center;
          
          .image-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-right: 16px;
          }
          
          .image-actions {
            display: flex;
            gap: 8px;
            
            .action-btn {
              padding: 6px 8px;
              border: 1px solid #d9d9d9;
              border-radius: 6px;
              background: white;
              color: #666;
              cursor: pointer;
              transition: all 0.2s ease;
              
              &:hover {
                background: #f0f0f0;
                border-color: #1890ff;
                color: #1890ff;
              }
            }
          }
                }
      }
    }
  }
  
  // 滚动到底部按钮
  .scroll-to-bottom-btn {
    position: fixed;
    top: 50%;
    right: 30px;
    transform: translateY(-50%);
    width: 52px;
    height: 52px;
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    color: white;
    border: none;
    border-radius: 50%;
    box-shadow: 
      0 8px 24px rgba(24, 144, 255, 0.35),
      0 4px 12px rgba(24, 144, 255, 0.25),
      0 2px 6px rgba(24, 144, 255, 0.15);
    cursor: pointer;
    z-index: 100;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    animation: floatIn 0.5s ease, floatAnimation 3s ease-in-out infinite;
    backdrop-filter: blur(10px);
    
    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(135deg, #1890ff, #40a9ff, #69c0ff);
      border-radius: 50%;
      opacity: 0;
      z-index: -1;
      transition: opacity 0.3s ease;
    }
    
    &:hover {
      background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
      transform: translateY(-50%) translateX(-2px) scale(1.08);
      box-shadow: 
        0 12px 32px rgba(24, 144, 255, 0.4),
        0 8px 20px rgba(24, 144, 255, 0.3),
        0 4px 12px rgba(24, 144, 255, 0.2);
      
      &::before {
        opacity: 0.3;
      }
    }
    
    &:active {
      transform: translateY(-50%) translateX(0) scale(0.96);
      transition: all 0.15s ease;
    }
  }


// 动画效果
@keyframes blink {
  0%, 90%, 100% {
    opacity: 1;
  }
  95% {
    opacity: 0;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 8px rgba(79, 172, 254, 0.8);
  }
  50% {
    transform: scale(1.3);
    box-shadow: 0 0 20px rgba(79, 172, 254, 1), 0 0 40px rgba(0, 242, 254, 0.6);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes floatIn {
  0% {
    opacity: 0;
    transform: translateY(-50%) translateX(10px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(-50%) translateX(0) scale(1);
  }
}

@keyframes floatAnimation {
  0%, 100% {
    transform: translateY(-50%) translateX(0);
  }
  50% {
    transform: translateY(-50%) translateX(-3px);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes typingPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .knowledge-graph-chat {
    
    .chat-main {
      .chat-header {
        padding: 12px 16px;
        flex-wrap: wrap;
        
        .header-left {
          flex-wrap: wrap;
          
          .toggle-sidebar-btn,
          .new-chat-btn {
            width: 32px;
            height: 32px;
          }
          
          .chat-title {
            font-size: 16px;
          }
        }
        
        .header-right {
          .collection-selector {
            .ant-select {
              width: 150px;
            }
          }
        }
      }
      
      .chat-content {
        .messages-container {
          padding: 16px;
          
          .messages-wrapper {
            .message-wrapper {
              max-width: 100%; // 移动端占满宽度
              padding: 0 8px; // 添加一些左右内边距
            }
          }
          
          .welcome-message,
          .typing-indicator {
            max-width: 100%; // 移动端占满宽度
            padding: 0 8px; // 添加一些左右内边距
          }
        }
        
        .chat-input {
          padding: 12px 16px;
        }
      }
    }
  }
  
  .image-preview-modal {
    .image-preview-container {
      .close-btn {
        top: -40px;
        right: 10px;
      }
      
      .image-preview-content {
        .preview-image {
          min-width: 300px;
          min-height: 200px;
          max-width: 90vw;
          max-height: 60vh;
        }
        
        .image-info {
          padding: 12px 16px;
          
          .image-title {
            font-size: 13px;
          }
          
          .image-actions {
            .action-btn {
              padding: 5px 6px;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

// 模态框过渡动画 - 平滑无震荡效果
.source-modal-enter-active {
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

.source-modal-leave-active {
  transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.source-modal-enter-from {
  opacity: 0;
  transform: scale3d(0.85, 0.85, 1);
}

.source-modal-leave-to {
  opacity: 0;
  transform: scale3d(0.95, 0.95, 1);
}

// 图片预览过渡动画 - 平滑无震荡效果
.image-preview-enter-active {
  transition: all 0.35s cubic-bezier(0.16, 1, 0.3, 1);
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

.image-preview-leave-active {
  transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.image-preview-enter-from {
  opacity: 0;
  transform: scale3d(0.9, 0.9, 1);
}

.image-preview-leave-to {
  opacity: 0;
  transform: scale3d(0.95, 0.95, 1);
}

// 展开源码过渡动画 - 平滑无震荡效果
.expanded-source-enter-active {
  transition: all 0.4s cubic-bezier(0.16, 1, 0.3, 1);
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

.expanded-source-leave-active {
  transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  will-change: transform, opacity;
  backface-visibility: hidden;
}

.expanded-source-enter-from {
  opacity: 0;
  transform: scale3d(0.88, 0.88, 1) translateY(20px);
}

.expanded-source-leave-to {
  opacity: 0;
  transform: scale3d(0.95, 0.95, 1) translateY(-10px);
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  transition: background 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:hover {
    background: #a1a1a1;
  }
}

// 暗黑模式滚动条样式
[data-theme='dark'] {
  ::-webkit-scrollbar-track {
    background: #1a1a1a;
  }

  ::-webkit-scrollbar-thumb {
    background: #404040;

    &:hover {
      background: #555555;
    }
  }
}

// 知识片段模态框样式
.source-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
  animation: fadeInOverlay 0.3s ease-out;
}

@keyframes fadeInOverlay {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(4px);
  }
}

@keyframes fadeInContent {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.source-modal-container {
  position: relative;
  
  &.expanded {
    .source-modal {
      width: 98vw;
      height: 98vh;
      max-width: none;
      max-height: none;
    }
  }
  
  .source-modal {
    background: white;
    border-radius: 12px;
    width: 1000px;
    height: 700px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.25);
    border: 1px solid #e8e8e8;
    
    .modal-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 24px;
      border-bottom: 1px solid #f0f0f0;
      background: #fafafa;
      animation: fadeInContent 0.25s ease-out 0.1s both;
      
      .modal-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        
        .anticon {
          color: #1890ff;
        }
      }
      
      .header-actions {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .expand-btn,
        .close-btn {
          background: none;
          border: none;
          cursor: pointer;
          padding: 8px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          will-change: transform, background-color;
          
          &:hover {
            background: #f0f0f0;
            transform: scale(1.05);
          }
          
          &:active {
            transform: scale(0.98);
          }
          
          .anticon {
            font-size: 16px;
            color: #666;
            transition: color 0.2s;
          }
        }
        
        .expand-btn:hover .anticon {
          color: #1890ff;
        }
      }
    }
    
    .modal-content {
      overflow-y: auto;
      height: calc(100% - 80px);
      animation: fadeInContent 0.3s ease-out 0.15s both;
      
      // 单个数据源样式
      .single-source {
        padding: 24px;
      }
      
      // 多个数据源Tab样式
      .multiple-sources {
        display: flex;
        
        .tab-nav {
          display: flex;
          flex-direction: column;
          border-right: 1px solid #f0f0f0;
          background: #fafafa;
          min-width: 200px;
          max-width: 250px;
          
          .tab-btn {
            display: flex;
            align-items: center;
            transition: all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            will-change: transform, background-color;
            gap: 8px;
            padding: 16px 20px;
            background: none;
            border: none;
            cursor: pointer;
            color: #666;
            font-size: 14px;
            white-space: nowrap;
            border-left: 3px solid transparent;
            text-align: left;
            
            &:hover {
              color: #1890ff;
              background: rgba(24, 144, 255, 0.08);
              transform: translateX(1px);
            }
            
            &.active {
              color: #1890ff;
              border-left-color: #1890ff;
              background: white;
              transform: translateX(1px);
            }
            
            .anticon {
              font-size: 16px;
              flex-shrink: 0;
            }
            
            span {
              flex: 1;
            }
            
            .tab-type {
              font-size: 12px;
              color: #999;
              background: #f0f0f0;
              padding: 2px 6px;
              border-radius: 4px;
              flex-shrink: 0;
            }
            
            &.active .tab-type {
              background: #e8f4ff;
              color: #1890ff;
            }
          }
        }
        
        .tab-content {
          flex: 1;
          
          .tab-pane {
            padding: 24px;
            height: 100%;
          }
        }
      }
      
      // 通用数据源项样式
      .source-item {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 16px;
        border: 1px solid #e8e8e8;
        
        .source-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;
          
          .source-icon {
            font-size: 18px;
            color: #666;
          }
          
          .source-info {
            flex: 1;
            
            .source-name {
              display: block;
              font-weight: 500;
              font-size: 14px;
              color: #333;
              margin-bottom: 4px;
            }
            
            .source-type {
              display: inline-block;
              font-size: 12px;
              color: #999;
              background: #f0f0f0;
              padding: 2px 8px;
              border-radius: 4px;
            }
          }
        }
        
        .source-content {
          position: relative;
          
          .source-text {
            background: white;
            border-radius: 6px;
            padding: 16px;
            border: 1px solid #e8e8e8;
            max-height: 400px;
            overflow-y: auto;
            
            .markdown-content {
              font-size: 14px;
              line-height: 1.6;
              color: #333;
              
              // Markdown样式
              :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
                margin-top: 16px;
                margin-bottom: 12px;
                font-weight: 600;
                color: #333;
              }
              
              :deep(p) {
                margin-bottom: 12px;
              }
              
              :deep(code) {
                background: #f6f8fa;
                padding: 2px 4px;
                border-radius: 3px;
                font-family: 'SFMono-Regular', Consolas, monospace;
                font-size: 0.9em;
              }
              
              :deep(pre) {
                background: #f6f8fa;
                padding: 12px;
                border-radius: 6px;
                overflow-x: auto;
                margin: 12px 0;
                
                code {
                  background: none;
                  padding: 0;
                }
              }
              
              :deep(ul), :deep(ol) {
                padding-left: 20px;
                margin: 12px 0;
              }
              
              :deep(li) {
                margin: 4px 0;
              }
              
              :deep(blockquote) {
                border-left: 4px solid #e8e8e8;
                padding-left: 12px;
                margin: 12px 0;
                color: #666;
              }
              
              :deep(a) {
                color: #1890ff;
                text-decoration: none;
                
                &:hover {
                  text-decoration: underline;
                }
              }
            }
          }
          
          .source-actions {
            position: absolute;
            top: 8px;
            right: 8px;
            display: flex;
            gap: 4px;
            
            .action-btn {
              background: white;
              border: 1px solid #d9d9d9;
              border-radius: 4px;
              padding: 6px 8px;
              cursor: pointer;
              transition: all 0.2s ease-out;
              
              &:hover {
                background: #f0f0f0;
                border-color: #1890ff;
                color: #1890ff;
              }
              
              .anticon {
                font-size: 14px;
              }
            }
          }
        }
        
        .source-image-container {
          position: relative;
          display: inline-block;
          border-radius: 8px;
          overflow: hidden;
          
          .source-image {
            display: block;
            transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            
            &:hover {
              transform: scale(1.01);
            }
          }
        }
        
        // 类型特定样式
        &.entity {
          border-left: 4px solid #52c41a;
        }
        
        &.chunk {
          border-left: 4px solid #1890ff;
        }
        
        &.text {
          border-left: 4px solid #722ed1;
        }
        
        &.image {
          border-left: 4px solid #fa8c16;
        }
      }
    }
  }
}

// 知识片段图片预览模态框
.source-image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeInOverlay 0.3s ease;
  
  .image-preview-container {
    position: relative;
    max-width: 90%;
    max-height: 90%;
    
    .close-btn {
      position: absolute;
      top: 20px;
      right: 20px;
      background: rgba(255, 255, 255, 0.9);
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1101;
      
      &:hover {
        background: white;
      }
      
      .anticon {
        font-size: 18px;
        color: #333;
      }
    }
    
    .image-preview-content {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
      
      .preview-image {
        max-width: 100%;
        max-height: 70vh;
        display: block;
      }
      
      .image-info {
        padding: 16px;
        
        .image-title {
          font-size: 14px;
          font-weight: 500;
          color: #333;
          text-align: center;
        }
      }
    }
  }
}

// 知识片段展开源码模态框
.expanded-source-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  backdrop-filter: blur(4px);
  padding: 20px;
  animation: fadeInOverlay 0.3s ease;
  
  .expanded-source-container {
    background: white;
    border-radius: 12px;
    max-width: 90%;
    max-height: 90%;
    width: 1000px;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;
    
    .expanded-source-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 24px;
      border-bottom: 1px solid #f0f0f0;
      background: #fafafa;
      
      .expanded-source-title {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        
        .anticon {
          color: #1890ff;
        }
      }
      
      .close-btn {
        background: none;
        border: none;
        cursor: pointer;
        padding: 8px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.2s;
        
        &:hover {
          background: #f0f0f0;
        }
        
        .anticon {
          font-size: 16px;
          color: #666;
        }
      }
    }
    
    .expanded-source-content {
      flex: 1;
      padding: 24px;
      overflow-y: auto;
      
      .expanded-markdown-content {
        font-size: 14px;
        line-height: 1.6;
        color: #333;
        
        // 完整的Markdown样式
        :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
          margin-top: 24px;
          margin-bottom: 16px;
          font-weight: 600;
          color: #333;
        }
        
        :deep(h1) {
          font-size: 2em;
          border-bottom: 1px solid #e1e4e8;
          padding-bottom: 0.3em;
        }
        
        :deep(h2) {
          font-size: 1.5em;
          border-bottom: 1px solid #e1e4e8;
          padding-bottom: 0.3em;
        }
        
        :deep(p) {
          margin-bottom: 16px;
        }
        
        :deep(code) {
          background: #f6f8fa;
          padding: 2px 4px;
          border-radius: 3px;
          font-family: 'SFMono-Regular', Consolas, monospace;
          font-size: 0.9em;
        }
        
        :deep(pre) {
          background: #f6f8fa;
          padding: 16px;
          border-radius: 6px;
          overflow-x: auto;
          margin: 16px 0;
          
          code {
            background: none;
            padding: 0;
          }
        }
        
        :deep(ul), :deep(ol) {
          padding-left: 2em;
          margin: 16px 0;
        }
        
        :deep(li) {
          margin: 8px 0;
        }
        
        :deep(blockquote) {
          border-left: 4px solid #e1e4e8;
          padding-left: 16px;
          margin: 16px 0;
          color: #666;
        }
        
        :deep(table) {
          border-collapse: collapse;
          width: 100%;
          margin: 16px 0;
          
          th, td {
            border: 1px solid #e1e4e8;
            padding: 8px 12px;
            text-align: left;
          }
          
          th {
            background: #f6f8fa;
            font-weight: 600;
          }
        }
        
        :deep(a) {
          color: #1890ff;
          text-decoration: none;
          
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
    
    .expanded-source-actions {
      padding: 16px 24px;
      border-top: 1px solid #f0f0f0;
      background: #fafafa;
      display: flex;
      justify-content: flex-end;
      
      .action-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        background: #1890ff;
        color: white;
        border: none;
        border-radius: 6px;
        padding: 8px 16px;
        cursor: pointer;
        transition: all 0.2s;
        
        &:hover {
          background: #40a9ff;
        }
        
        .anticon {
          font-size: 14px;
        }
      }
    }
  }
}

/* 暗黑模式样式 */
[data-theme='dark'] .knowledge-graph-chat {
  background: #141414;

  .chat-main {
    background: #1f1f1f;

    .chat-header {
      background: linear-gradient(135deg, #1f1f1f 0%, #262626 100%);
      border-bottom-color: #303030;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);

      &::before {
        background: radial-gradient(circle, rgba(24, 144, 255, 0.1) 0%, transparent 60%);
      }

      .header-left {
        .toggle-sidebar-btn,
        .new-chat-btn {
          background: #262626;
          border-color: #404040;
          color: rgba(255, 255, 255, 0.65);

          &:hover {
            border-color: #1890ff;
            color: #1890ff;
            background: #303030;
          }
        }

        .new-chat-btn {
          border-color: #1890ff;
          color: #1890ff;

          &:hover {
            background: rgba(24, 144, 255, 0.1);
          }
        }

        .chat-title {
          color: rgba(255, 255, 255, 0.85);
        }

        .chat-status {
          &.connected {
            background: rgba(82, 196, 26, 0.15);
            color: #73d13d;
          }

          &.typing {
            background: rgba(250, 140, 22, 0.15);
            color: #ffa940;
          }

          &.disconnected {
            background: rgba(255, 77, 79, 0.15);
            color: #ff7875;
          }
        }
      }

      .header-right {
        .collection-selector {
          .ant-select {
            .ant-select-selector {
              background: #262626;
              border-color: #404040;
              color: rgba(255, 255, 255, 0.85);

              &:hover {
                border-color: #1890ff;
              }
            }
          }
        }
      }

      .header-actions {
        .action-btn {
          background: #262626;
          border-color: #404040;
          color: rgba(255, 255, 255, 0.65);

          &:hover {
            border-color: #1890ff;
            color: #1890ff;
            background: #303030;
          }

          &.cancel-btn {
            border-color: #ff4d4f;
            color: #ff4d4f;

            &:hover:not(:disabled) {
              border-color: #ff7875;
              color: #ff7875;
              background: rgba(255, 77, 79, 0.1);
            }
          }
        }
      }
    }

    .chat-content {
      .messages-container {
        background: #1f1f1f;

        &::-webkit-scrollbar-thumb {
          background: rgba(255, 255, 255, 0.2);

          &:hover {
            background: rgba(255, 255, 255, 0.3);
          }
        }

        .welcome-message {
          .welcome-card {
            h3 {
              color: rgba(255, 255, 255, 0.85);
            }

            .main-input-container {
              .main-input {
                background: #262626;
                border-color: #404040;
                color: rgba(255, 255, 255, 0.85);

                &:focus {
                  border-color: #1890ff;
                  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15);
                }

                &::placeholder {
                  color: rgba(255, 255, 255, 0.45);
                }

                &:hover {
                  border-color: #40a9ff;
                  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
                }
              }

              .input-actions {
                .action-btn {
                  &:hover {
                    background: #303030;
                  }

                  .anticon {
                    color: rgba(255, 255, 255, 0.65);
                  }
                }
              }
            }

            .quick-questions {
              .quick-question {
                background: #262626;
                border-color: #404040;
                color: rgba(255, 255, 255, 0.85);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);

                &:hover {
                  border-color: #1890ff;
                  background: rgba(24, 144, 255, 0.1);
                  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.15);
                }

                .anticon {
                  color: #1890ff;
                }
              }
            }
          }
        }

        .typing-indicator {
          .typing-bubble {
            background: linear-gradient(135deg, #262626 0%, #303030 100%);
            border-color: rgba(255, 255, 255, 0.1);

            .typing-text {
              color: rgba(255, 255, 255, 0.65);
            }
          }
        }
      }

      .chat-input {
        background: #1f1f1f;
        border-top-color: #303030;

        .input-container {
          .message-input {
            background: #262626;
            border-color: #404040;
            color: rgba(255, 255, 255, 0.85);

            &:focus {
              border-color: #1890ff;
              box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15);
            }

            &::placeholder {
              color: rgba(255, 255, 255, 0.45);
            }

            &:hover {
              border-color: #40a9ff;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }

            &:disabled {
              background: #1a1a1a;
              color: rgba(255, 255, 255, 0.25);
            }
          }

          .input-actions {
            .action-btn {
              &:hover {
                background: #303030;
              }

              .anticon {
                color: rgba(255, 255, 255, 0.65);
              }
            }
          }
        }
      }
    }
  }

  // 文件上传相关暗黑模式样式
  .uploaded-files-preview {
    background: #262626;
    border-color: #404040;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

    .file-tag {
      background: #1a1a1a;
      border-color: #404040;
      color: rgba(255, 255, 255, 0.85);

      &:hover {
        background: rgba(24, 144, 255, 0.1);
        border-color: #40a9ff;
      }

      &.uploading {
        background: rgba(24, 144, 255, 0.1);
        border-color: #40a9ff;
        color: #1890ff;
      }

      &.converting {
        background: rgba(250, 140, 22, 0.1);
        border-color: #ffad12;
        color: #fa8c16;

        .file-icon {
          color: #fa8c16;
        }

        .file-name {
          color: #fa8c16;
        }

        .file-remove-btn {
          background: rgba(255, 255, 255, 0.06);
          color: rgba(255, 255, 255, 0.45);

          &:hover {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.65);
          }
        }
      }

      &.error {
        background: rgba(255, 77, 79, 0.1);
        border-color: #ffccc7;
        color: #ff4d4f;
      }

      .file-icon {
        color: #1890ff;
      }

      .file-name {
        &.clickable {
          color: #1890ff;

          &:hover {
            color: #40a9ff;
          }
        }
      }

      .file-preview-btn {
        background: rgba(24, 144, 255, 0.15);
        color: #1890ff;

        &:hover {
          background: #1890ff;
          color: #fff;
        }
      }

      .file-remove-btn {
        background: rgba(255, 255, 255, 0.06);
        color: rgba(255, 255, 255, 0.45);

        &:hover {
          background: #ff4d4f;
          color: #fff;
        }
      }
    }
  }

  .upload-btn {
    color: rgba(255, 255, 255, 0.65);

    &:hover:not(:disabled) {
      background: #303030;
      color: #1890ff;
    }
  }

  // 图片预览模态框暗黑模式
  .image-preview-modal {
    background: rgba(0, 0, 0, 0.9);

    .image-preview-container {
      .close-btn {
        background: rgba(255, 255, 255, 0.15);
        color: white;

        &:hover {
          background: rgba(255, 255, 255, 0.25);
        }
      }

      .image-preview-content {
        background: #262626;

        .preview-image {
          background: #1a1a1a;
        }

        .image-info {
          border-top-color: #404040;

          .image-title {
            color: rgba(255, 255, 255, 0.85);
          }

          .image-actions {
            .action-btn {
              background: #1a1a1a;
              border-color: #404040;
              color: rgba(255, 255, 255, 0.65);

              &:hover {
                background: #303030;
                border-color: #1890ff;
                color: #1890ff;
              }
            }
          }
        }
      }
    }
  }

  // 滚动到底部按钮暗黑模式
  .scroll-to-bottom-btn {
    background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
    box-shadow:
      0 8px 24px rgba(24, 144, 255, 0.35),
      0 4px 12px rgba(24, 144, 255, 0.25),
      0 2px 6px rgba(24, 144, 255, 0.15);

    &:hover {
      background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
      box-shadow:
        0 12px 32px rgba(24, 144, 255, 0.4),
        0 8px 20px rgba(24, 144, 255, 0.3),
        0 4px 12px rgba(24, 144, 255, 0.2);
    }
  }

  // 知识片段模态框暗黑模式
  .source-modal-overlay {
    background: rgba(0, 0, 0, 0.7);
  }

  .source-modal-container {
    .source-modal {
      background: #1f1f1f;
      border-color: #404040;

      .modal-header {
        background: #262626;
        border-bottom-color: #404040;

        .modal-title {
          color: rgba(255, 255, 255, 0.85);

          .anticon {
            color: #1890ff;
          }
        }

        .header-actions {
          .expand-btn,
          .close-btn {
            &:hover {
              background: #303030;
            }

            .anticon {
              color: rgba(255, 255, 255, 0.65);
            }
          }

          .expand-btn:hover .anticon {
            color: #1890ff;
          }
        }
      }

      .modal-content {
        .single-source {
          // 单个数据源暗黑模式样式在下面统一处理
        }

        .multiple-sources {
          .tab-nav {
            background: #262626;
            border-right-color: #404040;

            .tab-btn {
              color: rgba(255, 255, 255, 0.65);
              border-left-color: transparent;

              &:hover {
                color: #1890ff;
                background: rgba(24, 144, 255, 0.1);
              }

              &.active {
                color: #1890ff;
                border-left-color: #1890ff;
                background: #1f1f1f;
              }

              .tab-type {
                background: #1a1a1a;
                color: rgba(255, 255, 255, 0.45);
              }

              &.active .tab-type {
                background: rgba(24, 144, 255, 0.15);
                color: #1890ff;
              }
            }
          }

          .tab-content {
            .tab-pane {
              // 内容区域暗黑模式样式在下面统一处理
            }
          }
        }

        // 通用数据源项暗黑模式样式
        .source-item {
          background: #262626;
          border-color: #404040;

          .source-header {
            .source-icon {
              color: rgba(255, 255, 255, 0.65);
            }

            .source-info {
              .source-name {
                color: rgba(255, 255, 255, 0.85);
              }

              .source-type {
                background: #1a1a1a;
                color: rgba(255, 255, 255, 0.45);
              }
            }
          }

          .source-content {
            .source-text {
              background: #1f1f1f;
              border-color: #404040;

              .markdown-content {
                color: rgba(255, 255, 255, 0.85);

                :deep(h1), :deep(h2), :deep(h3), :deep(h4), :deep(h5), :deep(h6) {
                  color: rgba(255, 255, 255, 0.85);
                }

                :deep(code) {
                  background: #1a1a1a;
                  color: rgba(255, 255, 255, 0.85);
                }

                :deep(pre) {
                  background: #1a1a1a;

                  code {
                    background: none;
                  }
                }

                :deep(blockquote) {
                  border-left-color: #404040;
                  color: rgba(255, 255, 255, 0.65);
                }

                :deep(a) {
                  color: #1890ff;

                  &:hover {
                    color: #40a9ff;
                  }
                }
              }
            }

            .source-actions {
              .action-btn {
                background: #262626;
                border-color: #404040;
                color: rgba(255, 255, 255, 0.65);

                &:hover {
                  background: #303030;
                  border-color: #1890ff;
                  color: #1890ff;
                }
              }
            }
          }

          // 类型特定暗黑模式样式
          &.entity {
            border-left-color: #52c41a;
          }

          &.chunk {
            border-left-color: #1890ff;
          }

          &.text {
            border-left-color: #722ed1;
          }

          &.image {
            border-left-color: #fa8c16;
          }
        }
      }
    }
  }

  // 知识片段图片预览模态框暗黑模式
  .source-image-preview-modal {
    background: rgba(0, 0, 0, 0.9);

    .image-preview-container {
      .close-btn {
        background: rgba(255, 255, 255, 0.15);

        &:hover {
          background: rgba(255, 255, 255, 0.25);
        }

        .anticon {
          color: white;
        }
      }

      .image-preview-content {
        background: #262626;

        .image-info {
          border-top-color: #404040;

          .image-title {
            color: rgba(255, 255, 255, 0.85);
          }
        }
      }
    }
  }

  // 展开源码模态框暗黑模式
  .expanded-source-modal {
    background: rgba(0, 0, 0, 0.7);

    .expanded-source-container {
      background: #1f1f1f;

      .expanded-source-header {
        background: #262626;
        border-bottom-color: #404040;

        .expanded-source-title {
          color: rgba(255, 255, 255, 0.85);

          .anticon {
            color: #1890ff;
          }
        }

        .close-btn {
          &:hover {
            background: #303030;
          }

          .anticon {
            color: rgba(255, 255, 255, 0.65);
          }
        }
      }

      .expanded-source-content {
        background: #1f1f1f;
        color: rgba(255, 255, 255, 0.85);

        // 这里的内容样式会继承上面的source-item样式
      }
    }
  }
}
