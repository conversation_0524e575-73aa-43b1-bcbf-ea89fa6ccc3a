<!--手动录入text-->
<template>
  <div class="p-2">
    <BasicModal destroyOnClose @register="registerModal" width="600px" :title="title" @ok="handleOk" @cancel="handleCancel">
      <BasicForm @register="registerForm"></BasicForm>
    </BasicModal>
  </div>
</template>

<script lang="ts">
  import { ref, unref } from 'vue';
  import BasicModal from '@/components/Modal/src/BasicModal.vue';
  import { useModal, useModalInner } from '@/components/Modal';

  import BasicForm from '@/components/Form/src/BasicForm.vue';
  import { useForm } from '@/components/Form';
  import { docTextSchema } from '../AiKnowledgeBase.data';
  import { knowledgeSaveDoc, queryById } from '../AiKnowledgeBase.api';
  import { useMessage } from '/@/hooks/web/useMessage';

  export default {
    name: 'AiragKnowledgeDocModal',
    components: {
      BasicForm,
      BasicModal,
    },
    emits: ['success', 'register'],
    setup(props, { emit }) {
      const title = ref<string>('创建知识库');

      //保存或修改
      const isUpdate = ref<boolean>(false);
      //知识库id
      const knowledgeId = ref<string>();
      //表单配置
      const [registerForm, { resetFields, setFieldsValue, validate, clearValidate, updateSchema }] = useForm({
        schemas: docTextSchema,
        showActionButtonGroup: false,
        layout: 'vertical',
        wrapperCol: { span: 24 },
      });

      //注册modal
      const [registerModal, { closeModal, setModalProps }] = useModalInner(async (data) => {
        //重置表单
        await resetFields();
        setModalProps({ confirmLoading: false });
        isUpdate.value = !!data?.isUpdate;
        title.value = isUpdate.value ? '编辑文档' : '创建文档';
        if (unref(isUpdate)) {
          if(data.record.type === 'file' && data.record.metadata){
            try {
              data.record.filePath = JSON.parse(data.record.metadata).filePath;
            } catch (e) {
              console.error('metadata解析错误:', e);
              // 如果解析失败但metadata是字符串格式，直接设置为filePath
              data.record.filePath = data.record.metadata;
            }
          }
          //表单赋值
          await setFieldsValue({
            ...data.record,
          });
        } else {
          knowledgeId.value = data.knowledgeId;
          await setFieldsValue({ type: data.type })
        }
        setModalProps({ bodyStyle: { padding: '10px' } });
      });

      /**
       * 保存
       */
      async function handleOk() {
        try {
          setModalProps({ confirmLoading: true });
          let values = await validate();
          if (!unref(isUpdate)) {
            values.knowledgeId = knowledgeId.value;
          }
          //values.metadata = JSON.stringify(
          if(values.filePath){
            try {
              const filePathObj = JSON.parse(values.filePath);
              values.metadata = JSON.stringify({ filePath: filePathObj[0].filePath });
              values.fileSize = filePathObj[0].fileSize;
            } catch (e) {
              console.error('filePath解析错误:', e);
              // 如果解析失败，直接使用filePath值
              values.metadata = JSON.stringify({ filePath: values.filePath });
            }
            delete values.filePath;
          }
          await knowledgeSaveDoc(values);
          //关闭弹窗
          closeModal();
          //刷新列表
          emit('success');
        } finally {
          setModalProps({ confirmLoading: false });
        }
      }

      /**
       * 取消
       */
      function handleCancel() {
        closeModal();
      }

      return {
        registerModal,
        registerForm,
        title,
        handleOk,
        handleCancel,
      };
    },
  };
</script>

<style scoped lang="less">
  .pointer {
    cursor: pointer;
  }

  /* 暗黑模式适配 */
  [data-theme='dark'] {
    .p-2 {
      :deep(.ant-modal-content) {
        background: #1f1f1f !important;
        color: rgba(255, 255, 255, 0.85) !important;
      }

      :deep(.ant-modal-header) {
        background: #1f1f1f !important;
        border-bottom-color: #404040 !important;

        .ant-modal-title {
          color: rgba(255, 255, 255, 0.85) !important;
        }
      }

      :deep(.ant-modal-close) {
        color: rgba(255, 255, 255, 0.45) !important;

        &:hover {
          color: rgba(255, 255, 255, 0.85) !important;
        }
      }

      :deep(.ant-modal-footer) {
        background: #1f1f1f !important;
        border-top-color: #404040 !important;
      }

      :deep(.ant-form) {
        .ant-form-item-label > label {
          color: rgba(255, 255, 255, 0.85) !important;
        }

        .ant-input,
        .ant-input-affix-wrapper {
          background: #262626 !important;
          border-color: #404040 !important;
          color: rgba(255, 255, 255, 0.85) !important;

          &:hover {
            border-color: #1890ff !important;
          }

          &:focus {
            border-color: #1890ff !important;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
          }

          &::placeholder {
            color: rgba(255, 255, 255, 0.45) !important;
          }
        }

        .ant-input.ant-input-textarea {
          background: #262626 !important;
          border-color: #404040 !important;
          color: rgba(255, 255, 255, 0.85) !important;

          &:hover {
            border-color: #1890ff !important;
          }

          &:focus {
            border-color: #1890ff !important;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
          }

          &::placeholder {
            color: rgba(255, 255, 255, 0.45) !important;
          }
        }

        .ant-select {
          .ant-select-selector {
            background: #262626 !important;
            border-color: #404040 !important;
            color: rgba(255, 255, 255, 0.85) !important;

            &:hover {
              border-color: #1890ff !important;
            }
          }

          &.ant-select-focused .ant-select-selector {
            border-color: #1890ff !important;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2) !important;
          }

          .ant-select-selection-placeholder {
            color: rgba(255, 255, 255, 0.45) !important;
          }

          .ant-select-selection-item {
            color: rgba(255, 255, 255, 0.85) !important;
          }

          .ant-select-arrow {
            color: rgba(255, 255, 255, 0.45) !important;
          }
        }

        .ant-form-item-explain-error {
          color: #ff4d4f !important;
        }
      }
    }
  }
</style>
