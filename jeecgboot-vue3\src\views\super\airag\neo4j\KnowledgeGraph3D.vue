<!-- 3D知识图谱展示页面 -->
<template>
  <div class="knowledge-graph-container">
    <a-card :bordered="false" class="card-container">
      <a-row :gutter="16">
        <a-col :span="24">
          <div class="search-area">
            <div class="search-field"><span class="search-label">{{ t('neo4j.common.entityName') }}:</span>
              <a-input v-model:value="queryParam.entity_name" :placeholder="t('neo4j.search.entityNamePlaceholder')" allow-clear
                       class="search-input"/>
            </div>
            <div class="search-field"><span class="search-label">{{ t('neo4j.common.entityType') }}:</span>
              <a-select v-model:value="queryParam.entity_type" :placeholder="t('neo4j.search.entityTypePlaceholder')" allow-clear
                        class="search-input">
                <a-select-option v-for="item in entityTypes" :key="item" :value="item">{{
                    item
                  }}
                </a-select-option>
              </a-select>
            </div>
            <div class="search-field"><span class="search-label">{{ t('neo4j.common.relationType') }}:</span>
              <a-select v-model:value="queryParam.relation_type" :placeholder="t('neo4j.search.relationTypePlaceholder')" allow-clear
                        class="search-input">
                <a-select-option v-for="item in relationTypes" :key="item" :value="item">{{
                    item
                  }}
                </a-select-option>
              </a-select>
            </div>
            <div class="search-field"><span class="search-label">{{ t('neo4j.common.nodeCount') }}:</span>
              <a-slider v-model:value="queryParam.max_nodes" :min="10" :max="200"
                        class="search-input"/>
            </div>
            <div class="search-field search-buttons">
              <a-button type="primary" @click="searchGraph">{{ t('neo4j.common.query') }}</a-button>
              <a-button style="margin-left: 8px" @click="resetSearch">{{ t('neo4j.common.reset') }}</a-button>
              <a-button style="margin-left: 8px" @click="toggleViewMode">
                {{ is3DMode ? t('neo4j.view.toggle2D') : t('neo4j.view.toggle3D') }}
              </a-button>
            </div>
          </div>
        </a-col>
      </a-row>
      <a-row :gutter="16" class="graph-main-container">
        <a-col :span="24">
          <div class="graph-container" ref="graphContainerRef">
            <!-- 3D视图 -->
            <div v-if="is3DMode" ref="graphRef" class="graph-view"></div>
            <!-- 2D视图 -->
            <KnowledgeGraph2D 
              v-if="!is3DMode" 
              :queryParam="queryParam" 
              @show-entity-detail="showEntityDetail"
              @update-statistics="updateStatistics"
              @fullscreen-change="handle2DFullscreenChange"
              ref="graph2DRef"
            />
            <div v-if="loading" class="loading-container">
              <a-spin :tip="t('neo4j.common.loading')"/>
            </div>
            <div v-if="!loading && (!graphData.nodes || graphData.nodes.length === 0)"
                 class="empty-container">
              <a-empty :description="t('neo4j.common.noData')"/>
            </div>
            <div class="graph-controls"
                 v-if="is3DMode">
              <a-button-group>
                <a-button type="primary" size="small" @click="zoomIn" :title="t('neo4j.controls.zoomIn')">
                  <plus-outlined />
                </a-button>
                <a-button type="primary" size="small" @click="zoomOut" :title="t('neo4j.controls.zoomOut')">
                  <minus-outlined />
                </a-button>
                <a-button type="primary" size="small" @click="resetCamera" :title="t('neo4j.controls.resetView')">
                  <undo-outlined />
                </a-button>
                <a-button type="primary" size="small" @click="toggleFullscreen" :title="t('neo4j.controls.fullscreen')">
                  <fullscreen-outlined v-if="!isFullscreen" />
                  <fullscreen-exit-outlined v-else />
                </a-button>
              </a-button-group>
            </div>
            
            <!-- 3D模式全屏下的详情面板 -->
            <div v-if="is3DMode && isFullscreen && selectedNode" class="node-detail-panel entity-detail-style" :style="detailPanelStyle">
              <div class="node-detail-header entity-detail-header">
                <div class="node-title">
                  <div class="node-type-indicator" :style="{ backgroundColor: getEntityTypeColor(selectedNode.type) }"></div>
                  <h3>{{ selectedNode.name }}</h3>
                </div>
                <a-button type="text" @click="closeNodeDetail">
                  <close-outlined />
                </a-button>
              </div>
              <div class="node-detail-content entity-detail-content">
                <a-descriptions :column="1" size="small">
                  <a-descriptions-item :label="t('neo4j.nodeDetail.nodeId')">{{ selectedNode.id }}</a-descriptions-item>
                  <a-descriptions-item :label="t('neo4j.nodeDetail.nodeName')">{{ selectedNode.name }}</a-descriptions-item>
                  <a-descriptions-item :label="t('neo4j.nodeDetail.nodeType')">
                    <div class="type-with-color">
                      <span class="type-color-marker" :style="{ backgroundColor: getEntityTypeColor(selectedNode.type) }"></span>
                      <span>{{ selectedNode.type }}</span>
                    </div>
                  </a-descriptions-item>
                  <a-descriptions-item v-for="(val, key) in selectedNodeProperties" :key="key" :label="key">
                    {{ val }}
                  </a-descriptions-item>
                </a-descriptions>
                
                <!-- 出入关系部分 -->
                <div class="node-relations">
                  <div v-if="nodeRelations.inbound.length > 0" class="relation-section">
                    <div class="relation-title">{{ t('neo4j.nodeDetail.inboundRelations') }}</div>
                    <ul class="relation-list">
                      <li v-for="(item, index) in nodeRelations.inbound" :key="'in-'+index" class="relation-item">
                        <span class="relation-node" :style="{ backgroundColor: getEntityTypeColor(getNodeType(item.source)) }"></span>
                        <span class="relation-name" @click="selectNodeById(item.source)">{{ getNodeName(item.source) }}</span>
                        <a-tag size="small" color="blue">{{ item.type }}</a-tag>
                        <span class="relation-arrow">→</span>
                        <span class="relation-current">{{ t('neo4j.nodeDetail.currentNode') }}</span>
                      </li>
                    </ul>
                  </div>
                  
                  <div v-if="nodeRelations.outbound.length > 0" class="relation-section">
                    <div class="relation-title">{{ t('neo4j.nodeDetail.outboundRelations') }}</div>
                    <ul class="relation-list">
                      <li v-for="(item, index) in nodeRelations.outbound" :key="'out-'+index" class="relation-item">
                        <span class="relation-current">{{ t('neo4j.nodeDetail.currentNode') }}</span>
                        <span class="relation-arrow">→</span>
                        <a-tag size="small" color="green">{{ item.type }}</a-tag>
                        <span class="relation-name" @click="selectNodeById(item.target)">{{ getNodeName(item.target) }}</span>
                        <span class="relation-node" :style="{ backgroundColor: getEntityTypeColor(getNodeType(item.target)) }"></span>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 统计信息悬浮窗 -->
            <div v-if="(is3DMode || !is2DFullscreen) && graphData.nodes && graphData.nodes.length > 0" class="statistics-floating-panel">
              <div class="statistics-header">{{ t('neo4j.statistics.title') }}</div>
              <div class="statistics-content">
                <div class="statistic-item">
                  <span class="statistic-icon">📊</span>
                  <span class="statistic-label">{{ t('neo4j.statistics.nodes') }}:</span>
                  <span class="statistic-value">{{ graphData.nodes.length }}</span>
                </div>
                <div class="statistic-item">
                  <span class="statistic-icon">🔗</span>
                  <span class="statistic-label">{{ t('neo4j.statistics.relationships') }}:</span>
                  <span class="statistic-value">{{ graphData.links.length }}</span>
                </div>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
    </a-card>    <!-- 悬浮信息窗 -->
    <div v-show="showPopup && entityDetail" class="entity-popup entity-detail-style" :style="popupStyle"
         @mouseenter="cancelPopupClose" @mouseleave="closePopup">
      <div class="entity-popup-header entity-detail-header">
        <div class="node-title">
          <div class="node-type-indicator" :style="{ backgroundColor: getEntityTypeColor(entityDetail?.entity?.type) }"></div>
          <h3>{{ entityDetail?.entity?.name }}</h3>
        </div>
        <a-button type="text" @click="forceClosePopup">
          <close-outlined/>
        </a-button>
      </div>
      <div class="node-detail-content entity-detail-content">
        <a-descriptions :column="1" size="small">
          <a-descriptions-item :label="t('neo4j.nodeDetail.nodeId')">{{ entityDetail?.entity?.id }}</a-descriptions-item>
          <a-descriptions-item :label="t('neo4j.nodeDetail.nodeName')">{{ entityDetail?.entity?.name }}</a-descriptions-item>
          <a-descriptions-item :label="t('neo4j.nodeDetail.nodeType')">
            <div class="type-with-color">
              <span class="type-color-marker" :style="{ backgroundColor: getEntityTypeColor(entityDetail?.entity?.type) }"></span>
              <span>{{ entityDetail?.entity?.type }}</span>
            </div>
          </a-descriptions-item>
          <a-descriptions-item v-for="(value, key) in entityProperties" :key="key" :label="key">
            {{ value }}
          </a-descriptions-item>
        </a-descriptions>
        
        <!-- 出入关系部分 -->
        <div class="node-relations">
          <div v-if="entityDetail?.incoming_relations && entityDetail.incoming_relations.length > 0" class="relation-section">
            <div class="relation-title">{{ t('neo4j.nodeDetail.inboundRelations') }}</div>
            <ul class="relation-list">
              <li v-for="(item, index) in entityDetail.incoming_relations" :key="'in-'+index" class="relation-item">
                <span class="relation-node" :style="{ backgroundColor: getEntityTypeColor('') }"></span>
                <span class="relation-name" @click="loadEntityById(item.id)">{{ item.name }}</span>
                <a-tag size="small" color="blue">{{ item.type }}</a-tag>
                <span class="relation-arrow">→</span>
                <span class="relation-current">{{ t('neo4j.nodeDetail.currentNode') }}</span>
              </li>
            </ul>
          </div>
          
          <div v-if="entityDetail?.outgoing_relations && entityDetail.outgoing_relations.length > 0" class="relation-section">
            <div class="relation-title">{{ t('neo4j.nodeDetail.outboundRelations') }}</div>
            <ul class="relation-list">
              <li v-for="(item, index) in entityDetail.outgoing_relations" :key="'out-'+index" class="relation-item">
                <span class="relation-current">{{ t('neo4j.nodeDetail.currentNode') }}</span>
                <span class="relation-arrow">→</span>
                <a-tag size="small" color="green">{{ item.type }}</a-tag>
                <span class="relation-name" @click="loadEntityById(item.id)">{{ item.name }}</span>
                <span class="relation-node" :style="{ backgroundColor: getEntityTypeColor('') }"></span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {defineComponent, onMounted, ref, reactive, watch, nextTick, onBeforeUnmount, computed} from 'vue';
import { useKnowledgeGraph } from './composables/useKnowledgeGraph';
import { useThreeScene } from './composables/useThreeScene';
import { useGraphLayout } from './composables/useGraphLayout';
import { useNodeInteraction } from './composables/useNodeInteraction';
import { useViewControls } from './composables/useViewControls';
import { useRootSetting } from '/@/hooks/setting/useRootSetting';
import { ThemeEnum } from '/@/enums/appEnum';
import {
  CloseOutlined,
  PlusOutlined,
  MinusOutlined,
  UndoOutlined,
  FullscreenOutlined,
  FullscreenExitOutlined,
} from '@ant-design/icons-vue';
import * as THREE from 'three';
import {TweenMax} from 'gsap';
import KnowledgeGraph2D from './KnowledgeGraph2D.vue';

export default defineComponent({
  name: 'KnowledgeGraph3D',
  components: {
    CloseOutlined,
    PlusOutlined,
    MinusOutlined,
    UndoOutlined,
    FullscreenOutlined,
    FullscreenExitOutlined,
    KnowledgeGraph2D
  },
  setup() {
    // 主题检测
    const { getDarkMode } = useRootSetting();
    const isDarkMode = computed(() => getDarkMode.value === ThemeEnum.DARK);

    // 使用抽取的 composable 获取通用的数据状态和业务逻辑
    const {
      loading,
      graphData,
      entityTypes,
      relationTypes,
      entityDetail,
      queryParam,
      selectedNode,
      clickPosition,
      isFullscreen,
      showPopup,
      popupStyle,
      getEntityTypeColor,
      loadGraphData,
      loadTypes,
      loadEntityDetail,
      resetQueryParam,
      getNodeById,
      getNodeType,
      getNodeName,
      getNodeRelations,
      createDetailPanelStyle,
      entityProperties,
      selectedNodeProperties,
      nodeRelations,
      closeNodeDetail,
      setNodeDetail,
      showEntityDetail,
      closePopup,
      cancelPopupClose,
      forceClosePopup,
      createTimerManager,
      createMessage,
      t
    } = useKnowledgeGraph();
    
    const graphRef = ref<HTMLElement | null>(null);
    const graph2DRef = ref<any>(null);
    const graphContainerRef = ref<HTMLElement | null>(null);

    // 跟踪2D组件的全屏状态
    const is2DFullscreen = ref<boolean>(false);

    // 使用composable中的定时器管理器
    const timerManager = createTimerManager();

    // 使用composable中的详情面板功能
    const detailPanelStyle = createDetailPanelStyle(graphRef);

    // 使用Three.js场景管理composable
    const threeScene = useThreeScene();
    
    // 使用图谱布局管理composable
    const graphLayout = useGraphLayout();
    
    // 使用节点交互管理composable
    const nodeInteraction = useNodeInteraction();
    const { highlightedNode } = nodeInteraction;

    // 使用视图控制管理composable
    const viewControls = useViewControls();

    // 创建视图模式管理器
    const viewModeManager = viewControls.createViewModeManager(false, (is3DMode) => {
      // 视图模式变化时的处理逻辑将在后面实现
      handleViewModeChange(is3DMode);
    });
    const { is3DMode, toggleViewMode } = viewModeManager;

    // 创建相机控制功能
    const cameraZoom = viewControls.createCameraZoom(() => threeScene.getCamera());
    const { zoomIn, zoomOut } = cameraZoom;

    const cameraReset = viewControls.createCameraReset(
      () => threeScene.getCamera(),
      () => threeScene.getControls()
    );
    const { resetCamera } = cameraReset;

    // 查询参数已从 composable 中获取

    // 搜索防抖定时器
    const searchTimer = ref<number | null>(null);
    
    // 监听查询参数变化，自动触发搜索（仅在3D模式下）
    watch(queryParam, (newValue, oldValue) => {
      // 只在3D模式下生效
      if (!is3DMode.value) return;
      
      // 清除之前的定时器
      if (searchTimer.value) {
        clearTimeout(searchTimer.value);
        searchTimer.value = null;
      }
      
      // 防抖处理，800ms后执行搜索
      searchTimer.value = window.setTimeout(() => {
        // 确保场景已经初始化
        if (threeScene.getScene()) {
          // 检查是否有任何搜索条件或节点数量发生变化
          const hasSearchCondition = queryParam.entity_name || queryParam.entity_type || queryParam.relation_type;
          const nodeCountChanged = oldValue && newValue.max_nodes !== oldValue.max_nodes;
          
          // 如果有搜索条件或节点数量变化，或者当前已有图谱数据（意味着可以重新查询）
          const shouldTriggerSearch = hasSearchCondition || nodeCountChanged || (graphData.value.nodes && graphData.value.nodes.length > 0);
          
          if (shouldTriggerSearch) {
            // console.log('3D模式自动触发查询:', {
            //   entity_name: queryParam.entity_name,
            //   entity_type: queryParam.entity_type,
            //   relation_type: queryParam.relation_type,
            //   max_nodes: queryParam.max_nodes,
            //   nodeCountChanged,
            //   hasExistingData: graphData.value.nodes && graphData.value.nodes.length > 0
            // });
            searchGraph();
          }
        }
        searchTimer.value = null;
      }, 800);
    }, { deep: true });

    // 监听主题变化，更新3D场景背景
    watch(isDarkMode, () => {
      if (threeScene.isInitialized()) {
        threeScene.updateBackground();
      }
    });

    // 颜色管理和节点查找函数已从 composable 中获取



    // 包装 loadGraphData 以支持3D特定的渲染逻辑
    const load3DGraphData = async () => {
      const result = await loadGraphData();
      if (result) {
        nextTick(() => {
          if (is3DMode.value) {
            // 如果没有数据，先初始化Three.js场景
            if (!threeScene.isInitialized() && graphRef.value) {
              initThreeJS();
            }
            // 然后渲染图谱
            if (result.nodes && result.nodes.length > 0) {
              render3DGraph();
            }
          }
        });
      }
      return result;
    };



    // 通过ID选择节点并显示详情
    const selectNodeById = (nodeId: string) => {
      nodeInteraction.selectNodeById(
        nodeId,
        graphLayout.getNodeObject,
        threeScene.getScene,
        threeScene.getControls,
        threeScene.getCamera,
        threeScene.getOrCreateMaterial,
        getEntityTypeColor,
        setNodeDetail
      );
    };

    // 初始化Three.js场景
    const initThreeJS = () => {
      if (!graphRef.value) return;
      
      // 使用composable初始化场景
      const initialized = threeScene.initThreeJS(
        graphRef.value,
        onMouseMove,
        onMouseClick,
        onMouseDoubleClick
      );
      
      return initialized;
    };

    // 渲染3D图谱
    const render3DGraph = async () => {
      // 确保scene已经初始化
      const scene = threeScene.getScene();
      if (!scene || !graphRef.value) return;
      
      if (!graphData.value.nodes || !graphData.value.nodes.length) return;

      // 使用图谱布局管理器渲染图谱
      await graphLayout.render3DGraph(
        graphData.value,
        scene,
        threeScene.clearGraphObjects,
        threeScene.getOrCreateMaterial,
        getEntityTypeColor
      );
    };











    // 鼠标移动事件处理（高效节流优化）
    const onMouseMove = nodeInteraction.createMouseMoveHandler(
      graphRef,
      threeScene.getRaycaster,
      threeScene.getMouse,
      threeScene.getCamera,
      threeScene.getScene,
      graphLayout.getAllNodeObjects,
      threeScene.getOrCreateMaterial,
      getEntityTypeColor,
      closePopup
    );

    // 鼠标点击事件处理
    const onMouseClick = nodeInteraction.createMouseClickHandler(
      graphRef,
      threeScene.getRaycaster,
      threeScene.getMouse,
      threeScene.getCamera,
      threeScene.getScene,
      graphLayout.getAllNodeObjects,
      timerManager,
      clickPosition,
      isFullscreen,
      setNodeDetail,
      showEntityDetail,
      cancelPopupClose,
      forceClosePopup,
      selectedNode
    );

    // 创建视图模式变化处理器
    const viewModeChangeHandler = viewControls.createViewModeChangeHandler(
      is3DMode,
      graphRef,
      timerManager,
      threeScene,
      graphLayout,
      nodeInteraction,
      initThreeJS,
      load3DGraphData
    );
    const { handleViewModeChange } = viewModeChangeHandler;

    // 创建查询管理器
    const queryManager = viewControls.createQueryManager(
      is3DMode,
      loading,
      graph2DRef,
      graphData,
      threeScene,
      graphLayout,
      load3DGraphData,
      resetQueryParam
    );
    const { searchGraph, resetSearch } = queryManager;

    // 创建实体加载管理器
    const entityLoader = viewControls.createEntityLoader(
      is3DMode,
      selectNodeById,
      graph2DRef,
      showEntityDetail
    );
    const { loadEntityById } = entityLoader;

    // 相机控制功能已通过 composable 提供

    // 鼠标双击事件处理
    const onMouseDoubleClick = nodeInteraction.createMouseDoubleClickHandler(
      graphRef,
      threeScene.getRaycaster,
      threeScene.getMouse,
      threeScene.getCamera,
      threeScene.getScene,
      threeScene.getControls,
      graphLayout.getAllNodeObjects,
      timerManager,
      resetCamera
    );

    // 实体加载功能已通过 composable 提供

    // 创建窗口大小变化处理器（优化防闪烁）
    const resizeHandler = viewControls.createResizeHandler(() => {
      if (!graphRef.value || !is3DMode.value) return;

      // 确保DOM已经更新
      nextTick(() => {
        if (!graphRef.value) return;
        
        const width = graphRef.value.clientWidth;
        const height = graphRef.value.clientHeight;
        
        // 防止无效尺寸
        if (width > 0 && height > 0) {
          threeScene.resizeRenderer(width, height);
        }
      });
    });
    const { addResizeListener, removeResizeListener, handleResize } = resizeHandler;

    // 创建全屏管理器，传入外部的 isFullscreen 状态
    const fullscreenManager = viewControls.createFullscreenManager(graphContainerRef, isFullscreen, (isFullscreenState) => {
      if (is3DMode.value) {
        // 强制重置渲染器尺寸
        if (graphRef.value && graphContainerRef.value && threeScene.isInitialized()) {
          // 退出全屏后强制重置Three.js尺寸
          setTimeout(() => {
            // 确保DOM已更新
            if (!graphRef.value) return;
            
            const width = graphRef.value.clientWidth;
            const height = graphRef.value.clientHeight;
            
            // 防止尺寸异常
            if (width > 0 && height > 0) {
              threeScene.resizeRenderer(width, height);
            }
          }, 300);
        }
      }
    });
    const { toggleFullscreen: toggleFullscreenNew, addFullscreenListeners, removeFullscreenListeners } = fullscreenManager;

    // 组件挂载后加载数据
    onMounted(() => {
      loadTypes();
      if (is3DMode.value) {
        // 先初始化Three.js场景，然后加载数据
        nextTick(() => {
          if (graphRef.value) {
            initThreeJS();
          }
          load3DGraphData();
        });
      }
      // 不需要显式加载2D数据，因为2D组件会自动加载
      
      // 添加事件监听器
      addFullscreenListeners();
      addResizeListener();
    });

    // 组件卸载前清理资源
    onBeforeUnmount(() => {
      // 移除事件监听器
      removeResizeListener();
      removeFullscreenListeners();

      // 清理搜索定时器
      if (searchTimer.value) {
        clearTimeout(searchTimer.value);
        searchTimer.value = null;
      }

      // 清理所有定时器
      timerManager.clearAllTimers();

      // 使用composable清理Three.js资源
      threeScene.dispose();
      
      // 清空对象存储
      graphLayout.clearAllObjects();
      
      // 重置交互状态
      nodeInteraction.resetInteractionState();
    });

    // 更新统计信息
    const updateStatistics = (stats: { nodesCount: number, linksCount: number }) => {
      // 更新graphData的节点和关系数量，不影响实际图谱数据
      if (!is3DMode.value) {
        // 只在2D模式下更新统计信息
        graphData.value = {
          ...graphData.value,
          nodes: Array(stats.nodesCount),
          links: Array(stats.linksCount)
        };
      }
    };

    // 处理2D组件的全屏状态变化
    const handle2DFullscreenChange = (isFullscreen: boolean) => {
      is2DFullscreen.value = isFullscreen;
    };

    return {
      t,
      graphRef,
      graph2DRef,
      loading,
      graphData,
      entityTypes,
      relationTypes,
      queryParam,
      entityDetail,
      showPopup,
      popupStyle,
      is3DMode,
      getEntityTypeColor,
      searchGraph,
      resetSearch,
      showEntityDetail,
      loadEntityById,
      closePopup,
      cancelPopupClose,
      forceClosePopup,
      toggleViewMode,
      zoomIn,
      zoomOut,
      resetCamera,
      updateStatistics,
      graphContainerRef,
      isFullscreen,
      toggleFullscreen: toggleFullscreenNew,
      entityProperties,
      selectedNode,
      selectedNodeProperties,
      nodeRelations,
      detailPanelStyle,
      closeNodeDetail,
      setNodeDetail,
      selectNodeById,
      getNodeType,
      getNodeName,
      clickPosition,
      handle2DFullscreenChange,
      is2DFullscreen
    };
  },
});
</script>

<style scoped lang="less">
@import './styles/KnowledgeGraph3Ds.less';
</style>

<style lang="less">
@import './styles/KnowledgeGraph3Dg.less';
</style>
