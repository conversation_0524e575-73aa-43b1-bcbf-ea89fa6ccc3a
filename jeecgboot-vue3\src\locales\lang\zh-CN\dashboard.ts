export default {
  // 通用
  title: '仪表盘',
  loading: '加载中...',
  
  // 指引按钮
  guide: {
    button: '仪表盘指引',
    welcome: '欢迎使用仪表板',
    description: '让我们一起了解仪表板的各个功能区域 📊',
    
    // 仪表板指引步骤
    steps: {
      aiStats: {
        title: 'AI 统计卡片',
        intro: '这里显示AI相关的核心统计数据，包括对话数量、用户活跃度等关键指标。',
      },
      knowledgeAnalysis: {
        title: '知识库文档分析',
        intro: '展示知识库文档的详细分析，帮助您了解文档的构建状态和分布情况。',
      },
      dataCharts: {
        title: '数据分析图表',
        intro: '这个区域包含多个数据可视化图表，提供全面的数据分析视图。',
      },
      docDistribution: {
        title: '文档分布图',
        intro: '显示不同类型文档的分布情况和状态统计。',
      },
      databaseStats: {
        title: '数据库表统计',
        intro: '展示数据库相关的统计信息，包括表数量、字段统计等。',
      },
      chatStats: {
        title: 'AI 对话统计',
        intro: '提供AI对话系统的详细统计数据和趋势分析。',
      },
      complete: {
        title: '仪表板导览完成',
        intro: '仪表板导览结束！您现在已经了解了各个数据分析区域的功能。',
      },
    },
    
    // 控制按钮
    controls: {
      next: '下一步',
      prev: '上一步',
      skip: '跳过',
      done: '完成',
    },
  },
  
  // AI统计卡片
  aiStats: {
    totalConversations: '总对话量',
    avgSessionDuration: '平均会话时长',
    responsePerformance: '响应性能',
    questionHotness: '问题热度',
    
    // 标签和单位
    current: '当前周期',
    average: '平均',
    popular: '热门',
    minutes: '分钟',
    milliseconds: 'ms',
    categories: '类',
    activeUsers: '活跃用户',
    satisfaction: '满意度',
    completionRate: '完成率',
    coverage: '覆盖率',
  },
  
  // 知识库文档分析
  knowledgeAnalysis: {
    title: '知识库文档统计',
    documentDistribution: '文档分布',
  },
  
  // 文档分布
  docDistribution: {
    title: '文档分布',
    draft: '草稿文档',
    building: '构建中文档',
    complete: '已完成文档',
    noData: '暂无文档数据',
    loadFailed: '数据加载失败',
    tooltip: {
      documents: '文档数量',
      count: '篇',
      percentage: '占比',
    },
  },
  
  // 数据库表统计
  databaseStats: {
    title: '数据库表统计',
    dataTables: '数据表',
    totalFields: '字段总数',
    relationships: '表关系',
    dataSources: '数据源',
    avgFields: '平均每表',
    fieldsUnit: '个字段',
    
    // 关系类型
    relationshipTypes: {
      oneToOne: '一对一',
      oneToMany: '一对多',
      manyToOne: '多对一',
      foreignKey: '外键关系',
      uncategorized: '未分类关系',
    },
    
    tooltip: {
      relationships: '个关系',
    },
  },
  
  // AI对话统计
  chatStats: {
    title: '知识库对话统计',
    totalConversations: '总对话数',
    activeUsers: '活跃用户',
    avgSessionDuration: '平均会话时长',
    satisfactionRate: '满意度',
    
    // 图表
    chartTitle: '最近7天对话趋势',
    conversationCount: '对话数量',
    
    // 单位
    minutes: 'min',
    percentage: '%',
    
    // 周几
    weekdays: {
      monday: '周一',
      tuesday: '周二', 
      wednesday: '周三',
      thursday: '周四',
      friday: '周五',
      saturday: '周六',
      sunday: '周日',
    },
  },
  
  // 访问分析
  visitAnalysis: {
    title: '访问分析',
    visitors: '访问者',
    pageViews: '页面浏览量',
    bounceRate: '跳出率',
    avgVisitTime: '平均访问时长',
  },
  
  // 错误信息
  errors: {
    loadStatsFailed: '获取统计数据失败',
    loadChartFailed: '获取图表数据失败', 
    loadReportFailed: '获取综合报告失败',
    loadTrendFailed: '获取趋势数据失败',
    loadDistributionFailed: '获取文档分布数据失败',
  },
}; 
