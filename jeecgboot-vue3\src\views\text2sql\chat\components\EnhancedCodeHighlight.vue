<template>
  <div class="code-highlight-container" :class="{ 'with-actions': showActions }">
    <div class="code-actions" v-if="showActions">
      <a-button 
        type="text" 
        size="small"
        @click="copyCode"
        :title="copied ? '已复制' : '复制代码'"
      >
        <template #icon>
          <CopyOutlined v-if="!copied" />
          <CheckOutlined v-else />
        </template>
      </a-button>
      <a-button
        v-if="allowRun"
        type="text"
        size="small"
        @click="$emit('run')"
        :title="runText || '运行代码'"
      >
        <template #icon>
          <PlayCircleOutlined />
        </template>
      </a-button>
    </div>
    <div class="code-content" ref="codeContainer">
      <div class="line-numbers" v-if="showLineNumbers" ref="lineNumbersContainer"></div>
      <pre 
        ref="codeBlock" 
        :class="['language-' + language]"
        :style="{ maxHeight: maxHeight }"
      ><code>{{ trimmedCode }}</code></pre>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, nextTick, computed } from 'vue';
import Prism from 'prismjs';
// 不再导入Prism默认样式，以避免冲突
// import 'prismjs/themes/prism.css';
import 'prismjs/components/prism-sql';
import 'prismjs/components/prism-javascript';
import 'prismjs/components/prism-typescript';
import 'prismjs/components/prism-python';
import 'prismjs/components/prism-bash';
import 'prismjs/components/prism-json';
import { CopyOutlined, CheckOutlined, PlayCircleOutlined } from '@ant-design/icons-vue';

const props = defineProps({
  code: {
    type: String,
    required: true
  },
  language: {
    type: String,
    default: 'sql'
  },
  maxHeight: {
    type: String,
    default: '400px'
  },
  showActions: {
    type: Boolean,
    default: true
  },
  allowRun: {
    type: Boolean,
    default: false
  },
  runText: {
    type: String,
    default: '运行'
  },
  showLineNumbers: {
    type: Boolean,
    default: true
  },
  maxDisplayLines: {
    type: Number,
    default: 1000  // 默认最大显示1000行
  }
});

defineEmits(['run']);

const codeBlock = ref<HTMLElement | null>(null);
const codeContainer = ref<HTMLElement | null>(null);
const lineNumbersContainer = ref<HTMLElement | null>(null);
const copied = ref(false);

// 移除多余空行后的代码
const trimmedCode = computed(() => {
  if (!props.code) return '';
  
  // 分割成行
  const lines = props.code.split('\n');
  
  // 去除末尾空行
  let lastNonEmptyLine = lines.length - 1;
  while (lastNonEmptyLine >= 0 && lines[lastNonEmptyLine].trim() === '') {
    lastNonEmptyLine--;
  }
  
  // 只保留有效内容行
  return lines.slice(0, lastNonEmptyLine + 1).join('\n');
});

// 高亮代码并处理行号
const highlightCode = () => {
  nextTick(() => {
    if (codeBlock.value) {
      // 应用代码高亮
      Prism.highlightElement(codeBlock.value);
      
      // 渲染行号
      renderLineNumbers();
    }
  });
};

// 渲染行号
const renderLineNumbers = () => {
  // 确保lineNumbersContainer已加载
  if (!lineNumbersContainer.value) return;
  
  // 获取有效代码行数
  const linesCount = trimmedCode.value.split('\n').length;
  const maxLines = Math.min(linesCount, props.maxDisplayLines);
  
  // 清空现有行号
  lineNumbersContainer.value.innerHTML = '';
  
  // 创建新行号
  for (let i = 1; i <= maxLines; i++) {
    const lineNumber = document.createElement('div');
    lineNumber.className = 'line-number';
    lineNumber.textContent = String(i);
    lineNumbersContainer.value.appendChild(lineNumber);
  }
};

// 复制代码
const copyCode = async () => {
  try {
    // 复制处理后的代码
    await navigator.clipboard.writeText(trimmedCode.value);
    copied.value = true;
    setTimeout(() => {
      copied.value = false;
    }, 2000);
  } catch (err) {
    console.error('无法复制代码: ', err);
  }
};

// 监听代码变化重新高亮和渲染行号
watch(() => props.code, () => {
  highlightCode();
});

onMounted(() => {
  highlightCode();
});
</script>

<style>
/* 重置Prism.js的默认样式 */
:not(pre) > code[class*="language-"],
pre[class*="language-"] {
  background: transparent; /* 移除默认背景 */
  margin: 0;
  padding: 0;
  box-shadow: none;
  border: none;
  text-shadow: none;
}

.token {
  text-shadow: none !important;
  background: transparent;
  font-weight: normal;
}
</style>

<style scoped>
.code-highlight-container {
  position: relative;
  border-radius: 4px;
  margin: 0;
  overflow: hidden;
  background: #fafafa;
  color: #333333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.code-highlight-container.with-actions {
  padding-top: 32px;
}

.code-actions {
  position: absolute;
  top: 4px;
  right: 8px;
  z-index: 5;
  display: flex;
  gap: 4px;
}

.code-content {
  display: flex;
  overflow: hidden;
}

.line-numbers {
  user-select: none;
  text-align: right;
  padding: 16px 8px 16px 16px;
  color: #8e8e8e;
  background-color: #f0f0f0;
  border-right: 1px solid #e0e0e0;
  min-width: 40px;
}

.line-number {
  font-size: 13px;
  line-height: 1.5;
  font-family: 'Consolas', monospace;
  font-weight: normal;
  font-feature-settings: "liga" 0, "calt" 0;
  text-rendering: geometricPrecision;
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
}

pre {
  margin: 0;
  padding: 16px;
  overflow-x: auto;
  background-color: #fafafa !important;
  border-radius: 0;
  font-family: 'Consolas', monospace;
  font-size: 13px;
  line-height: 1.5;
  flex: 1;
  letter-spacing: normal;
  font-weight: normal;
  text-rendering: geometricPrecision;
  -webkit-font-smoothing: auto;
  -moz-osx-font-smoothing: auto;
  font-feature-settings: "liga" 0, "calt" 0;
}

code {
  text-shadow: none !important;
  font-family: 'Consolas', monospace;
  font-weight: normal;
  letter-spacing: normal;
  font-feature-settings: "liga" 0, "calt" 0;
  background: transparent !important;
}

/* 处理所有可能的token类型，确保没有重影 */
:deep(.token) {
  text-shadow: none !important;
  font-weight: normal;
  background: transparent !important;
}

/* 关键字：更鲜明的蓝色 */
:deep(.token.keyword) {
  color: #1a73e8;
  font-weight: bold;
  text-shadow: none !important;
}

/* 函数：暖橙色 */
:deep(.token.function) {
  color: #c2410c;
  text-shadow: none !important;
}

/* 字符串：柔和的红色 */
:deep(.token.string) {
  color: #e45649;
  text-shadow: none !important;
}

/* 数字：翠绿色 */
:deep(.token.number) {
  color: #11862f;
  text-shadow: none !important;
}

/* 注释：绿色 */
:deep(.token.comment) {
  color: #008000;
  font-style: italic;
  text-shadow: none !important;
}

/* 运算符：深灰色 */
:deep(.token.operator) {
  color: #494949;
  text-shadow: none !important;
}

/* 标点符号：灰色 */
:deep(.token.punctuation) {
  color: #7c7c7c;
  text-shadow: none !important;
}

/* 属性名和常量：深蓝色 */
:deep(.token.property),
:deep(.token.constant) {
  color: #0550ae;
  text-shadow: none !important;
}

/* 内置函数：紫蓝色 */
:deep(.token.builtin) {
  color: #4b21b0;
  text-shadow: none !important;
}

:deep(.token.atrule),
:deep(.token.attr-value) {
  color: #e45649;
  text-shadow: none !important;
}

/* SQL专用样式 */
:deep(.language-sql) {
  text-shadow: none !important;
  background: transparent !important;
}

:deep(.language-sql .token) {
  text-shadow: none !important;
  font-weight: normal;
  background: transparent !important;
}

/* SQL关键字：蓝色 */
:deep(.language-sql .token.keyword) {
  color: #1a73e8;
  font-weight: bold;
  text-shadow: none !important;
}

/* SQL运算符：紫红色 */
:deep(.language-sql .token.operator) {
  color: #7928ca;
  font-weight: bold;
  text-shadow: none !important;
}

/* SQL函数：橙色 */
:deep(.language-sql .token.function) {
  color: #c2410c;
  text-shadow: none !important;
}

/* SQL数字：翠绿色 */
:deep(.language-sql .token.number) {
  color: #11862f;
  text-shadow: none !important;
}

/* SQL字符串：红色 */
:deep(.language-sql .token.string) {
  color: #e45649;
  text-shadow: none !important;
}

/* SQL标点符号：灰色 */
:deep(.language-sql .token.punctuation) {
  color: #7c7c7c;
  text-shadow: none !important;
}

/* SQL变量：深蓝色 */
:deep(.language-sql .token.variable) {
  color: #0550ae;
  text-shadow: none !important;
}

/* SQL常量：亮蓝色 */
:deep(.language-sql .token.constant) {
  color: #0086b3;
  text-shadow: none !important;
}

/* 特别处理SQL的表名和字段名 */
:deep(.language-sql .token.column-name),
:deep(.language-sql .token.table-name),
:deep(.language-sql .token.identifier),
:deep(.language-sql .token.entity),
:deep(.language-sql .token.field) {
  color: #7d5bbf;
  text-shadow: none !important;
  font-weight: normal;
}

/* NULL, TRUE, FALSE等特殊值 */
:deep(.language-sql .token.null),
:deep(.language-sql .token.boolean) {
  color: #e76f51;
  font-weight: bold;
  text-shadow: none !important;
}

/* 为COUNT, SUM等聚合函数添加特殊样式 */
:deep(.language-sql .token.function[class*="count"]),
:deep(.language-sql .token.function[class*="sum"]),
:deep(.language-sql .token.function[class*="avg"]),
:deep(.language-sql .token.function[class*="min"]),
:deep(.language-sql .token.function[class*="max"]) {
  color: #c2410c;
  font-weight: bold;
  text-shadow: none !important;
}

/* AS关键字 */
:deep(.language-sql .token.keyword[class*="as"]) {
  color: #1a73e8;
  font-style: italic;
  text-shadow: none !important;
}

/* Prism可能将普通字段名标记为普通文本 */
:deep(.language-sql span:not([class])),
:deep(.language-sql .token.plain-text) {
  color: #7d5bbf;
  text-shadow: none !important;
  font-weight: normal;
}

/* 暗黑模式样式 */
[data-theme='dark'] {
  .code-highlight-container {
    background: #1a1a1a;
    color: rgba(255, 255, 255, 0.85);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    border-color: #404040;
  }

  .line-numbers {
    color: rgba(255, 255, 255, 0.45);
    background-color: #262626;
    border-right-color: #404040;
  }

  pre {
    background-color: #1a1a1a !important;
    color: rgba(255, 255, 255, 0.85);
  }

  .action-btn {
    background: #262626;
    border-color: #404040;
    color: rgba(255, 255, 255, 0.65);

    &:hover {
      background: #303030;
      border-color: #1890ff;
      color: #1890ff;
    }
  }

  /* 暗黑模式下的代码高亮颜色 */
  :deep(.language-sql .token.keyword) {
    color: #569cd6;
  }

  :deep(.language-sql .token.string) {
    color: #ce9178;
  }

  :deep(.language-sql .token.number) {
    color: #b5cea8;
  }

  :deep(.language-sql .token.operator) {
    color: #d4d4d4;
  }

  :deep(.language-sql .token.punctuation) {
    color: #d4d4d4;
  }

  :deep(.language-sql .token.comment) {
    color: #6a9955;
  }

  :deep(.language-sql .token.function) {
    color: #dcdcaa;
  }

  :deep(.language-sql .token.table-name) {
    color: #4ec9b0;
  }

  :deep(.language-sql .token.column-name) {
    color: #9cdcfe;
  }

  :deep(.language-sql .token.variable) {
    color: #4fc1ff;
  }

  :deep(.language-sql .token.builtin) {
    color: #569cd6;
  }

  :deep(.language-sql .token.boolean) {
    color: #569cd6;
  }

  :deep(.language-sql .token.null) {
    color: #569cd6;
  }

  :deep(.language-sql .token.important) {
    color: #ff6b6b;
  }

  :deep(.language-sql .token.atrule) {
    color: #c586c0;
  }

  :deep(.language-sql .token.attr-name) {
    color: #92c5f8;
  }

  :deep(.language-sql .token.attr-value) {
    color: #ce9178;
  }

  :deep(.language-sql .token.url) {
    color: #ce9178;
  }

  :deep(.language-sql .token.selector) {
    color: #d7ba7d;
  }

  :deep(.language-sql .token.property) {
    color: #92c5f8;
  }

  :deep(.language-sql .token.regex) {
    color: #d16969;
  }

  :deep(.language-sql .token.entity) {
    color: #dcdcaa;
  }

  :deep(.language-sql .token.directive.tag) {
    color: #569cd6;
  }

  :deep(.language-sql .token.doctype) {
    color: #6a9955;
  }

  :deep(.language-sql .token.cdata) {
    color: #6a9955;
  }

  :deep(.language-sql .token.namespace) {
    color: #4ec9b0;
  }

  :deep(.language-sql .token.prolog) {
    color: #6a9955;
  }

  :deep(.language-sql .token.tag) {
    color: #569cd6;
  }

  :deep(.language-sql .token.class-name) {
    color: #4ec9b0;
  }

  :deep(.language-sql .token.symbol) {
    color: #b5cea8;
  }

  :deep(.language-sql .token.deleted) {
    color: #ff6b6b;
  }

  :deep(.language-sql .token.inserted) {
    color: #b5cea8;
  }

  :deep(.language-sql .token.constant) {
    color: #4fc1ff;
  }

  :deep(.language-sql span:not([class])),
  :deep(.language-sql .token.plain-text) {
    color: #9cdcfe;
  }
}
</style>