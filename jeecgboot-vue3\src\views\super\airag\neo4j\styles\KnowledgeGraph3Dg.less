// 3D知识图谱 - 实体弹出框样式
@import './common.less';

.entity-popup {
  .entity-detail-base();
  position: fixed; // 3D图使用固定定位
  
  // v-show动画效果
  &[style*="display: none"] {
    opacity: 0;
    transform: scale(0.95);
  }
  
  // 3D特有的标签页样式
  .ant-tabs {
    flex: 1;
    overflow-y: auto;
    
    .ant-tabs-content {
      padding: 0 12px 12px;
    }
    
    .ant-tabs-tab-btn {
      transition: color @animation-duration ease;
      
      &:hover {
        color: @primary-color;
      }
    }
  }

  // 应用节点关系样式
  .node-relations-style();
}

// ========== 暗黑模式适配 ==========
[data-theme='dark'] {
  .entity-popup {
    .entity-detail-base();
    position: fixed; // 3D图使用固定定位

    // 确保在暗黑模式下正确显示
    background: @dark-bg-translucent !important;
    border-color: @dark-border-color !important;
    color: @dark-text-color !important;
    box-shadow: @dark-shadow-light !important;

    // v-show动画效果
    &[style*="display: none"] {
      opacity: 0;
      transform: scale(0.95);
    }

    // 3D特有的标签页样式
    .ant-tabs {
      .ant-tabs-nav {
        background: @dark-bg-secondary !important;

        .ant-tabs-tab {
          color: @dark-text-color-secondary !important;

          &.ant-tabs-tab-active {
            color: @primary-color !important;
          }

          &:hover {
            color: @primary-color !important;
          }
        }

        .ant-tabs-ink-bar {
          background: @primary-color !important;
        }
      }

      .ant-tabs-content {
        background: @dark-bg-primary !important;
        color: @dark-text-color !important;

        .ant-tabs-tabpane {
          color: @dark-text-color !important;
        }
      }

      .ant-tabs-tab-btn {
        &:hover {
          color: @primary-color !important;
        }
      }
    }

    // 应用节点关系样式
    .node-relations-style();

    // 暗黑模式下的关系项样式
    .node-relations {
      .relation-item {
        background: @dark-bg-secondary !important;
        border-color: @dark-border-color !important;
        color: @dark-text-color !important;

        &:hover {
          background: @dark-bg-tertiary !important;
          border-color: @primary-color !important;
        }
      }
    }

    // 暗黑模式下的描述列表样式
    .ant-descriptions {
      .ant-descriptions-item-label {
        color: @dark-text-color-secondary !important;
      }

      .ant-descriptions-item-content {
        color: @dark-text-color !important;
      }
    }

    // 暗黑模式下的头部样式
    .entity-detail-header {
      background: @dark-bg-secondary !important;
      border-bottom-color: @dark-border-color !important;

      .node-title h3 {
        color: @dark-text-color !important;
      }

      .ant-btn {
        color: @dark-text-color-light !important;

        &:hover {
          color: @dark-text-color !important;
          background: rgba(255, 255, 255, 0.1) !important;
        }
      }
    }

    // 暗黑模式下的内容区域样式
    .entity-detail-content {
      background: @dark-bg-primary !important;
    }
  }
}